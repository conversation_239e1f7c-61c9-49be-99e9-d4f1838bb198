const faker = require('faker');
const { v4: uuidv4 } = require('uuid');
const config = require('../config/test.config');

class RoleFactory {
  /**
   * Create custom role data
   */
  static createCustomRole() {
    const timestamp = Date.now();
    
    return {
      name: `${config.testData.rolePrefix || 'test.role'}.custom.${timestamp}`,
      description: `Custom role created for testing purposes - ${timestamp}`,
      permissions: [
        'properties.read',
        'maintenance.read',
        'dashboard.view',
      ],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create site coordinator role
   */
  static createSiteCoordinator() {
    const timestamp = Date.now();
    
    return {
      name: `Site Coordinator ${timestamp}`,
      description: 'Coordinates site activities and reports',
      permissions: [
        'properties.read',
        'maintenance.read',
        'maintenance.create',
        'attendance.read',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create facilities manager role
   */
  static createFacilitiesManager() {
    const timestamp = Date.now();
    
    return {
      name: `Facilities Manager ${timestamp}`,
      description: 'Manages facility operations and maintenance',
      permissions: [
        'properties.read',
        'properties.update',
        'maintenance.read',
        'maintenance.create',
        'maintenance.update',
        'maintenance.assign',
        'fuel.read',
        'fuel.update',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create security supervisor role
   */
  static createSecuritySupervisor() {
    const timestamp = Date.now();
    
    return {
      name: `Security Supervisor ${timestamp}`,
      description: 'Supervises security operations',
      permissions: [
        'security.read',
        'security.create',
        'security.update',
        'incidents.manage',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create escalation manager role
   */
  static createEscalationRole() {
    const timestamp = Date.now();
    
    return {
      name: `Escalation Manager ${timestamp}`,
      description: 'Role for testing escalation workflows',
      permissions: [
        'maintenance.read',
        'maintenance.escalate',
        'maintenance.assign',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create reporting specialist role
   */
  static createReportingRole() {
    const timestamp = Date.now();
    
    return {
      name: `Reporting Specialist ${timestamp}`,
      description: 'Role for testing reporting functionality',
      permissions: [
        'properties.read',
        'maintenance.read',
        'attendance.read',
        'fuel.read',
        'reports.generate',
        'reports.export',
        'dashboard.view',
      ],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create role with minimal permissions
   */
  static createMinimalRole() {
    const timestamp = Date.now();
    
    return {
      name: `Minimal Role ${timestamp}`,
      description: 'Role with minimal permissions for testing',
      permissions: ['dashboard.view'],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create role with maximum permissions (excluding admin)
   */
  static createMaximalRole() {
    const timestamp = Date.now();
    
    return {
      name: `Maximal Role ${timestamp}`,
      description: 'Role with maximum non-admin permissions',
      permissions: [
        'properties.read',
        'properties.create',
        'properties.update',
        'maintenance.read',
        'maintenance.create',
        'maintenance.update',
        'maintenance.assign',
        'maintenance.escalate',
        'attendance.read',
        'attendance.create',
        'attendance.update',
        'fuel.read',
        'fuel.create',
        'fuel.update',
        'security.read',
        'security.create',
        'reports.generate',
        'reports.export',
        'dashboard.view',
        'thresholds.configure',
      ],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create role for permission testing
   */
  static createForPermissionTesting() {
    const timestamp = Date.now();
    
    return {
      name: `Permission Test Role ${timestamp}`,
      description: 'Role for testing permission assignments',
      permissions: [
        'properties.read',
        'maintenance.read',
      ],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create role with invalid data for validation testing
   */
  static createInvalid() {
    return {
      name: '', // Invalid: empty name
      description: 'Invalid role for testing validation',
      permissions: [],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create role with duplicate name for testing
   */
  static createDuplicate(existingName) {
    return {
      name: existingName,
      description: 'Duplicate role for testing',
      permissions: ['dashboard.view'],
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create multiple roles for bulk operations
   */
  static createBulkRoles(count) {
    const roles = [];
    
    for (let i = 0; i < count; i++) {
      const timestamp = Date.now() + i;
      
      roles.push({
        name: `Bulk Role ${i + 1} ${timestamp}`,
        description: `Bulk role ${i + 1} for testing`,
        permissions: [
          'dashboard.view',
          'properties.read',
        ],
        isSystemRole: false,
        isActive: true
      });
    }
    
    return roles;
  }

  /**
   * Create role update data
   */
  static createUpdateData() {
    return {
      description: `Updated role description ${Date.now()}`,
      permissions: [
        'properties.read',
        'maintenance.read',
        'reports.generate',
        'dashboard.view',
      ]
    };
  }

  /**
   * Create role with specific permissions
   */
  static createWithPermissions(permissions) {
    const timestamp = Date.now();
    
    return {
      name: `Custom Permissions Role ${timestamp}`,
      description: 'Role with specific permissions for testing',
      permissions: permissions,
      isSystemRole: false,
      isActive: true
    };
  }

  /**
   * Create role for specific test scenario
   */
  static createForScenario(scenario) {
    const timestamp = Date.now();
    
    const baseRole = {
      name: `${scenario} Test Role ${timestamp}`,
      description: `Role created for ${scenario} testing`,
      isSystemRole: false,
      isActive: true
    };

    switch (scenario.toLowerCase()) {
      case 'escalation_testing':
        return {
          ...baseRole,
          permissions: [
            'maintenance.read',
            'maintenance.escalate',
            'maintenance.assign',
            'dashboard.view'
          ]
        };
      case 'reporting_testing':
        return {
          ...baseRole,
          permissions: [
            'properties.read',
            'maintenance.read',
            'reports.generate',
            'reports.export',
            'dashboard.view'
          ]
        };
      case 'security_testing':
        return {
          ...baseRole,
          permissions: [
            'security.read',
            'security.create',
            'incidents.manage',
            'dashboard.view'
          ]
        };
      case 'permission_testing':
        return {
          ...baseRole,
          permissions: [
            'properties.read',
            'maintenance.read'
          ]
        };
      default:
        return {
          ...baseRole,
          permissions: ['dashboard.view']
        };
    }
  }

  /**
   * Generate random role data
   */
  static generateRandom() {
    const timestamp = Date.now();
    const allPermissions = [
      'properties.read', 'properties.create', 'properties.update',
      'maintenance.read', 'maintenance.create', 'maintenance.update',
      'attendance.read', 'attendance.create',
      'fuel.read', 'fuel.create',
      'reports.generate', 'dashboard.view'
    ];
    
    // Select random subset of permissions
    const numPermissions = Math.floor(Math.random() * 5) + 1;
    const selectedPermissions = [];
    for (let i = 0; i < numPermissions; i++) {
      const randomPermission = allPermissions[Math.floor(Math.random() * allPermissions.length)];
      if (!selectedPermissions.includes(randomPermission)) {
        selectedPermissions.push(randomPermission);
      }
    }
    
    return {
      name: `Random Role ${timestamp}`,
      description: faker.lorem.sentence(),
      permissions: selectedPermissions,
      isSystemRole: false,
      isActive: faker.datatype.boolean()
    };
  }
}

class PermissionFactory {
  /**
   * Create custom permission data
   */
  static createCustomPermission() {
    const timestamp = Date.now();
    
    return {
      name: `test.permission.${timestamp}`,
      description: `Custom permission for testing - ${timestamp}`,
      category: 'testing',
      isActive: true
    };
  }

  /**
   * Get all standard permissions
   */
  static getAllStandardPermissions() {
    return [
      // Properties permissions
      { name: 'properties.read', description: 'View properties', category: 'properties' },
      { name: 'properties.create', description: 'Create new properties', category: 'properties' },
      { name: 'properties.update', description: 'Update property details', category: 'properties' },
      { name: 'properties.delete', description: 'Delete properties', category: 'properties' },
      
      // Maintenance permissions
      { name: 'maintenance.read', description: 'View maintenance issues', category: 'maintenance' },
      { name: 'maintenance.create', description: 'Create maintenance issues', category: 'maintenance' },
      { name: 'maintenance.update', description: 'Update maintenance issues', category: 'maintenance' },
      { name: 'maintenance.assign', description: 'Assign maintenance issues', category: 'maintenance' },
      { name: 'maintenance.escalate', description: 'Escalate maintenance issues', category: 'maintenance' },
      
      // Attendance permissions
      { name: 'attendance.read', description: 'View attendance records', category: 'attendance' },
      { name: 'attendance.create', description: 'Create attendance records', category: 'attendance' },
      { name: 'attendance.update', description: 'Update attendance records', category: 'attendance' },
      
      // Fuel permissions
      { name: 'fuel.read', description: 'View fuel records', category: 'fuel' },
      { name: 'fuel.create', description: 'Create fuel records', category: 'fuel' },
      { name: 'fuel.update', description: 'Update fuel records', category: 'fuel' },
      
      // Security permissions
      { name: 'security.read', description: 'View security records', category: 'security' },
      { name: 'security.create', description: 'Create security records', category: 'security' },
      { name: 'security.update', description: 'Update security records', category: 'security' },
      
      // Admin permissions
      { name: 'users.create', description: 'Create new users', category: 'admin' },
      { name: 'users.update', description: 'Update user details', category: 'admin' },
      { name: 'users.delete', description: 'Delete users', category: 'admin' },
      { name: 'roles.create', description: 'Create new roles', category: 'admin' },
      { name: 'roles.update', description: 'Update role details', category: 'admin' },
      { name: 'permissions.configure', description: 'Configure permissions', category: 'admin' },
      
      // General permissions
      { name: 'dashboard.view', description: 'View dashboard', category: 'general' },
      { name: 'reports.generate', description: 'Generate reports', category: 'reports' },
      { name: 'reports.export', description: 'Export reports', category: 'reports' },
      { name: 'thresholds.configure', description: 'Configure thresholds', category: 'settings' },
    ];
  }

  /**
   * Get permissions by category
   */
  static getPermissionsByCategory(category) {
    return this.getAllStandardPermissions().filter(perm => perm.category === category);
  }

  /**
   * Get property permissions
   */
  static getPropertyPermissions() {
    return this.getPermissionsByCategory('properties');
  }

  /**
   * Get maintenance permissions
   */
  static getMaintenancePermissions() {
    return this.getPermissionsByCategory('maintenance');
  }

  /**
   * Get admin permissions
   */
  static getAdminPermissions() {
    return this.getPermissionsByCategory('admin');
  }
}

module.exports = { RoleFactory, PermissionFactory };
