const AuthHelper = require('../src/helpers/auth_helper');
const UserFactory = require('../src/factories/user_factory');
const config = require('../src/config/test.config');

/**
 * Setup test environment for SRSR Property Management testing
 * This script creates necessary test data and verifies system readiness
 */

class TestEnvironmentSetup {
  constructor() {
    this.createdUsers = [];
    this.createdRoles = [];
    this.createdProperties = [];
  }

  async run() {
    console.log('🔧 Setting up SRSR Property Management test environment...');
    console.log('='.repeat(60));

    try {
      // Step 1: Verify backend connectivity
      await this.verifyBackendConnectivity();

      // Step 2: Verify admin authentication
      await this.verifyAdminAuthentication();

      // Step 3: Create test users
      await this.createTestUsers();

      // Step 4: Create test properties
      await this.createTestProperties();

      // Step 5: Verify test data
      await this.verifyTestData();

      console.log('');
      console.log('✅ Test environment setup completed successfully!');
      console.log('='.repeat(60));
      
      this.printSummary();

    } catch (error) {
      console.error('❌ Test environment setup failed:', error.message);
      console.error('Stack trace:', error.stack);
      
      // Attempt cleanup
      await this.cleanup();
      
      process.exit(1);
    }
  }

  async verifyBackendConnectivity() {
    console.log('🔍 Verifying backend connectivity...');
    
    try {
      const response = await fetch(`${config.apiBaseUrl.replace('/api', '')}/health`);
      if (response.ok) {
        console.log('✅ Backend is responding');
      } else {
        throw new Error(`Backend health check failed: ${response.status}`);
      }
    } catch (error) {
      // Try alternative endpoint
      try {
        const response = await fetch(config.apiBaseUrl);
        if (response.status < 500) {
          console.log('✅ Backend API is accessible');
        } else {
          throw error;
        }
      } catch (fallbackError) {
        throw new Error(`Backend not accessible at ${config.apiBaseUrl}: ${fallbackError.message}`);
      }
    }
  }

  async verifyAdminAuthentication() {
    console.log('🔐 Verifying admin authentication...');
    
    try {
      const adminToken = await AuthHelper.getAdminToken();
      
      // Verify token works
      const userInfo = await AuthHelper.getUserInfo(adminToken);
      
      if (userInfo && userInfo.data && userInfo.data.roles && userInfo.data.roles.includes('admin')) {
        console.log('✅ Admin authentication successful');
        console.log(`   Admin user: ${userInfo.data.email}`);
      } else {
        throw new Error('Admin user does not have admin role');
      }
    } catch (error) {
      throw new Error(`Admin authentication failed: ${error.message}`);
    }
  }

  async createTestUsers() {
    console.log('👥 Creating test users...');
    
    const adminToken = await AuthHelper.getAdminToken();
    
    const testUserConfigs = [
      {
        factory: () => UserFactory.createPropertyManager(),
        description: 'Property Manager'
      },
      {
        factory: () => UserFactory.createMaintenanceStaff(),
        description: 'Maintenance Staff'
      },
      {
        factory: () => UserFactory.createSecurityGuard(),
        description: 'Security Guard'
      },
      {
        factory: () => UserFactory.createViewer(),
        description: 'Viewer'
      }
    ];

    for (const userConfig of testUserConfigs) {
      try {
        const userData = userConfig.factory();
        
        // Check if user already exists
        const existingUserResponse = await AuthHelper.makeAuthenticatedRequest(
          adminToken,
          'GET',
          `/users?search=${userData.email}`
        );

        if (existingUserResponse.data.data && existingUserResponse.data.data.length > 0) {
          console.log(`   ℹ️ ${userConfig.description} user already exists: ${userData.email}`);
          continue;
        }

        // Create user
        const createResponse = await AuthHelper.makeAuthenticatedRequest(
          adminToken,
          'POST',
          '/users',
          userData
        );

        if (createResponse.status === 201) {
          this.createdUsers.push(createResponse.data.data);
          console.log(`   ✅ Created ${userConfig.description}: ${userData.email}`);
          
          // Verify user can login
          try {
            await AuthHelper.loginWithCredentials(userData.email, userData.password);
            console.log(`   ✅ Login verified for: ${userData.email}`);
          } catch (loginError) {
            console.warn(`   ⚠️ Login verification failed for ${userData.email}: ${loginError.message}`);
          }
        } else {
          console.warn(`   ⚠️ Failed to create ${userConfig.description}: ${createResponse.status}`);
        }
      } catch (error) {
        console.warn(`   ⚠️ Error creating ${userConfig.description}: ${error.message}`);
      }
    }
  }

  async createTestProperties() {
    console.log('🏢 Creating test properties...');
    
    const adminToken = await AuthHelper.getAdminToken();
    
    const testProperties = [
      {
        name: 'Test Residential Property',
        type: 'residential',
        address: '123 Test Residential Street',
        city: 'Test City',
        state: 'Test State',
        zipCode: '12345',
        isActive: true,
        description: 'Test residential property for automation testing'
      },
      {
        name: 'Test Office Property',
        type: 'office',
        address: '456 Test Business Avenue',
        city: 'Test City',
        state: 'Test State',
        zipCode: '12345',
        isActive: true,
        description: 'Test office property for automation testing'
      },
      {
        name: 'Test Construction Site',
        type: 'construction',
        address: '789 Test Construction Road',
        city: 'Test City',
        state: 'Test State',
        zipCode: '12345',
        isActive: true,
        description: 'Test construction site for automation testing'
      }
    ];

    for (const propertyData of testProperties) {
      try {
        // Check if property already exists
        const existingPropertyResponse = await AuthHelper.makeAuthenticatedRequest(
          adminToken,
          'GET',
          `/properties?search=${propertyData.name}`
        );

        if (existingPropertyResponse.data.data && existingPropertyResponse.data.data.length > 0) {
          console.log(`   ℹ️ Property already exists: ${propertyData.name}`);
          continue;
        }

        // Create property
        const createResponse = await AuthHelper.makeAuthenticatedRequest(
          adminToken,
          'POST',
          '/properties',
          propertyData
        );

        if (createResponse.status === 201) {
          this.createdProperties.push(createResponse.data.data);
          console.log(`   ✅ Created property: ${propertyData.name}`);
        } else {
          console.warn(`   ⚠️ Failed to create property ${propertyData.name}: ${createResponse.status}`);
        }
      } catch (error) {
        console.warn(`   ⚠️ Error creating property ${propertyData.name}: ${error.message}`);
      }
    }
  }

  async verifyTestData() {
    console.log('🔍 Verifying test data...');
    
    const adminToken = await AuthHelper.getAdminToken();
    
    try {
      // Verify users
      const usersResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/users'
      );
      
      const totalUsers = usersResponse.data.data ? usersResponse.data.data.length : 0;
      console.log(`   ✅ Total users in system: ${totalUsers}`);

      // Verify properties
      const propertiesResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/properties'
      );
      
      const totalProperties = propertiesResponse.data.data ? propertiesResponse.data.data.length : 0;
      console.log(`   ✅ Total properties in system: ${totalProperties}`);

      // Verify roles
      const rolesResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/roles'
      );
      
      const totalRoles = rolesResponse.data.data ? rolesResponse.data.data.length : 0;
      console.log(`   ✅ Total roles in system: ${totalRoles}`);

    } catch (error) {
      console.warn(`   ⚠️ Error verifying test data: ${error.message}`);
    }
  }

  async cleanup() {
    console.log('🧹 Cleaning up after failed setup...');
    
    try {
      const adminToken = await AuthHelper.getAdminToken();
      
      // Cleanup created users
      for (const user of this.createdUsers) {
        try {
          await AuthHelper.makeAuthenticatedRequest(
            adminToken,
            'DELETE',
            `/users/${user.id}`
          );
          console.log(`   ✅ Cleaned up user: ${user.email}`);
        } catch (error) {
          console.warn(`   ⚠️ Failed to cleanup user ${user.email}: ${error.message}`);
        }
      }

      // Cleanup created properties
      for (const property of this.createdProperties) {
        try {
          await AuthHelper.makeAuthenticatedRequest(
            adminToken,
            'DELETE',
            `/properties/${property.id}`
          );
          console.log(`   ✅ Cleaned up property: ${property.name}`);
        } catch (error) {
          console.warn(`   ⚠️ Failed to cleanup property ${property.name}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('Failed to cleanup:', error.message);
    }
  }

  printSummary() {
    console.log('📊 Test Environment Summary:');
    console.log(`   • Backend URL: ${config.apiBaseUrl}`);
    console.log(`   • Environment: ${config.currentEnv}`);
    console.log(`   • Created Users: ${this.createdUsers.length}`);
    console.log(`   • Created Properties: ${this.createdProperties.length}`);
    console.log('');
    console.log('🎯 Ready for admin dashboard testing!');
    console.log('   Focus areas:');
    console.log('   • User Management');
    console.log('   • Role Management');
    console.log('   • Permission Configuration');
    console.log('   • Screen Management');
    console.log('   • Widget Management');
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new TestEnvironmentSetup();
  setup.run().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

module.exports = TestEnvironmentSetup;
