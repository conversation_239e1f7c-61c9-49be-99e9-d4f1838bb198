import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../models/user_data.dart';

class AttendancePage {
  final WidgetTester tester;
  static final _logger = Logger();

  AttendancePage(this.tester);

  // Main screen locators
  Finder get screen => find.byKey(const Key('attendance_screen'));
  Finder get attendanceList => find.byKey(const Key('attendance_list'));
  Finder get addAttendanceFab => find.byKey(const Key('add_attendance_fab'));
  Finder get refreshButton => find.byKey(const Key('refresh_button'));
  
  // Tab locators
  Finder get allRecordsTab => find.text('All Records');
  Finder get siteAttendanceTab => find.text('Site Attendance');
  Finder get officeAttendanceTab => find.text('Office Attendance');
  
  // Filter and search locators
  Finder get dateFilterButton => find.byKey(const Key('date_filter_button'));
  Finder get propertyFilterDropdown => find.byKey(const Key('property_filter_dropdown'));
  Finder get statusFilterDropdown => find.byKey(const Key('status_filter_dropdown'));
  Finder get searchField => find.byKey(const Key('search_field'));
  
  // Attendance record locators
  Finder attendanceRecord(String employeeName) => find.byKey(Key('attendance_record_$employeeName'));
  Finder editAttendanceButton(String employeeName) => find.byKey(Key('edit_attendance_$employeeName'));
  Finder deleteAttendanceButton(String employeeName) => find.byKey(Key('delete_attendance_$employeeName'));
  
  // Add attendance dialog locators
  Finder get addAttendanceDialog => find.byKey(const Key('add_attendance_dialog'));
  Finder get employeeDropdown => find.byKey(const Key('employee_dropdown'));
  Finder get propertyDropdown => find.byKey(const Key('property_dropdown'));
  Finder get attendanceDateField => find.byKey(const Key('attendance_date_field'));
  Finder get checkInTimeField => find.byKey(const Key('check_in_time_field'));
  Finder get checkOutTimeField => find.byKey(const Key('check_out_time_field'));
  Finder get attendanceStatusDropdown => find.byKey(const Key('attendance_status_dropdown'));
  Finder get notesField => find.byKey(const Key('notes_field'));
  Finder get saveAttendanceButton => find.text('Save Attendance');
  Finder get cancelButton => find.text('Cancel');
  
  // Bulk operations locators
  Finder get bulkActionsButton => find.byKey(const Key('bulk_actions_button'));
  Finder get selectAllCheckbox => find.byKey(const Key('select_all_checkbox'));
  Finder get markPresentButton => find.text('Mark Present');
  Finder get markAbsentButton => find.text('Mark Absent');
  Finder get exportButton => find.byKey(const Key('export_button'));
  
  // Quick actions locators
  Finder get quickCheckInButton => find.byKey(const Key('quick_check_in_button'));
  Finder get quickCheckOutButton => find.byKey(const Key('quick_check_out_button'));
  Finder get todayAttendanceButton => find.byKey(const Key('today_attendance_button'));
  
  // Statistics locators
  Finder get attendanceStatsCard => find.byKey(const Key('attendance_stats_card'));
  Finder get presentCountText => find.byKey(const Key('present_count_text'));
  Finder get absentCountText => find.byKey(const Key('absent_count_text'));
  Finder get lateCountText => find.byKey(const Key('late_count_text'));
  
  // Verification locators
  Finder get successMessage => find.byKey(const Key('success_message'));
  Finder get errorMessage => find.byKey(const Key('error_message'));
  Finder get noAttendanceMessage => find.text('No attendance records found');

  /// Navigate to attendance screen
  Future<void> navigateToAttendance() async {
    _logger.i('Navigating to attendance screen');
    
    final attendanceNavItem = find.text('Attendance');
    if (attendanceNavItem.evaluate().isNotEmpty) {
      await tester.tap(attendanceNavItem);
      await tester.pumpAndSettle();
    }
    
    await _verifyScreenLoaded();
  }

  /// Switch to all records tab
  Future<void> switchToAllRecordsTab() async {
    _logger.d('Switching to all records tab');
    
    if (allRecordsTab.evaluate().isNotEmpty) {
      await tester.tap(allRecordsTab);
      await tester.pumpAndSettle();
    }
  }

  /// Switch to site attendance tab
  Future<void> switchToSiteAttendanceTab() async {
    _logger.d('Switching to site attendance tab');
    
    if (siteAttendanceTab.evaluate().isNotEmpty) {
      await tester.tap(siteAttendanceTab);
      await tester.pumpAndSettle();
    }
  }

  /// Switch to office attendance tab
  Future<void> switchToOfficeAttendanceTab() async {
    _logger.d('Switching to office attendance tab');
    
    if (officeAttendanceTab.evaluate().isNotEmpty) {
      await tester.tap(officeAttendanceTab);
      await tester.pumpAndSettle();
    }
  }

  /// Create a new attendance record
  Future<void> createAttendanceRecord(AttendanceData attendanceData) async {
    _logger.i('Creating attendance record for: ${attendanceData.employeeName}');
    
    await _openAddAttendanceDialog();
    await _fillAttendanceForm(attendanceData);
    await _submitAttendanceForm();
    await _verifyAttendanceCreated(attendanceData.employeeName);
  }

  /// Open add attendance dialog
  Future<void> _openAddAttendanceDialog() async {
    _logger.d('Opening add attendance dialog');
    
    expect(addAttendanceFab, findsOneWidget, reason: 'Add attendance FAB should be present');
    await tester.tap(addAttendanceFab);
    await tester.pumpAndSettle();
    
    expect(addAttendanceDialog, findsOneWidget, reason: 'Add attendance dialog should open');
  }

  /// Fill attendance creation form
  Future<void> _fillAttendanceForm(AttendanceData attendanceData) async {
    _logger.d('Filling attendance form for: ${attendanceData.employeeName}');
    
    // Select employee
    await tester.tap(employeeDropdown);
    await tester.pumpAndSettle();
    final employeeOption = find.text(attendanceData.employeeName);
    if (employeeOption.evaluate().isNotEmpty) {
      await tester.tap(employeeOption);
      await tester.pumpAndSettle();
    }
    
    // Select property
    await tester.tap(propertyDropdown);
    await tester.pumpAndSettle();
    final propertyOption = find.text(attendanceData.propertyName);
    if (propertyOption.evaluate().isNotEmpty) {
      await tester.tap(propertyOption);
      await tester.pumpAndSettle();
    }
    
    // Set date
    await tester.enterText(attendanceDateField, attendanceData.date);
    
    // Set check-in time
    await tester.enterText(checkInTimeField, attendanceData.checkInTime);
    
    // Set check-out time if provided
    if (attendanceData.checkOutTime.isNotEmpty) {
      await tester.enterText(checkOutTimeField, attendanceData.checkOutTime);
    }
    
    // Select status
    await tester.tap(attendanceStatusDropdown);
    await tester.pumpAndSettle();
    final statusOption = find.text(attendanceData.status);
    if (statusOption.evaluate().isNotEmpty) {
      await tester.tap(statusOption);
      await tester.pumpAndSettle();
    }
    
    // Add notes if provided
    if (attendanceData.notes.isNotEmpty) {
      await tester.enterText(notesField, attendanceData.notes);
    }
    
    await tester.pumpAndSettle();
  }

  /// Submit attendance creation form
  Future<void> _submitAttendanceForm() async {
    _logger.d('Submitting attendance form');
    
    expect(saveAttendanceButton, findsOneWidget, reason: 'Save attendance button should be present');
    await tester.tap(saveAttendanceButton);
    await tester.pumpAndSettle();
    
    // Wait for form processing
    await tester.pump(const Duration(seconds: 2));
    await tester.pumpAndSettle();
  }

  /// Verify attendance was created successfully
  Future<void> _verifyAttendanceCreated(String employeeName) async {
    _logger.d('Verifying attendance created for: $employeeName');
    
    // Check for success message
    expect(successMessage, findsOneWidget, reason: 'Success message should appear');
    
    // Verify attendance appears in list
    await verifyAttendanceExists(employeeName);
  }

  /// Verify attendance exists in the list
  Future<void> verifyAttendanceExists(String employeeName) async {
    _logger.d('Verifying attendance exists for: $employeeName');
    
    // Search for attendance if search field is available
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, employeeName);
      await tester.pumpAndSettle();
    }
    
    // Scroll to find attendance if needed
    await _scrollToFindAttendance(employeeName);
    
    expect(find.text(employeeName), findsOneWidget, reason: 'Attendance for $employeeName should be visible in list');
  }

  /// Scroll to find attendance in list
  Future<void> _scrollToFindAttendance(String employeeName) async {
    final attendanceText = find.text(employeeName);
    
    if (attendanceText.evaluate().isEmpty && attendanceList.evaluate().isNotEmpty) {
      await tester.dragUntilVisible(
        attendanceText,
        attendanceList,
        const Offset(0, -300),
        maxIteration: 10,
      );
    }
  }

  /// Filter attendance by date
  Future<void> filterAttendanceByDate(String date) async {
    _logger.d('Filtering attendance by date: $date');
    
    if (dateFilterButton.evaluate().isNotEmpty) {
      await tester.tap(dateFilterButton);
      await tester.pumpAndSettle();
      
      // Date picker interaction would be implemented here
      // For now, we'll assume the date is set
    }
  }

  /// Filter attendance by property
  Future<void> filterAttendanceByProperty(String propertyName) async {
    _logger.d('Filtering attendance by property: $propertyName');
    
    if (propertyFilterDropdown.evaluate().isNotEmpty) {
      await tester.tap(propertyFilterDropdown);
      await tester.pumpAndSettle();
      
      final propertyOption = find.text(propertyName);
      if (propertyOption.evaluate().isNotEmpty) {
        await tester.tap(propertyOption);
        await tester.pumpAndSettle();
      }
    }
  }

  /// Filter attendance by status
  Future<void> filterAttendanceByStatus(String status) async {
    _logger.d('Filtering attendance by status: $status');
    
    if (statusFilterDropdown.evaluate().isNotEmpty) {
      await tester.tap(statusFilterDropdown);
      await tester.pumpAndSettle();
      
      final statusOption = find.text(status);
      if (statusOption.evaluate().isNotEmpty) {
        await tester.tap(statusOption);
        await tester.pumpAndSettle();
      }
    }
  }

  /// Search for attendance records
  Future<void> searchAttendance(String query) async {
    _logger.d('Searching for attendance: $query');
    
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, query);
      await tester.pumpAndSettle();
    }
  }

  /// Clear search
  Future<void> clearSearch() async {
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, '');
      await tester.pumpAndSettle();
    }
  }

  /// Quick check-in for current user
  Future<void> performQuickCheckIn() async {
    _logger.i('Performing quick check-in');
    
    if (quickCheckInButton.evaluate().isNotEmpty) {
      await tester.tap(quickCheckInButton);
      await tester.pumpAndSettle();
      
      expect(successMessage, findsOneWidget, reason: 'Success message should appear after check-in');
    }
  }

  /// Quick check-out for current user
  Future<void> performQuickCheckOut() async {
    _logger.i('Performing quick check-out');
    
    if (quickCheckOutButton.evaluate().isNotEmpty) {
      await tester.tap(quickCheckOutButton);
      await tester.pumpAndSettle();
      
      expect(successMessage, findsOneWidget, reason: 'Success message should appear after check-out');
    }
  }

  /// View today's attendance
  Future<void> viewTodayAttendance() async {
    _logger.d('Viewing today\'s attendance');
    
    if (todayAttendanceButton.evaluate().isNotEmpty) {
      await tester.tap(todayAttendanceButton);
      await tester.pumpAndSettle();
    }
  }

  /// Edit existing attendance record
  Future<void> editAttendanceRecord(String employeeName, AttendanceData newData) async {
    _logger.i('Editing attendance record for: $employeeName');
    
    await _findAndTapEditButton(employeeName);
    await _fillAttendanceForm(newData);
    await _submitEditForm();
    await _verifyAttendanceUpdated(newData.employeeName);
  }

  /// Find and tap edit button for attendance
  Future<void> _findAndTapEditButton(String employeeName) async {
    await _scrollToFindAttendance(employeeName);
    
    final editButton = editAttendanceButton(employeeName);
    expect(editButton, findsOneWidget, reason: 'Edit button should be present for attendance $employeeName');
    
    await tester.tap(editButton);
    await tester.pumpAndSettle();
  }

  /// Submit edit form
  Future<void> _submitEditForm() async {
    final updateButton = find.text('Update Attendance');
    expect(updateButton, findsOneWidget, reason: 'Update attendance button should be present');
    
    await tester.tap(updateButton);
    await tester.pumpAndSettle();
  }

  /// Verify attendance was updated
  Future<void> _verifyAttendanceUpdated(String employeeName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after update');
    await verifyAttendanceExists(employeeName);
  }

  /// Delete attendance record
  Future<void> deleteAttendanceRecord(String employeeName) async {
    _logger.i('Deleting attendance record for: $employeeName');
    
    await _findAndTapDeleteButton(employeeName);
    await _confirmDeletion();
    await _verifyAttendanceDeleted(employeeName);
  }

  /// Find and tap delete button for attendance
  Future<void> _findAndTapDeleteButton(String employeeName) async {
    await _scrollToFindAttendance(employeeName);
    
    final deleteButton = deleteAttendanceButton(employeeName);
    expect(deleteButton, findsOneWidget, reason: 'Delete button should be present for attendance $employeeName');
    
    await tester.tap(deleteButton);
    await tester.pumpAndSettle();
  }

  /// Confirm deletion in dialog
  Future<void> _confirmDeletion() async {
    final confirmButton = find.text('Delete');
    expect(confirmButton, findsOneWidget, reason: 'Confirm delete button should be present');
    
    await tester.tap(confirmButton);
    await tester.pumpAndSettle();
  }

  /// Verify attendance was deleted
  Future<void> _verifyAttendanceDeleted(String employeeName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after deletion');
    expect(find.text(employeeName), findsNothing, reason: 'Attendance for $employeeName should no longer be visible');
  }

  /// Perform bulk mark present operation
  Future<void> bulkMarkPresent(List<String> employeeNames) async {
    _logger.i('Bulk marking present for: $employeeNames');
    
    // Select employees
    for (final employeeName in employeeNames) {
      await _selectAttendanceRecord(employeeName);
    }
    
    // Perform bulk action
    await tester.tap(bulkActionsButton);
    await tester.pumpAndSettle();
    
    await tester.tap(markPresentButton);
    await tester.pumpAndSettle();
    
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after bulk operation');
  }

  /// Select attendance record for bulk operations
  Future<void> _selectAttendanceRecord(String employeeName) async {
    await _scrollToFindAttendance(employeeName);
    
    final recordCheckbox = find.byKey(Key('select_attendance_$employeeName'));
    if (recordCheckbox.evaluate().isNotEmpty) {
      await tester.tap(recordCheckbox);
      await tester.pumpAndSettle();
    }
  }

  /// Export attendance data
  Future<void> exportAttendanceData() async {
    _logger.i('Exporting attendance data');
    
    if (exportButton.evaluate().isNotEmpty) {
      await tester.tap(exportButton);
      await tester.pumpAndSettle();
      
      expect(successMessage, findsOneWidget, reason: 'Success message should appear after export');
    }
  }

  /// Verify attendance statistics
  Future<void> verifyAttendanceStatistics() async {
    _logger.d('Verifying attendance statistics');
    
    expect(attendanceStatsCard, findsOneWidget, reason: 'Attendance stats card should be visible');
    expect(presentCountText, findsOneWidget, reason: 'Present count should be displayed');
    expect(absentCountText, findsOneWidget, reason: 'Absent count should be displayed');
    expect(lateCountText, findsOneWidget, reason: 'Late count should be displayed');
  }

  /// Refresh attendance list
  Future<void> refreshAttendance() async {
    _logger.d('Refreshing attendance list');
    
    if (refreshButton.evaluate().isNotEmpty) {
      await tester.tap(refreshButton);
      await tester.pumpAndSettle();
    }
  }

  /// Verify screen is loaded
  Future<void> _verifyScreenLoaded() async {
    expect(screen, findsOneWidget, reason: 'Attendance screen should be loaded');
  }

  /// Verify form validation errors
  Future<void> verifyValidationError(String expectedError) async {
    expect(find.text(expectedError), findsOneWidget, 
           reason: 'Validation error should be displayed: $expectedError');
  }

  /// Cancel attendance creation
  Future<void> cancelAttendanceCreation() async {
    if (cancelButton.evaluate().isNotEmpty) {
      await tester.tap(cancelButton);
      await tester.pumpAndSettle();
    }
  }

  /// Verify no attendance message
  Future<void> verifyNoAttendanceMessage() async {
    expect(noAttendanceMessage, findsOneWidget, reason: 'No attendance message should be displayed');
  }
}
