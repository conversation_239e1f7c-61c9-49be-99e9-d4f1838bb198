import 'package:json_annotation/json_annotation.dart';

part 'user_data.g.dart';

@JsonSerializable()
class UserData {
  final String fullName;
  final String email;
  final String password;
  final List<String> roles;
  final List<String> expectedScreens;
  final List<String> restrictedScreens;
  final Map<String, bool> expectedWidgets;
  final bool isActive;
  final String? phone;
  final String? username;

  const UserData({
    required this.fullName,
    required this.email,
    required this.password,
    required this.roles,
    required this.expectedScreens,
    required this.restrictedScreens,
    this.expectedWidgets = const {},
    this.isActive = true,
    this.phone,
    this.username,
  });

  factory UserData.fromJson(Map<String, dynamic> json) => _$UserDataFromJson(json);
  Map<String, dynamic> toJson() => _$UserDataToJson(this);

  UserData copyWith({
    String? fullName,
    String? email,
    String? password,
    List<String>? roles,
    List<String>? expectedScreens,
    List<String>? restrictedScreens,
    Map<String, bool>? expectedWidgets,
    bool? isActive,
    String? phone,
    String? username,
  }) {
    return UserData(
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      password: password ?? this.password,
      roles: roles ?? this.roles,
      expectedScreens: expectedScreens ?? this.expectedScreens,
      restrictedScreens: restrictedScreens ?? this.restrictedScreens,
      expectedWidgets: expectedWidgets ?? this.expectedWidgets,
      isActive: isActive ?? this.isActive,
      phone: phone ?? this.phone,
      username: username ?? this.username,
    );
  }
}

@JsonSerializable()
class RoleData {
  final String name;
  final String description;
  final List<String> permissions;
  final bool isSystemRole;
  final bool isActive;

  const RoleData({
    required this.name,
    required this.description,
    required this.permissions,
    this.isSystemRole = false,
    this.isActive = true,
  });

  factory RoleData.fromJson(Map<String, dynamic> json) => _$RoleDataFromJson(json);
  Map<String, dynamic> toJson() => _$RoleDataToJson(this);
}

@JsonSerializable()
class PermissionData {
  final String name;
  final String description;
  final String category;
  final bool isActive;

  const PermissionData({
    required this.name,
    required this.description,
    required this.category,
    this.isActive = true,
  });

  factory PermissionData.fromJson(Map<String, dynamic> json) => _$PermissionDataFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionDataToJson(this);
}

@JsonSerializable()
class ScreenData {
  final String name;
  final String title;
  final String route;
  final String description;
  final String icon;
  final List<String> requiredPermissions;
  final List<String> allowedRoles;
  final bool isEnabled;
  final bool showInNavigation;
  final int priority;

  const ScreenData({
    required this.name,
    required this.title,
    required this.route,
    required this.description,
    required this.icon,
    required this.requiredPermissions,
    required this.allowedRoles,
    this.isEnabled = true,
    this.showInNavigation = true,
    this.priority = 10,
  });

  factory ScreenData.fromJson(Map<String, dynamic> json) => _$ScreenDataFromJson(json);
  Map<String, dynamic> toJson() => _$ScreenDataToJson(this);
}

@JsonSerializable()
class WidgetData {
  final String name;
  final String title;
  final String type;
  final String screen;
  final String description;
  final List<String> requiredPermissions;
  final List<String> allowedRoles;
  final bool isVisible;
  final bool isEnabled;
  final int positionX;
  final int positionY;
  final int width;
  final int height;
  final int order;
  final Map<String, dynamic> properties;

  const WidgetData({
    required this.name,
    required this.title,
    required this.type,
    required this.screen,
    required this.description,
    required this.requiredPermissions,
    required this.allowedRoles,
    this.isVisible = true,
    this.isEnabled = true,
    this.positionX = 0,
    this.positionY = 0,
    this.width = 1,
    this.height = 1,
    this.order = 1,
    this.properties = const {},
  });

  factory WidgetData.fromJson(Map<String, dynamic> json) => _$WidgetDataFromJson(json);
  Map<String, dynamic> toJson() => _$WidgetDataToJson(this);
}

class RoleScenario {
  final String role;
  final List<String> accessibleScreens;
  final List<String> inaccessibleScreens;
  final Map<String, bool> expectedWidgets;

  const RoleScenario({
    required this.role,
    required this.accessibleScreens,
    required this.inaccessibleScreens,
    required this.expectedWidgets,
  });

  static RoleScenario admin() {
    return const RoleScenario(
      role: 'admin',
      accessibleScreens: [
        'dashboard',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
      ],
      inaccessibleScreens: [],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': true,
        'fuel_monitoring_widget': true,
        'user_management_widget': true,
        'admin_controls_widget': true,
        'recent_activities_widget': true,
        'system_alerts_widget': true,
      },
    );
  }

  static RoleScenario propertyManager() {
    return const RoleScenario(
      role: 'property_manager',
      accessibleScreens: [
        'dashboard',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
      ],
      inaccessibleScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
      ],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': true,
        'fuel_monitoring_widget': true,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': true,
        'system_alerts_widget': false,
      },
    );
  }

  static RoleScenario maintenanceStaff() {
    return const RoleScenario(
      role: 'maintenance_staff',
      accessibleScreens: [
        'dashboard',
        'maintenance',
      ],
      inaccessibleScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
        'properties',
        'attendance',
        'fuel',
      ],
      expectedWidgets: {
        'property_stats_widget': false,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': false,
        'fuel_monitoring_widget': false,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': false,
        'system_alerts_widget': false,
      },
    );
  }

  static RoleScenario viewer() {
    return const RoleScenario(
      role: 'viewer',
      accessibleScreens: [
        'dashboard',
      ],
      inaccessibleScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
      ],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': false,
        'attendance_overview_widget': false,
        'fuel_monitoring_widget': false,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': false,
        'system_alerts_widget': false,
      },
    );
  }
}

class PropertyData {
  final String name;
  final String type;
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String description;

  const PropertyData({
    required this.name,
    required this.type,
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.description,
  });

  PropertyData copyWith({
    String? name,
    String? type,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? description,
  }) {
    return PropertyData(
      name: name ?? this.name,
      type: type ?? this.type,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      description: description ?? this.description,
    );
  }

  @override
  String toString() {
    return 'PropertyData(name: $name, type: $type, address: $address)';
  }
}

class MaintenanceIssueData {
  final String title;
  final String description;
  final String priority;
  final String category;
  final String propertyName;
  final String location;
  final String status;

  const MaintenanceIssueData({
    required this.title,
    required this.description,
    required this.priority,
    required this.category,
    required this.propertyName,
    required this.location,
    required this.status,
  });

  MaintenanceIssueData copyWith({
    String? title,
    String? description,
    String? priority,
    String? category,
    String? propertyName,
    String? location,
    String? status,
  }) {
    return MaintenanceIssueData(
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      category: category ?? this.category,
      propertyName: propertyName ?? this.propertyName,
      location: location ?? this.location,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'MaintenanceIssueData(title: $title, priority: $priority, status: $status)';
  }
}

class AttendanceData {
  final String employeeName;
  final String propertyName;
  final String date;
  final String checkInTime;
  final String checkOutTime;
  final String status;
  final String notes;

  const AttendanceData({
    required this.employeeName,
    required this.propertyName,
    required this.date,
    required this.checkInTime,
    required this.checkOutTime,
    required this.status,
    required this.notes,
  });

  AttendanceData copyWith({
    String? employeeName,
    String? propertyName,
    String? date,
    String? checkInTime,
    String? checkOutTime,
    String? status,
    String? notes,
  }) {
    return AttendanceData(
      employeeName: employeeName ?? this.employeeName,
      propertyName: propertyName ?? this.propertyName,
      date: date ?? this.date,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      status: status ?? this.status,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'AttendanceData(employeeName: $employeeName, status: $status, date: $date)';
  }
}