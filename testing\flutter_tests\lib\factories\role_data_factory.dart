import 'package:faker/faker.dart';
import '../models/user_data.dart';

class RoleDataFactory {
  static final _faker = Faker();

  /// Creates a custom role for testing
  static RoleData createCustomRole({
    String? name,
    String? description,
    List<String>? permissions,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: name ?? 'Test Role $timestamp',
      description: description ?? 'Custom role created for testing purposes',
      permissions: permissions ?? [
        'properties.read',
        'maintenance.read',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a site coordinator role
  static RoleData createSiteCoordinator() {
    return const RoleData(
      name: 'Site Coordinator',
      description: 'Coordinates site activities and reports',
      permissions: [
        'properties.read',
        'maintenance.read',
        'maintenance.create',
        'attendance.read',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a security supervisor role
  static RoleData createSecuritySupervisor() {
    return const RoleData(
      name: 'Security Supervisor',
      description: 'Supervises security operations',
      permissions: [
        'security.read',
        'security.create',
        'security.update',
        'incidents.manage',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a facilities manager role
  static RoleData createFacilitiesManager() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: 'Facilities Manager $timestamp',
      description: 'Manages facility operations and maintenance',
      permissions: [
        'properties.read',
        'properties.update',
        'maintenance.read',
        'maintenance.create',
        'maintenance.update',
        'maintenance.assign',
        'fuel.read',
        'fuel.update',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a role with minimal permissions
  static RoleData createMinimalRole() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: 'Minimal Role $timestamp',
      description: 'Role with minimal permissions for testing',
      permissions: ['dashboard.view'],
      isSystemRole: false,
    );
  }

  /// Creates a role with maximum permissions (excluding admin)
  static RoleData createMaximalRole() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: 'Maximal Role $timestamp',
      description: 'Role with maximum non-admin permissions',
      permissions: [
        'properties.read',
        'properties.create',
        'properties.update',
        'maintenance.read',
        'maintenance.create',
        'maintenance.update',
        'maintenance.assign',
        'maintenance.escalate',
        'attendance.read',
        'attendance.create',
        'attendance.update',
        'fuel.read',
        'fuel.create',
        'fuel.update',
        'security.read',
        'security.create',
        'reports.generate',
        'reports.export',
        'dashboard.view',
        'thresholds.configure',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a role for permission testing
  static RoleData createForPermissionTesting() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: 'Permission Test Role $timestamp',
      description: 'Role for testing permission assignments',
      permissions: [
        'properties.read',
        'maintenance.read',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a role with invalid data for validation testing
  static RoleData createInvalid() {
    return const RoleData(
      name: '', // Invalid: empty name
      description: 'Invalid role for testing validation',
      permissions: [],
      isSystemRole: false,
    );
  }

  /// Creates a role with duplicate name for testing
  static RoleData createDuplicate(String existingName) {
    return RoleData(
      name: existingName,
      description: 'Duplicate role for testing',
      permissions: ['dashboard.view'],
      isSystemRole: false,
    );
  }

  /// Creates multiple roles for bulk operations
  static List<RoleData> createBulkRoles(int count) {
    final roles = <RoleData>[];
    
    for (int i = 0; i < count; i++) {
      final timestamp = DateTime.now().millisecondsSinceEpoch + i;
      
      roles.add(RoleData(
        name: 'Bulk Role ${i + 1} $timestamp',
        description: 'Bulk role ${i + 1} for testing',
        permissions: [
          'dashboard.view',
          'properties.read',
        ],
        isSystemRole: false,
      ));
    }
    
    return roles;
  }

  /// Creates a role for escalation testing
  static RoleData createEscalationRole() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: 'Escalation Manager $timestamp',
      description: 'Role for testing escalation workflows',
      permissions: [
        'maintenance.read',
        'maintenance.escalate',
        'maintenance.assign',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a role for reporting testing
  static RoleData createReportingRole() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: 'Reporting Specialist $timestamp',
      description: 'Role for testing reporting functionality',
      permissions: [
        'properties.read',
        'maintenance.read',
        'attendance.read',
        'fuel.read',
        'reports.generate',
        'reports.export',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }
}

class PermissionDataFactory {
  /// Creates a custom permission for testing
  static PermissionData createCustomPermission({
    String? name,
    String? description,
    String? category,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return PermissionData(
      name: name ?? 'test.permission.$timestamp',
      description: description ?? 'Custom permission for testing',
      category: category ?? 'testing',
    );
  }

  /// Creates property-related permissions
  static List<PermissionData> createPropertyPermissions() {
    return [
      const PermissionData(
        name: 'properties.read',
        description: 'View properties',
        category: 'properties',
      ),
      const PermissionData(
        name: 'properties.create',
        description: 'Create new properties',
        category: 'properties',
      ),
      const PermissionData(
        name: 'properties.update',
        description: 'Update property details',
        category: 'properties',
      ),
      const PermissionData(
        name: 'properties.delete',
        description: 'Delete properties',
        category: 'properties',
      ),
    ];
  }

  /// Creates maintenance-related permissions
  static List<PermissionData> createMaintenancePermissions() {
    return [
      const PermissionData(
        name: 'maintenance.read',
        description: 'View maintenance issues',
        category: 'maintenance',
      ),
      const PermissionData(
        name: 'maintenance.create',
        description: 'Create maintenance issues',
        category: 'maintenance',
      ),
      const PermissionData(
        name: 'maintenance.update',
        description: 'Update maintenance issues',
        category: 'maintenance',
      ),
      const PermissionData(
        name: 'maintenance.assign',
        description: 'Assign maintenance issues',
        category: 'maintenance',
      ),
      const PermissionData(
        name: 'maintenance.escalate',
        description: 'Escalate maintenance issues',
        category: 'maintenance',
      ),
    ];
  }

  /// Creates admin-related permissions
  static List<PermissionData> createAdminPermissions() {
    return [
      const PermissionData(
        name: 'users.create',
        description: 'Create new users',
        category: 'admin',
      ),
      const PermissionData(
        name: 'users.update',
        description: 'Update user details',
        category: 'admin',
      ),
      const PermissionData(
        name: 'users.delete',
        description: 'Delete users',
        category: 'admin',
      ),
      const PermissionData(
        name: 'roles.create',
        description: 'Create new roles',
        category: 'admin',
      ),
      const PermissionData(
        name: 'roles.update',
        description: 'Update role details',
        category: 'admin',
      ),
      const PermissionData(
        name: 'permissions.configure',
        description: 'Configure permissions',
        category: 'admin',
      ),
    ];
  }

  /// Creates all standard permissions
  static List<PermissionData> createAllStandardPermissions() {
    return [
      ...createPropertyPermissions(),
      ...createMaintenancePermissions(),
      ...createAdminPermissions(),
      const PermissionData(
        name: 'dashboard.view',
        description: 'View dashboard',
        category: 'general',
      ),
      const PermissionData(
        name: 'reports.generate',
        description: 'Generate reports',
        category: 'reports',
      ),
      const PermissionData(
        name: 'reports.export',
        description: 'Export reports',
        category: 'reports',
      ),
    ];
  }
}

class ScreenDataFactory {
  /// Creates a custom screen for testing
  static ScreenData createCustomScreen({
    String? name,
    String? title,
    String? route,
    List<String>? requiredPermissions,
    List<String>? allowedRoles,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return ScreenData(
      name: name ?? 'test_screen_$timestamp',
      title: title ?? 'Test Screen $timestamp',
      route: route ?? '/test-screen-$timestamp',
      description: 'Custom screen created for testing',
      icon: 'test_tube',
      requiredPermissions: requiredPermissions ?? ['dashboard.view'],
      allowedRoles: allowedRoles ?? ['admin'],
    );
  }

  /// Creates a reports screen
  static ScreenData createReportsScreen() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return ScreenData(
      name: 'custom_reports_$timestamp',
      title: 'Custom Reports',
      route: '/custom-reports-$timestamp',
      description: 'Custom reporting dashboard',
      icon: 'assessment',
      requiredPermissions: ['reports.generate', 'reports.export'],
      allowedRoles: ['admin', 'property_manager', 'reporting_specialist'],
      priority: 15,
    );
  }

  /// Creates an analytics screen
  static ScreenData createAnalyticsScreen() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return ScreenData(
      name: 'analytics_$timestamp',
      title: 'Analytics Dashboard',
      route: '/analytics-$timestamp',
      description: 'Advanced analytics and insights',
      icon: 'analytics',
      requiredPermissions: ['reports.generate', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager'],
      priority: 20,
    );
  }
}

class WidgetDataFactory {
  /// Creates a custom widget for testing
  static WidgetData createCustomWidget({
    String? name,
    String? title,
    String? type,
    String? screen,
    List<String>? requiredPermissions,
    List<String>? allowedRoles,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return WidgetData(
      name: name ?? 'test_widget_$timestamp',
      title: title ?? 'Test Widget $timestamp',
      type: type ?? 'statCard',
      screen: screen ?? 'dashboard',
      description: 'Custom widget created for testing',
      requiredPermissions: requiredPermissions ?? ['dashboard.view'],
      allowedRoles: allowedRoles ?? ['admin'],
      positionX: 0,
      positionY: 0,
      width: 1,
      height: 1,
      order: 1,
    );
  }

  /// Creates a chart widget
  static WidgetData createChartWidget() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return WidgetData(
      name: 'efficiency_chart_$timestamp',
      title: 'Efficiency Chart',
      type: 'chart',
      screen: 'dashboard',
      description: 'Displays efficiency metrics',
      requiredPermissions: ['reports.generate', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager'],
      positionX: 0,
      positionY: 1,
      width: 2,
      height: 1,
      order: 2,
      properties: {
        'chartType': 'line',
        'dataSource': 'efficiency_metrics',
        'refreshInterval': 300,
      },
    );
  }

  /// Creates a table widget
  static WidgetData createTableWidget() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return WidgetData(
      name: 'recent_issues_table_$timestamp',
      title: 'Recent Issues',
      type: 'table',
      screen: 'dashboard',
      description: 'Shows recent maintenance issues',
      requiredPermissions: ['maintenance.read', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
      positionX: 2,
      positionY: 0,
      width: 2,
      height: 2,
      order: 3,
      properties: {
        'maxRows': 10,
        'sortBy': 'created_at',
        'sortOrder': 'desc',
      },
    );
  }
}
