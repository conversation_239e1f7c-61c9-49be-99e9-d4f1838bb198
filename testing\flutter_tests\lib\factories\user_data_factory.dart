import 'package:faker/faker.dart';
import '../models/user_data.dart';

class UserDataFactory {
  static final _faker = Faker();

  /// Creates a test admin user with full permissions
  static UserData createAdmin() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Test Admin ${_faker.person.firstName()}',
      email: 'test.admin.$<EMAIL>',
      password: 'TestAdmin123!',
      username: 'testadmin$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: ['admin'],
      expectedScreens: [
        'dashboard',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
      ],
      restrictedScreens: [],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': true,
        'fuel_monitoring_widget': true,
        'user_management_widget': true,
        'admin_controls_widget': true,
        'recent_activities_widget': true,
        'system_alerts_widget': true,
      },
    );
  }

  /// Creates a test property manager user
  static UserData createPropertyManager() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Test Property Manager ${_faker.person.firstName()}',
      email: 'test.manager.$<EMAIL>',
      password: 'TestManager123!',
      username: 'testmanager$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: ['property_manager'],
      expectedScreens: [
        'dashboard',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
      ],
      restrictedScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
      ],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': true,
        'fuel_monitoring_widget': true,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': true,
        'system_alerts_widget': false,
      },
    );
  }

  /// Creates a test maintenance staff user
  static UserData createMaintenanceStaff() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Test Maintenance Staff ${_faker.person.firstName()}',
      email: 'test.maintenance.$<EMAIL>',
      password: 'TestMaintenance123!',
      username: 'testmaintenance$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: ['maintenance_staff'],
      expectedScreens: [
        'dashboard',
        'maintenance',
      ],
      restrictedScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
        'properties',
        'attendance',
        'fuel',
      ],
      expectedWidgets: {
        'property_stats_widget': false,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': false,
        'fuel_monitoring_widget': false,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': false,
        'system_alerts_widget': false,
      },
    );
  }

  /// Creates a test viewer user with minimal permissions
  static UserData createViewer() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Test Viewer ${_faker.person.firstName()}',
      email: 'test.viewer.$<EMAIL>',
      password: 'TestViewer123!',
      username: 'testviewer$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: ['viewer'],
      expectedScreens: [
        'dashboard',
      ],
      restrictedScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
      ],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': false,
        'attendance_overview_widget': false,
        'fuel_monitoring_widget': false,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': false,
        'system_alerts_widget': false,
      },
    );
  }

  /// Creates a user with invalid email for validation testing
  static UserData createWithInvalidEmail() {
    return UserData(
      fullName: 'Invalid Email User',
      email: 'invalid-email-format',
      password: 'ValidPassword123!',
      roles: ['viewer'],
      expectedScreens: [],
      restrictedScreens: [],
    );
  }

  /// Creates a user with weak password for validation testing
  static UserData createWithWeakPassword() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Weak Password User',
      email: 'weak.password.$<EMAIL>',
      password: '123',
      roles: ['viewer'],
      expectedScreens: [],
      restrictedScreens: [],
    );
  }

  /// Creates a user with custom roles and permissions
  static UserData createCustomRole({
    required List<String> roles,
    required List<String> expectedScreens,
    List<String>? restrictedScreens,
    Map<String, bool>? expectedWidgets,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Custom Role User ${_faker.person.firstName()}',
      email: 'custom.role.$<EMAIL>',
      password: 'CustomRole123!',
      username: 'customrole$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: roles,
      expectedScreens: expectedScreens,
      restrictedScreens: restrictedScreens ?? [],
      expectedWidgets: expectedWidgets ?? {},
    );
  }

  /// Creates a user for role assignment testing
  static UserData createForRoleAssignment() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Role Assignment Test User',
      email: 'role.assignment.$<EMAIL>',
      password: 'RoleAssignment123!',
      username: 'roleassignment$timestamp',
      roles: ['viewer'], // Start with minimal role
      expectedScreens: ['dashboard'],
      restrictedScreens: [
        'admin',
        'user_management',
        'properties',
        'maintenance',
      ],
    );
  }

  /// Creates a user for permission testing
  static UserData createForPermissionTesting() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Permission Test User',
      email: 'permission.test.$<EMAIL>',
      password: 'PermissionTest123!',
      username: 'permissiontest$timestamp',
      roles: ['property_manager'],
      expectedScreens: [
        'dashboard',
        'properties',
        'maintenance',
      ],
      restrictedScreens: [
        'admin',
        'user_management',
      ],
    );
  }

  /// Creates multiple test users for bulk operations
  static List<UserData> createBulkUsers(int count) {
    final users = <UserData>[];

    for (int i = 0; i < count; i++) {
      final timestamp = DateTime.now().millisecondsSinceEpoch + i;

      users.add(UserData(
        fullName: 'Bulk User ${i + 1} ${_faker.person.firstName()}',
        email: 'bulk.user.$<EMAIL>',
        password: 'BulkUser123!',
        username: 'bulkuser$timestamp',
        roles: ['viewer'],
        expectedScreens: ['dashboard'],
        restrictedScreens: [],
      ));
    }

    return users;
  }

  /// Creates a user with duplicate email for testing
  static UserData createDuplicate(String existingEmail) {
    return UserData(
      fullName: 'Duplicate Email User',
      email: existingEmail,
      password: 'DuplicateTest123!',
      roles: ['viewer'],
      expectedScreens: [],
      restrictedScreens: [],
    );
  }
}

class RoleDataFactory {
  static final _faker = Faker();

  /// Creates a custom role for testing
  static RoleData createCustomRole({
    String? name,
    String? description,
    List<String>? permissions,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return RoleData(
      name: name ?? 'Test Role $timestamp',
      description: description ?? 'Custom role created for testing purposes',
      permissions: permissions ?? [
        'properties.read',
        'maintenance.read',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a site coordinator role
  static RoleData createSiteCoordinator() {
    return const RoleData(
      name: 'Site Coordinator',
      description: 'Coordinates site activities and reports',
      permissions: [
        'properties.read',
        'maintenance.read',
        'maintenance.create',
        'attendance.read',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a security supervisor role
  static RoleData createSecuritySupervisor() {
    return const RoleData(
      name: 'Security Supervisor',
      description: 'Supervises security operations',
      permissions: [
        'security.read',
        'security.create',
        'security.update',
        'incidents.manage',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a role with minimal permissions
  static RoleData createMinimalRole() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return RoleData(
      name: 'Minimal Role $timestamp',
      description: 'Role with minimal permissions for testing',
      permissions: ['dashboard.view'],
      isSystemRole: false,
    );
  }

  /// Creates a role with maximum permissions (excluding admin)
  static RoleData createMaximalRole() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return RoleData(
      name: 'Maximal Role $timestamp',
      description: 'Role with maximum non-admin permissions',
      permissions: [
        'properties.read',
        'properties.create',
        'properties.update',
        'maintenance.read',
        'maintenance.create',
        'maintenance.update',
        'maintenance.assign',
        'attendance.read',
        'attendance.create',
        'attendance.update',
        'fuel.read',
        'fuel.create',
        'fuel.update',
        'reports.generate',
        'reports.export',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }
}

class PropertyDataFactory {
  static final _faker = Faker();

  /// Creates a residential property
  static PropertyData createResidential() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return PropertyData(
      name: 'Test Residential ${_faker.address.streetName()} $timestamp',
      type: 'residential',
      address: _faker.address.streetAddress(),
      city: _faker.address.city(),
      state: _faker.address.state(),
      zipCode: _faker.address.zipCode(),
      description: 'Test residential property for automation testing',
    );
  }

  /// Creates an office property
  static PropertyData createOffice() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return PropertyData(
      name: 'Test Office ${_faker.company.name()} $timestamp',
      type: 'office',
      address: _faker.address.streetAddress(),
      city: _faker.address.city(),
      state: _faker.address.state(),
      zipCode: _faker.address.zipCode(),
      description: 'Test office property for automation testing',
    );
  }

  /// Creates a construction site property
  static PropertyData createConstruction() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return PropertyData(
      name: 'Test Construction Site $timestamp',
      type: 'construction',
      address: _faker.address.streetAddress(),
      city: _faker.address.city(),
      state: _faker.address.state(),
      zipCode: _faker.address.zipCode(),
      description: 'Test construction site for automation testing',
    );
  }

  /// Creates a property with invalid data for validation testing
  static PropertyData createInvalid() {
    return const PropertyData(
      name: '', // Invalid: empty name
      type: 'residential',
      address: 'Invalid Property Address',
      city: 'Test City',
      state: 'Test State',
      zipCode: '12345',
      description: 'Invalid property for testing validation',
    );
  }

  /// Creates multiple properties for bulk operations
  static List<PropertyData> createBulkProperties(int count) {
    final properties = <PropertyData>[];
    final types = ['residential', 'office', 'construction'];

    for (int i = 0; i < count; i++) {
      final timestamp = DateTime.now().millisecondsSinceEpoch + i;
      final type = types[i % types.length];

      properties.add(PropertyData(
        name: 'Bulk Property ${i + 1} $timestamp',
        type: type,
        address: _faker.address.streetAddress(),
        city: _faker.address.city(),
        state: _faker.address.state(),
        zipCode: _faker.address.zipCode(),
        description: 'Bulk property ${i + 1} for testing',
      ));
    }

    return properties;
  }
}

class MaintenanceIssueDataFactory {
  static final _faker = Faker();

  /// Creates a high priority maintenance issue
  static MaintenanceIssueData createHighPriority() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return MaintenanceIssueData(
      title: 'High Priority Issue $timestamp',
      description: 'Critical maintenance issue requiring immediate attention',
      priority: 'High',
      category: 'Electrical',
      propertyName: 'Test Property',
      location: 'Building A, Floor 2',
      status: 'Pending',
    );
  }

  /// Creates a medium priority maintenance issue
  static MaintenanceIssueData createMediumPriority() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return MaintenanceIssueData(
      title: 'Medium Priority Issue $timestamp',
      description: 'Maintenance issue that needs attention within a few days',
      priority: 'Medium',
      category: 'Plumbing',
      propertyName: 'Test Property',
      location: 'Building B, Floor 1',
      status: 'Pending',
    );
  }

  /// Creates a low priority maintenance issue
  static MaintenanceIssueData createLowPriority() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return MaintenanceIssueData(
      title: 'Low Priority Issue $timestamp',
      description: 'Minor maintenance issue that can be scheduled',
      priority: 'Low',
      category: 'General',
      propertyName: 'Test Property',
      location: 'Building C, Floor 3',
      status: 'Pending',
    );
  }

  /// Creates an HVAC maintenance issue
  static MaintenanceIssueData createHVACIssue() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return MaintenanceIssueData(
      title: 'HVAC System Issue $timestamp',
      description: 'Air conditioning system not working properly',
      priority: 'High',
      category: 'HVAC',
      propertyName: 'Test Property',
      location: 'Rooftop HVAC Unit',
      status: 'Pending',
    );
  }

  /// Creates a maintenance issue with invalid data
  static MaintenanceIssueData createInvalid() {
    return const MaintenanceIssueData(
      title: '', // Invalid: empty title
      description: 'Invalid maintenance issue for testing validation',
      priority: 'Medium',
      category: 'General',
      propertyName: 'Test Property',
      location: 'Test Location',
      status: 'Pending',
    );
  }
}

class AttendanceDataFactory {
  static final _faker = Faker();

  /// Creates a present attendance record
  static AttendanceData createPresent() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return AttendanceData(
      employeeName: 'Test Employee $timestamp',
      propertyName: 'Test Property',
      date: DateTime.now().toString().split(' ')[0],
      checkInTime: '09:00',
      checkOutTime: '17:00',
      status: 'Present',
      notes: 'Regular attendance',
    );
  }

  /// Creates an absent attendance record
  static AttendanceData createAbsent() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return AttendanceData(
      employeeName: 'Test Employee $timestamp',
      propertyName: 'Test Property',
      date: DateTime.now().toString().split(' ')[0],
      checkInTime: '',
      checkOutTime: '',
      status: 'Absent',
      notes: 'Sick leave',
    );
  }

  /// Creates a late attendance record
  static AttendanceData createLate() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return AttendanceData(
      employeeName: 'Test Employee $timestamp',
      propertyName: 'Test Property',
      date: DateTime.now().toString().split(' ')[0],
      checkInTime: '10:30',
      checkOutTime: '17:00',
      status: 'Late',
      notes: 'Traffic delay',
    );
  }

  /// Creates an attendance record with invalid data
  static AttendanceData createInvalid() {
    return const AttendanceData(
      employeeName: '', // Invalid: empty employee name
      propertyName: 'Test Property',
      date: '2024-01-01',
      checkInTime: '09:00',
      checkOutTime: '17:00',
      status: 'Present',
      notes: 'Invalid attendance for testing validation',
    );
  }
}