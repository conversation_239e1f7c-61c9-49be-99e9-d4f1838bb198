import 'package:faker/faker.dart';
import '../models/user_data.dart';

class UserDataFactory {
  static final _faker = Faker();

  /// Creates a test admin user with full permissions
  static UserData createAdmin() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return UserData(
      fullName: 'Test Admin ${_faker.person.firstName()}',
      email: 'test.admin.$<EMAIL>',
      password: 'TestAdmin123!',
      username: 'testadmin$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: ['admin'],
      expectedScreens: [
        'dashboard',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
      ],
      restrictedScreens: [],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': true,
        'fuel_monitoring_widget': true,
        'user_management_widget': true,
        'admin_controls_widget': true,
        'recent_activities_widget': true,
        'system_alerts_widget': true,
      },
    );
  }

  /// Creates a test property manager user
  static UserData createPropertyManager() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return UserData(
      fullName: 'Test Property Manager ${_faker.person.firstName()}',
      email: 'test.manager.$<EMAIL>',
      password: 'TestManager123!',
      username: 'testmanager$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: ['property_manager'],
      expectedScreens: [
        'dashboard',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
      ],
      restrictedScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
      ],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': true,
        'fuel_monitoring_widget': true,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': true,
        'system_alerts_widget': false,
      },
    );
  }

  /// Creates a test maintenance staff user
  static UserData createMaintenanceStaff() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return UserData(
      fullName: 'Test Maintenance Staff ${_faker.person.firstName()}',
      email: 'test.maintenance.$<EMAIL>',
      password: 'TestMaintenance123!',
      username: 'testmaintenance$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: ['maintenance_staff'],
      expectedScreens: [
        'dashboard',
        'maintenance',
      ],
      restrictedScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
        'properties',
        'attendance',
        'fuel',
      ],
      expectedWidgets: {
        'property_stats_widget': false,
        'maintenance_summary_widget': true,
        'attendance_overview_widget': false,
        'fuel_monitoring_widget': false,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': false,
        'system_alerts_widget': false,
      },
    );
  }

  /// Creates a test viewer user with minimal permissions
  static UserData createViewer() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return UserData(
      fullName: 'Test Viewer ${_faker.person.firstName()}',
      email: 'test.viewer.$<EMAIL>',
      password: 'TestViewer123!',
      username: 'testviewer$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: ['viewer'],
      expectedScreens: [
        'dashboard',
      ],
      restrictedScreens: [
        'admin',
        'user_management',
        'role_management',
        'permission_config',
        'screen_management',
        'widget_management',
        'properties',
        'maintenance',
        'attendance',
        'fuel',
      ],
      expectedWidgets: {
        'property_stats_widget': true,
        'maintenance_summary_widget': false,
        'attendance_overview_widget': false,
        'fuel_monitoring_widget': false,
        'user_management_widget': false,
        'admin_controls_widget': false,
        'recent_activities_widget': false,
        'system_alerts_widget': false,
      },
    );
  }

  /// Creates a user with invalid email for validation testing
  static UserData createWithInvalidEmail() {
    return UserData(
      fullName: 'Invalid Email User',
      email: 'invalid-email-format',
      password: 'ValidPassword123!',
      roles: ['viewer'],
      expectedScreens: [],
      restrictedScreens: [],
    );
  }

  /// Creates a user with weak password for validation testing
  static UserData createWithWeakPassword() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return UserData(
      fullName: 'Weak Password User',
      email: 'weak.password.$<EMAIL>',
      password: '123',
      roles: ['viewer'],
      expectedScreens: [],
      restrictedScreens: [],
    );
  }

  /// Creates a user with custom roles and permissions
  static UserData createCustomRole({
    required List<String> roles,
    required List<String> expectedScreens,
    List<String>? restrictedScreens,
    Map<String, bool>? expectedWidgets,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return UserData(
      fullName: 'Custom Role User ${_faker.person.firstName()}',
      email: 'custom.role.$<EMAIL>',
      password: 'CustomRole123!',
      username: 'customrole$timestamp',
      phone: _faker.phoneNumber.us(),
      roles: roles,
      expectedScreens: expectedScreens,
      restrictedScreens: restrictedScreens ?? [],
      expectedWidgets: expectedWidgets ?? {},
    );
  }

  /// Creates a user for role assignment testing
  static UserData createForRoleAssignment() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return UserData(
      fullName: 'Role Assignment Test User',
      email: 'role.assignment.$<EMAIL>',
      password: 'RoleAssignment123!',
      username: 'roleassignment$timestamp',
      roles: ['viewer'], // Start with minimal role
      expectedScreens: ['dashboard'],
      restrictedScreens: [
        'admin',
        'user_management',
        'properties',
        'maintenance',
      ],
    );
  }

  /// Creates a user for permission testing
  static UserData createForPermissionTesting() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return UserData(
      fullName: 'Permission Test User',
      email: 'permission.test.$<EMAIL>',
      password: 'PermissionTest123!',
      username: 'permissiontest$timestamp',
      roles: ['property_manager'],
      expectedScreens: [
        'dashboard',
        'properties',
        'maintenance',
      ],
      restrictedScreens: [
        'admin',
        'user_management',
      ],
    );
  }

  /// Creates multiple test users for bulk operations
  static List<UserData> createBulkUsers(int count) {
    final users = <UserData>[];
    
    for (int i = 0; i < count; i++) {
      final timestamp = DateTime.now().millisecondsSinceEpoch + i;
      
      users.add(UserData(
        fullName: 'Bulk User ${i + 1} ${_faker.person.firstName()}',
        email: 'bulk.user.$<EMAIL>',
        password: 'BulkUser123!',
        username: 'bulkuser$timestamp',
        roles: ['viewer'],
        expectedScreens: ['dashboard'],
        restrictedScreens: [],
      ));
    }
    
    return users;
  }

  /// Creates a user with duplicate email for testing
  static UserData createDuplicate(String existingEmail) {
    return UserData(
      fullName: 'Duplicate Email User',
      email: existingEmail,
      password: 'DuplicateTest123!',
      roles: ['viewer'],
      expectedScreens: [],
      restrictedScreens: [],
    );
  }
}

class RoleDataFactory {
  static final _faker = Faker();

  /// Creates a custom role for testing
  static RoleData createCustomRole({
    String? name,
    String? description,
    List<String>? permissions,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: name ?? 'Test Role $timestamp',
      description: description ?? 'Custom role created for testing purposes',
      permissions: permissions ?? [
        'properties.read',
        'maintenance.read',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a site coordinator role
  static RoleData createSiteCoordinator() {
    return const RoleData(
      name: 'Site Coordinator',
      description: 'Coordinates site activities and reports',
      permissions: [
        'properties.read',
        'maintenance.read',
        'maintenance.create',
        'attendance.read',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a security supervisor role
  static RoleData createSecuritySupervisor() {
    return const RoleData(
      name: 'Security Supervisor',
      description: 'Supervises security operations',
      permissions: [
        'security.read',
        'security.create',
        'security.update',
        'incidents.manage',
        'reports.generate',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }

  /// Creates a role with minimal permissions
  static RoleData createMinimalRole() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: 'Minimal Role $timestamp',
      description: 'Role with minimal permissions for testing',
      permissions: ['dashboard.view'],
      isSystemRole: false,
    );
  }

  /// Creates a role with maximum permissions (excluding admin)
  static RoleData createMaximalRole() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return RoleData(
      name: 'Maximal Role $timestamp',
      description: 'Role with maximum non-admin permissions',
      permissions: [
        'properties.read',
        'properties.create',
        'properties.update',
        'maintenance.read',
        'maintenance.create',
        'maintenance.update',
        'maintenance.assign',
        'attendance.read',
        'attendance.create',
        'attendance.update',
        'fuel.read',
        'fuel.create',
        'fuel.update',
        'reports.generate',
        'reports.export',
        'dashboard.view',
      ],
      isSystemRole: false,
    );
  }
}
