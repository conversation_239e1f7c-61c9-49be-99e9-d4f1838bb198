import{a as o}from"./chunk-7TT5MNTH.js";import{a as r}from"./chunk-KERBADJJ.js";import{n as a,o as e}from"./chunk-PC2QB7VM.js";var i=["5##-###-###","5##.###.###","5## ### ###","5########"];var ia={formats:i},n=ia;var l=["aceituna","amarillo","azul","blanco","cian","cielo azul","ciruela","fucsia","gris","lavanda","lima","magenta","marfil","marr\xF3n","menta verde","morado","naranja","negro","oro","orqu\xEDdea","plata","rojo","rosa","salm\xF3n","tan","teal","turquesa","verde","violeta","\xEDndigo"];var na={human:l},t=na;var s=["Aire libre","Automoci\xF3n","Baby","Belleza","Deportes","Electr\xF3nica","Herramientas","Hogar","Industrial","Jard\xEDn","Joyer\xEDa","Juegos","Juguetes","Kids","Libros","M\xFAsica","Ordenadores","Pel\xEDculas","Ropa","Salud","Ultramarinos","Zapatos"];var c={adjective:["Artesanal","Elegante","Ergon\xF3mico","Fant\xE1stico","Gen\xE9rica","Gorgeous","Hecho a mano","Incre\xEDble","Inteligente","Licencia","Peque\xF1o","Pr\xE1ctica","Refinado","R\xFAstico","Sabrosa","Sin marca"],material:["Acero","Caucho","Cotton","Fresco","Frozen","Granito","Hormig\xF3n","Madera","Metal","Pl\xE1stico","Soft"],product:["Auto","Bike","Camisa","Computadora","Embutidos","Ensalada","Guantes","Jab\xF3n","Mesa","Pantalones","Pelota","Pescado","Pizza","Pollo","Presidente","Queso","Rat\xF3n","Sombrero","Teclado","Toallas","Tocino","Tuna","Zapatos"]};var la={department:s,product_name:c},d=la;var u=["Actualizable","Adaptativo","Amigable","Asimilado","Auto proporciona","Automatizado","Avanzado","Cara a cara","Centrado en el negocio","Centrado en el usuario","Centralizado","Clonado","Compartible","Compatible","Configurable","Descentralizado","Digitalizado","Distribuido","Diverso","En red","Enfocado","Enfocado a benficios","Enfocado en la calidad","Equilibrado","Ergonomico","Exclusivo","Expandido","Extendido","Fundamental","F\xE1cil","Gestionado","Horizontal","Implementado","Ingenieria inversa","Innovador","Integrado","Intercambiable","Intuitivo","Inverso","Mejorado","Monitorizado","Multi canal","Multi capa","Multi grupo","Multi lateral","Multi plataforma","Obligatorio","Opcional","Open-source","Operativo","Optimizado","Organico","Organizado","Orientado a equipos","Orientado a objetos","Or\xEDgenes","Para toda la empresa","Perseverando","Persistente","Polarizado","Pre-emptivo","Proactivo","Profundo","Programable","Progresivo","Public-key","Re-contextualizado","Re-implementado","Reactivo","Realineado","Reducido","Robusto","Seguro","Sincronizado","Total","Totalmente configurable","Universal","Versatil","Virtual","Visionario","en fases"];var m=["24/365","24/7","A medida","Asesino","B2B","B2C","Back-end","Clase mundial","Clics y mortero","Colaboraci\xF3n","Convincente","Cross-media","C\xF3digo abierto","De extremo a extremo","De vanguardia","Din\xE1mico","Distribuida","Doce y cincuenta y nueve de la noche","E-business","Eficiente","Empresa","Enchufa y juega","Escalable","Estrat\xE9gico","Extensible","Filo sangriento","Fricci\xF3n","Frontal","Fuera de la caja","Global","Granular","Habilitado web","Hol\xEDstico","Impactante","Inal\xE1mbrico","Innovador","Integrado","Interactiva","Intuitivo","Llave en mano","Magn\xE9tica","Mejor de su clase","Misi\xF3n cr\xEDtica","Multiplataforma","Pegajosa","Proactivo","Pr\xF3xima generaci\xF3n","Punto com","Revolucionario","Ricos","Robusto","Sexy","Sin costura","Sin\xE9rgico","Tiempo real","Transparente","Ubicua","User-centric","Valor a\xF1adido","Vertical","Viral","Virtual","Visionario","ladrillos y clics"];var p=["Mindshare","ROI","ancho de banda","aplicaciones","arquitecturas","asociaciones","cadenas de suministro","canales","comunidades","contenido","convergencia","e-business","e-commerce","e-servicios","elementos de acci\xF3n","entregables","esquemas","experiencias","funcionalidades","infomediarios","infraestructuras","iniciativas","interfaces","mercados","mercados electr\xF3nicos","metodolog\xEDas","minoristas electr\xF3nicos","modelos","m\xE9tricas","nichos","ojos","paradigmas","plataformas","portales","redes","relaciones","servicios web","sinergias","sistemas","soluciones","tecnolog\xEDas","usuarios","vortales","web-readiness"];var g=["Envisioneer","abrazar","acelerar","agregado","apalancamiento","aprovechar","arn\xE9s","arquitecto","conducir","crecer","cultivar","desatar","desintermediar","desplegar","e-enable","empoderar","enganchar","entregue","escala","estrategias","evolucionar","explotar","extender","facilitar","generar","habilitar","incentivar","incubar","ingeniero","innovar","integrar","malla","marca","matriz","maximizar","mejorar","monetizar","objetivo","optimizar","orquestar","pizarra","poner en pr\xE1ctica","productize","punto de referencia","racionalizar","recontextualizar","redefinir","reintermediate","reinventar","repetir","reutilizar","revolucionar","sindicato","sinergia","sintetizar","transformar","transformarse","transici\xF3n","utilizar","visualizar"];var b=["24 horas","24/7","3rd generaci\xF3n","4th generaci\xF3n","5th generaci\xF3n","6th generaci\xF3n","acompasada","alto nivel","amplio \xE1banico","analizada","asim\xE9trica","as\xEDncrona","basado en contenido","basado en el contexto","basado en necesidades","bidireccional","bifurcada","cliente servidor","coherente","cohesiva","compuesto","dedicada","defectos cero","didactica","din\xE1mica","direccional","discreta","ejecutiva","escalable","estable","estatica","expl\xEDcita","generada por el cliente","generado por la demanda","global","heur\xEDstica","hibrida","hol\xEDstica","homog\xE9nea","incremental","innovadora","intangible","interactiva","intermedia","local","log\xEDstica","maximizada","met\xF3dica","misi\xF3n cr\xEDtica","modular","monitorizada por red","motivadora","multiestado","multimedia","multitarea","m\xF3bil","nacional","neutral","no-vol\xE1til","nueva generaci\xF3n","optimizada","orientada a soluciones","orientado a objetos","potenciada","radical","rec\xEDproca","regional","secundaria","sensible al contexto","sistem\xE1tica","sist\xE9mica","tangible","terciaria","tiempo real","tolerancia cero","tolerante a fallos","transicional","uniforme","valor a\xF1adido","v\xEDa web","\xF3ptima"];var C=["Hermanos","S.A.","S.L.","e Hijos"];var f=["{{person.last_name.generic}} y {{person.last_name.generic}}","{{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}} {{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}}, {{person.last_name.generic}} y {{person.last_name.generic}} Asociados"];var z=["Interfaz Gr\xE1fica","Interfaz gr\xE1fico de usuario","Soporte","acceso","actitud","adaptador","algoritmo","alianza","analista","aplicaci\xF3n","aprovechar","archivo","arquitectura","arquitectura abierta","array","base de datos","base de trabajo","base del conocimiento","caja de herramientas","capacidad","circuito","codificar","colaboraci\xF3n","complejidad","concepto","conglomeraci\xF3n","conjunto","conjunto de instrucciones","contingencia","data-warehouse","definici\xF3n","desaf\xEDo","emulaci\xF3n","encriptar","enfoque","estandardizaci\xF3n","estrategia","estructura","estructura de precios","extranet","fidelidad","firmware","flexibilidad","focus group","fuerza de trabajo","funcionalidad","funci\xF3n","gesti\xF3n presupuestaria","groupware","habilidad","hardware","implementaci\xF3n","infraestructura","iniciativa","instalaci\xF3n","inteligencia artificial","interfaz","intranet","jerarqu\xEDa","l\xEDnea segura","marco de tiempo","matrices","mediante","medici\xF3n","metodolog\xEDas","middleware","migraci\xF3n","modelo","moderador","monitorizar","n\xFAcleo","orquestar","paradigma","paralelismo","pol\xEDtica","portal","previsi\xF3n","proceso de mejora","productividad","producto","protocolo","proyecci\xF3n","proyecto","red de area local","sinergia","sistema abierto","software","soluci\xF3n","soporte","superestructura","utilizaci\xF3n","website","\xE9xito"];var ta={adjective:u,buzz_adjective:m,buzz_noun:p,buzz_verb:g,descriptor:b,legal_entity_type:C,name_pattern:f,noun:z},M=ta;var A=["com","com.mx","gob.mx","info","mx","org"];var v=["corpfolder.com","gmail.com","hotmail.com","nearbpo.com","yahoo.com"];var sa={domain_suffix:A,free_email:v},S=sa;var G=[" s/n.",", #",", ##"," #"," ##"," ###"," ####"];var E=["Aguascalientes","Apodaca","Buenavista","Campeche","Canc\xFAn","C\xE1rdenas","Celaya","Chalco","Chetumal","Chicoloapan","Chignahuapan","Chihuahua","Chilpancingo","Chimalhuac\xE1n","Ciudad Acu\xF1a","Ciudad de M\xE9xico","Ciudad del Carmen","Ciudad L\xF3pez Mateos","Ciudad Madero","Ciudad Obreg\xF3n","Ciudad Valles","Ciudad Victoria","Coatzacoalcos","Colima-Villa de \xC1lvarez","Comit\xE1n de Dominguez","C\xF3rdoba","Cuautitl\xE1n Izcalli","Cuautla","Cuernavaca","Culiac\xE1n","Delicias","Durango","Ensenada","Fresnillo","General Escobedo","G\xF3mez Palacio","Guadalajara","Guadalupe","Guanajuato","Guaymas","Hermosillo","Hidalgo del Parral","Iguala","Irapuato","Ixtapaluca","Jiutepec","Ju\xE1rez","La Laguna","La Paz","La Piedad-P\xE9njamo","Le\xF3n","Los Cabos","Los Mochis","Manzanillo","Matamoros","Mazatl\xE1n","M\xE9rida","Mexicali","Minatitl\xE1n","Miramar","Monclova","Monclova-Frontera","Monterrey","Morelia","Naucalpan de Ju\xE1rez","Navojoa","Nezahualc\xF3yotl","Nogales","Nuevo Laredo","Oaxaca","Ocotl\xE1n","Ojo de agua","Orizaba","Pachuca","Piedras Negras","Poza Rica","Puebla","Puerto Vallarta","Quer\xE9taro","Reynosa-R\xEDo Bravo","Rioverde-Ciudad Fern\xE1ndez","Salamanca","Saltillo","San Cristobal de las Casas","San Francisco Coacalco","San Francisco del Rinc\xF3n","San Juan Bautista Tuxtepec","San Juan del R\xEDo","San Luis Potos\xED-Soledad","San Luis R\xEDo Colorado","San Nicol\xE1s de los Garza","San Pablo de las Salinas","San Pedro Garza Garc\xEDa","Santa Catarina","Soledad de Graciano S\xE1nchez","Tampico-P\xE1nuco","Tapachula","Tecom\xE1n","Tehuac\xE1n","Tehuantepec-Salina Cruz","Tepexpan","Tepic","Tetela de Ocampo","Texcoco de Mora","Tijuana","Tlalnepantla","Tlaquepaque","Tlaxcala-Apizaco","Toluca","Tonal\xE1","Torre\xF3n","Tula","Tulancingo","Tulancingo de Bravo","Tuxtla Guti\xE9rrez","Uruapan","Uruapan del Progreso","Valle de M\xE9xico","Veracruz","Villa de \xC1lvarez","Villa Nicol\xE1s Romero","Villahermosa","Xalapa","Zacatecas-Guadalupe","Zacatlan","Zacatzingo","Zamora-Jacona","Zapopan","Zitacuaro"];var B=["{{location.city_name}}"];var D=["Afganist\xE1n","Albania","Argelia","Andorra","Angola","Argentina","Armenia","Aruba","Australia","Austria","Azerbay\xE1n","Bahamas","Barein","Bangladesh","Barbados","Bielorusia","B\xE9lgica","Belice","Bermuda","But\xE1n","Bolivia","Bosnia Herzegovina","Botswana","Brasil","Bulgaria","Burkina Faso","Burundi","Camboya","Camer\xFAn","Canada","Cabo Verde","Islas Caim\xE1n","Chad","Chile","China","Isla de Navidad","Colombia","Comodos","Congo","Costa Rica","Costa de Marfil","Croacia","Cuba","Chipre","Rep\xFAblica Checa","Dinamarca","Dominica","Rep\xFAblica Dominicana","Ecuador","Egipto","El Salvador","Guinea Ecuatorial","Eritrea","Estonia","Etiop\xEDa","Islas Faro","Fiji","Finlandia","Francia","Gab\xF3n","Gambia","Georgia","Alemania","Ghana","Grecia","Groenlandia","Granada","Guadalupe","Guam","Guatemala","Guinea","Guinea-Bisau","Guayana","Haiti","Honduras","Hong Kong","Hungria","Islandia","India","Indonesia","Iran","Irak","Irlanda","Italia","Jamaica","Jap\xF3n","Jordania","Kazajistan","Kenia","Kiribati","Corea","Kuwait","Letonia","L\xEDbano","Liberia","Liechtenstein","Lituania","Luxemburgo","Macao","Macedonia","Madagascar","Malawi","Malasia","Maldivas","Mali","Malta","Martinica","Mauritania","M\xE9xico","Micronesia","Moldavia","M\xF3naco","Mongolia","Montenegro","Montserrat","Marruecos","Mozambique","Namibia","Nauru","Nepal","Pa\xEDses Bajos","Nueva Zelanda","Nicaragua","Niger","Nigeria","Noruega","Om\xE1n","Pakistan","Panam\xE1","Pap\xFAa Nueva Guinea","Paraguay","Per\xFA","Filipinas","Poland","Portugal","Puerto Rico","Rusia","Ruanda","Samoa","San Marino","Santo Tom\xE9 y Principe","Arabia Saud\xED","Senegal","Serbia","Seychelles","Sierra Leona","Singapur","Eslovaquia","Eslovenia","Somalia","Espa\xF1a","Sri Lanka","Sud\xE1n","Suriname","Suecia","Suiza","Siria","Taiwan","Tajikistan","Tanzania","Tailandia","Timor-Leste","Togo","Tonga","Trinidad y Tobago","Tunez","Turquia","Uganda","Ucrania","Emiratos \xC1rabes Unidos","Reino Unido","Estados Unidos de Am\xE9rica","Uruguay","Uzbekistan","Vanuatu","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"];var P=["#####"];var h=["Esc. ###","Puerta ###","Edificio #"];var R=["Aguascalientes","Baja California Norte","Baja California Sur","Estado de M\xE9xico","Campeche","Chiapas","Chihuahua","Coahuila","Colima","Durango","Guanajuato","Guerrero","Hidalgo","Jalisco","Michoacan","Morelos","Nayarit","Nuevo Le\xF3n","Oaxaca","Puebla","Quer\xE9taro","Quintana Roo","San Luis Potos\xED","Sinaloa","Sonora","Tabasco","Tamaulipas","Tlaxcala","Veracruz","Yucat\xE1n","Zacatecas"];var x=["AS","BC","BS","CC","CS","CH","CL","CM","DF","DG","GT","GR","HG","JC","MC","MN","MS","NT","NL","OC","PL","QT","QR","SP","SL","SR","TC","TS","TL","VZ","YN","ZS"];var I={normal:"{{location.street}}{{location.buildingNumber}}",full:"{{location.street}}{{location.buildingNumber}} {{location.secondaryAddress}}"};var L=["20 de Noviembre","Cinco de Mayo","Cuahutemoc","Manzanares","Donceles","Francisco I. Madero","Ju\xE1rez","Rep\xFAplica de Cuba","Rep\xFAplica de Chile","Rep\xFAplica de Argentina","Rep\xFAplica de Uruguay","Isabel la Cat\xF3lica","Izazaga","Eje Central","Eje 6","Eje 5","La viga","Aniceto Ortega","Miguel \xC1ngel de Quevedo","Amores","Coyoac\xE1n","Coru\xF1a","Batalla de Naco","La otra banda","Piedra del Comal","Balc\xF3n de los edecanes","Barrio la Lonja","Jicolapa","Zacatl\xE1n","Zapata","Polotitlan","Calimaya","Flor Marina","Flor Solvestre","San Miguel","Naranjo","Cedro","Jalisco","Avena"];var y=["{{location.street_suffix}} {{person.first_name.generic}}","{{location.street_suffix}} {{person.first_name.generic}} {{person.last_name.generic}}","{{location.street_suffix}} {{location.street_name}}"];var j=["Aldea","Apartamento","Arrabal","Arroyo","Avenida","Bajada","Barranco","Barrio","Bloque","Calle","Calleja","Camino","Carretera","Caserio","Colegio","Colonia","Conjunto","Cuesta","Chalet","Edificio","Entrada","Escalinata","Explanada","Extramuros","Extrarradio","Ferrocarril","Glorieta","Gran Subida","Grupo","Huerta","Jardines","Lado","Lugar","Manzana","Mas\xEDa","Mercado","Monte","Muelle","Municipio","Parcela","Parque","Partida","Pasaje","Paseo","Plaza","Poblado","Pol\xEDgono","Prolongaci\xF3n","Puente","Puerta","Quinta","Ramal","Rambla","Rampa","Riera","Rinc\xF3n","Ronda","Rua","Salida","Sector","Secci\xF3n","Senda","Solar","Subida","Terrenos","Torrente","Traves\xEDa","Urbanizaci\xF3n","V\xEDa","V\xEDa P\xFAblica"];var ca={building_number:G,city_name:E,city_pattern:B,country:D,postcode:P,secondary_address:h,state:R,state_abbr:x,street_address:I,street_name:L,street_pattern:y,street_suffix:j},J=ca;var F=["Abacalero","Abacer\xEDa","Abacero","Abacial","Abaco","Abacora","Abacorar","Abad","Abada","Abadejo","Abadengo","Abadernar","Abadesa","Abad\xED","Abad\xEDa","Abadiado","Abadiato","Abajadero","Abajamiento","Abajar","Abaje\xF1o","Abajera","Abajo","Abalada","Abalanzar","Abalar","Abalaustrado","Abaldonadamente","Abaldonamiento","Bastonada","Bastonazo","Bastoncillo","Bastonear","Bastonero","B\xE1stulo","Basura","Basural","Basurear","Basurero","Bata","Batacazo","Batahola","Batal\xE1n","Batalla","Batallador","Batallar","Batallaroso","Batallola","Batall\xF3n","Batallona","Batalloso","Bat\xE1n","Batanar","Batanear","Batanero","Batanga","Bataola","Batata","Batatazo","Batato","Batavia","B\xE1tavo","Batayola","Batazo","Bate","Batea","Bateador","Bateaguas","Cenagar","Cenagoso","Cenal","Cenaoscuras","Ce\xF1ar","Cenata","Cenca","Cencapa","Cencellada","Cence\xF1ada","Cence\xF1o","Cencero","Cencerra","Cencerrada","Cencerrado","Cencerrear","Cencerreo","Cencerril","Cencerrillas","Cencerro","Cencerr\xF3n","Cencha","Cencido","Cenc\xEDo","Cencivera","Cenco","Cencuate","Cendal","Cendal\xED","C\xE9ndea","Cendolilla","Cendra","Cendrada","Cendradilla","Cendrado","Cendrar","Cendrazo","Cenefa","Cenegar","Ceneque","Cenero","Cenestesia","Desce\xF1ir","Descensi\xF3n","Descenso","Descentrado","Descentralizaci\xF3n","Descentralizador","Descentralizar","Descentrar","Descepar","Descerar","Descercado","Descercador","Descercar","Descerco","Descerebraci\xF3n","Descerebrado","Descerebrar","Descerezar","Descerrajado","Descerrajadura","Descerrajar","Descerrar","Descerrumarse","Descervigamiento","Descervigar","Deschapar","Descharchar","Deschavetado","Deschavetarse","Deschuponar","Descifrable","Descifrador","Desciframiento","Descifrar","Descifre","Descimbramiento","Descimbrar","Engarbarse","Engarberar","Engarbullar","Engarce","Engarfiar","Engargantadura","Engargantar","Engargante","Engargolado","Engargolar","Engaritar","Engarmarse","Engarnio","Engarrafador","Engarrafar","Engarrar","Engarro","Engarronar","Engarrotar","Engarzador","Engarzadura","Engarzar","Engasgarse","Engastador","Engastadura","Engastar","Engaste","Ficci\xF3n","Fice","Ficha","Fichaje","Fichar","Fichero","Ficoideo","Ficticio","Fidalgo","Fidecomiso","Fidedigno","Fideero","Fideicomisario","Fideicomiso","Fideicomitente","Fide\xEDsmo","Fidelidad","Fidel\xEDsimo","Fideo","Fido","Fiducia","Geminaci\xF3n","Geminado","Geminar","G\xE9minis","G\xE9mino","Gem\xEDparo","Gemiquear","Gemiqueo","Gemir","Gemolog\xEDa","Gemol\xF3gico","Gem\xF3logo","Gemonias","Gemoso","Gemoterapia","Gen","Genciana","Gencian\xE1ceo","Gencianeo","Gendarme","Gendarmer\xEDa","Genealog\xEDa","Geneal\xF3gico","Genealogista","Genearca","Gene\xE1tico","Generable","Generaci\xF3n","Generacional","Generador","General","Generala","Generalato","Generalidad","General\xEDsimo","Incordio","Incorporaci\xF3n","Incorporal","Incorporalmente","Incorporar","Incorporeidad","Incorp\xF3reo","Incorporo","Incorrecci\xF3n","Incorrectamente","Incorrecto","Incorregibilidad","Incorregible","Incorregiblemente","Incorrupci\xF3n","Incorruptamente","Incorruptibilidad","Incorruptible","Incorrupto","Incrasar","Increado","Incredibilidad","Incr\xE9dulamente","Incredulidad","Incr\xE9dulo","Incre\xEDble","Incre\xEDblemente","Incrementar","Incremento","Increpaci\xF3n","Increpador","Increpar","Incriminaci\xF3n","Incriminar","Incristalizable","Incruentamente","Incruento","Incrustaci\xF3n"];var da={word:F},V=da;var ua={title:"Spanish (Mexico)",code:"es_MX",country:"MX",language:"es",endonym:"Espa\xF1ol (M\xE9xico)",dir:"ltr",script:"Latn"},T=ua;var N={generic:["Aar\xF3n","Abigail","Abraham","Abril","Adela","Adriana","Ad\xE1n","Agust\xEDn","Alan","Alberto","Alejandra","Alejandro","Alexa","Alexander","Alexis","Alfonso","Alfredo","Alicia","Alondra Romina","Amalia","Ana","Ana Luisa","Ana Mar\xEDa","Ana Sof\xEDa","Ana Victoria","Andrea","Andr\xE9s","Anita","Antonia","Antonio","Araceli","Ariadna","Armando","Arturo","Axel","Barbara","Beatriz","Benito","Benjam\xEDn","Bernardo","Berta","Blanca","Brandon","Brayan","Camila","Caridad","Carla","Carlos","Carlota","Carmen","Carolina","Catalina","Cecilia","Clara","Claudia","Claudio","Clemente","Concepci\xF3n","Conchita","Cristian","Cristina","Cristobal","C\xE9sar","Dami\xE1n","Daniel","Daniela","David","Diana","Diego","Dolores","Dorotea","Dulce Mar\xEDa","D\xE9bora","Eduardo","Elena","Elisa","Elizabeth","Eloisa","Elsa","Elvira","El\xEDas","Emilia","Emiliano","Emilio","Emily","Emmanuel","Enrique","Erick","Ernesto","Esmeralda","Esperanza","Esteban","Estefan\xEDa","Estela","Ester","Eva","Evelyn","Fatima","Federico","Felipe","Fernando","Fernando Javier","Florencia","Francisca","Francisco","Francisco Javier","Gabriel","Gabriela","Gael","Gerardo","Germ\xE1n","Gilberto","Gloria","Gonzalo","Graciela","Gregorio","Guadalupe","Guillermina","Guillermo","Gustavo","Hern\xE1n","Homero","Horacio","Hugo","Ignacio","Iker","In\xE9s","Irene","Isaac","Isabel","Isabela","Isaias","Israel","Ivan","Ivanna","Jacobo","Jaime","Javier","Jazmin","Jennifer","Jer\xF3nimo","Jes\xFAs","Jimena","Joaqu\xEDn","Jorge","Jorge Luis","Jose Daniel","Josefina","Jos\xE9","Jos\xE9 Antonio","Jos\xE9 Eduardo","Jos\xE9 Emilio","Jos\xE9 Luis","Jos\xE9 Mar\xEDa","Jos\xE9 Miguel","Juan","Juan Carlos","Juan Manuel","Juan Pablo","Juana","Julia","Julieta","Julio","Julio C\xE9sar","Kevin","Kimberly","Laura","Leonardo","Leonor","Leticia","Lilia","Liliana","Lizbeth","Lola","Lorena","Lorenzo","Lourdes","Lucas","Lucia","Luis","Luis Fernando","Luis Gabino","Luis Miguel","Luis \xC1ngel","Luisa","Luz","Magdalena","Manuel","Manuela","Marcela","Marco Antonio","Marcos","Margarita","Mariana","Mariano","Maricarmen","Marilu","Mario","Marisol","Marta","Mart\xEDn","Mar\xEDa","Mar\xEDa Cristina","Mar\xEDa Elena","Mar\xEDa Eugenia","Mar\xEDa Fernanda","Mar\xEDa Guadalupe","Mar\xEDa Jos\xE9","Mar\xEDa Luisa","Mar\xEDa Soledad","Mar\xEDa Teresa","Mar\xEDa de Jes\xFAs","Mar\xEDa de los \xC1ngeles","Mar\xEDa del Carmen","Mateo","Mat\xEDas","Mauricio","Maximiliano","Mayte","Melany","Melissa","Mercedes","Micaela","Miguel","Miguel \xC1ngel","Miranda","Monserrat","M\xF3nica","Naomi","Natalia","Nicole","Nicol\xE1s","Norma","Octavio","Olivia","Pablo","Paola","Patricia","Patricio","Paulina","Pedro","Pilar","Rafael","Ramiro","Ramona","Ram\xF3n","Raquel","Ra\xFAl","Rebeca","Regina","Reina","Renata","Ricardo","Roberto","Rocio","Rodrigo","Rosa","Rosa Mar\xEDa","Rosalia","Rosario","Rub\xE9n","Salvador","Samuel","Sancho","Santiago","Sara","Sa\xFAl","Sebastian","Sergio","Silvia","Sofia","Soledad","Sonia","Susana","Tadeo","Teodoro","Teresa","Timoteo","Tom\xE1s","Uriel","Valentina","Valeria","Vanessa","Ver\xF3nica","Vicente","Victor Manuel","Victoria","Virginia","V\xEDctor","Ximena","Ximena Guadalupe","Xochitl","Yamileth","Yaretzi","Yolanda","Zoe","\xC1ngel Daniel","\xC1ngel Gabriel","\xC1ngela","\xD3scar"],female:["Abigail","Abril","Adela","Adriana","Alejandra","Alexa","Alicia","Alondra Romina","Amalia","Ana","Ana Luisa","Ana Mar\xEDa","Ana Sof\xEDa","Ana Victoria","Andrea","Anita","Antonia","Araceli","Ariadna","Barbara","Beatriz","Berta","Blanca","Camila","Caridad","Carla","Carlota","Carmen","Carolina","Catalina","Cecilia","Clara","Claudia","Concepci\xF3n","Conchita","Cristina","Daniela","Diana","Dolores","Dorotea","Dulce Mar\xEDa","D\xE9bora","Elena","Elisa","Elizabeth","Eloisa","Elsa","Elvira","Emilia","Emily","Esmeralda","Esperanza","Estefan\xEDa","Estela","Ester","Eva","Evelyn","Fatima","Florencia","Francisca","Gabriela","Gloria","Graciela","Guadalupe","Guillermina","In\xE9s","Irene","Isabel","Isabela","Ivanna","Jazmin","Jennifer","Jimena","Josefina","Juana","Julia","Julieta","Kimberly","Laura","Leonor","Leticia","Lilia","Liliana","Lizbeth","Lola","Lorena","Lourdes","Lucia","Luisa","Luz","Magdalena","Manuela","Marcela","Margarita","Mariana","Maricarmen","Marilu","Marisol","Marta","Mar\xEDa","Mar\xEDa Cristina","Mar\xEDa Elena","Mar\xEDa Eugenia","Mar\xEDa Fernanda","Mar\xEDa Guadalupe","Mar\xEDa Jos\xE9","Mar\xEDa Luisa","Mar\xEDa Soledad","Mar\xEDa Teresa","Mar\xEDa de Jes\xFAs","Mar\xEDa de los \xC1ngeles","Mar\xEDa del Carmen","Mayte","Melany","Melissa","Mercedes","Micaela","Miranda","Monserrat","M\xF3nica","Naomi","Natalia","Nicole","Norma","Olivia","Paola","Patricia","Paulina","Pilar","Ramona","Raquel","Rebeca","Regina","Reina","Renata","Rocio","Rosa","Rosa Mar\xEDa","Rosalia","Rosario","Sara","Silvia","Sofia","Soledad","Sonia","Susana","Teresa","Valentina","Valeria","Vanessa","Ver\xF3nica","Victoria","Virginia","Ximena","Ximena Guadalupe","Xochitl","Yamileth","Yaretzi","Yolanda","Zoe","\xC1ngela"],male:["Aar\xF3n","Abraham","Ad\xE1n","Agust\xEDn","Alan","Alberto","Alejandro","Alexander","Alexis","Alfonso","Alfredo","Andr\xE9s","Antonio","Armando","Arturo","Axel","Benito","Benjam\xEDn","Bernardo","Brandon","Brayan","Carlos","Claudio","Clemente","Cristian","Cristobal","C\xE9sar","Dami\xE1n","Daniel","David","Diego","Eduardo","El\xEDas","Emiliano","Emilio","Emmanuel","Enrique","Erick","Ernesto","Esteban","Federico","Felipe","Fernando","Fernando Javier","Francisco","Francisco Javier","Gabriel","Gael","Gerardo","Germ\xE1n","Gilberto","Gonzalo","Gregorio","Guillermo","Gustavo","Hern\xE1n","Homero","Horacio","Hugo","Ignacio","Iker","Isaac","Isaias","Israel","Ivan","Jacobo","Jaime","Javier","Jer\xF3nimo","Jes\xFAs","Joaqu\xEDn","Jorge","Jorge Luis","Jose Daniel","Jos\xE9","Jos\xE9 Antonio","Jos\xE9 Eduardo","Jos\xE9 Emilio","Jos\xE9 Luis","Jos\xE9 Mar\xEDa","Jos\xE9 Miguel","Juan","Juan Carlos","Juan Manuel","Juan Pablo","Julio","Julio C\xE9sar","Kevin","Leonardo","Lorenzo","Lucas","Luis","Luis Fernando","Luis Gabino","Luis Miguel","Luis \xC1ngel","Manuel","Marco Antonio","Marcos","Mariano","Mario","Mart\xEDn","Mateo","Mat\xEDas","Mauricio","Maximiliano","Miguel","Miguel \xC1ngel","Nicol\xE1s","Octavio","Pablo","Patricio","Pedro","Rafael","Ramiro","Ram\xF3n","Ra\xFAl","Ricardo","Roberto","Rodrigo","Rub\xE9n","Salvador","Samuel","Sancho","Santiago","Sa\xFAl","Sebastian","Sergio","Tadeo","Teodoro","Timoteo","Tom\xE1s","Uriel","Vicente","Victor Manuel","V\xEDctor","\xC1ngel Daniel","\xC1ngel Gabriel","\xD3scar"]};var _=["Soluciones","Programa","Marca","Seguridad","Investigaci\xF3n","Marketing","Normas","Implementaci\xF3n","Integraci\xF3n","Funcionalidad","Respuesta","Paradigma","T\xE1cticas","Identidad","Mercados","Grupo","Divisi\xF3n","Aplicaciones","Optimizaci\xF3n","Operaciones","Infraestructura","Intranet","Comunicaciones","Web","Calidad","Seguro","Mobilidad","Cuentas","Datos","Creativo","Configuraci\xF3n","Contabilidad","Interacciones","Factores","Usabilidad","M\xE9tricas"];var O=["Jefe","Senior","Directo","Corporativo","Din\xE1nmico","Futuro","Producto","Nacional","Regional","Distrito","Central","Global","Cliente","Inversor","International","Heredado","Adelante","Interno","Humano","Gerente","SubGerente","Director"];var q=["Supervisor","Asociado","Ejecutivo","Relacciones","Oficial","Gerente","Ingeniero","Especialista","Director","Coordinador","Administrador","Arquitecto","Analista","Dise\xF1ador","Planificador","T\xE9cnico","Funcionario","Desarrollador","Productor","Consultor","Asistente","Facilitador","Agente","Representante","Estratega","Scrum Master","Scrum Owner","Product Owner","Scrum Developer"];var H={generic:["Abeyta","Abrego","Abreu","Acevedo","Acosta","Acu\xF1a","Adame","Adorno","Agosto","Aguayo","Aguilera","Aguirre","Alanis","Alaniz","Alarc\xF3n","Alba","Alcala","Alcaraz","Alc\xE1ntar","Alejandro","Alem\xE1n","Alfaro","Alicea","Almanza","Almaraz","Almonte","Alonso","Alonzo","Altamirano","Alva","Alvarado","Alvarez","Amador","Amaya","Anaya","Anguiano","Angulo","Aparicio","Apodaca","Aponte","Arag\xF3n","Aranda","Ara\xF1a","Arce","Archuleta","Arellano","Arenas","Arevalo","Arguello","Arias","Armas","Armend\xE1riz","Armenta","Armijo","Arredondo","Arreola","Arriaga","Arroyo","Arteaga","Atencio","Avil\xE9s","Ayala","Baca","Badillo","Baeza","Bahena","Balderas","Ballesteros","Banda","Barajas","Barela","Barrag\xE1n","Barraza","Barrera","Barreto","Barrientos","Barrios","Batista","Ba\xF1uelos","Becerra","Beltr\xE1n","Benavides","Benav\xEDdez","Ben\xEDtez","Berm\xFAdez","Bernal","Berr\xEDos","Blanco","Bonilla","Borrego","Botello","Bravo","Briones","Brise\xF1o","Brito","Bueno","Burgos","Bustamante","Bustos","B\xE1ez","B\xE9tancourt","Caballero","Cabrera","Cab\xE1n","Cadena","Caldera","Calder\xF3n","Calvillo","Camacho","Camarillo","Campos","Canales","Candelaria","Cano","Cant\xFA","Caraballo","Carbajal","Cardenas","Cardona","Carmona","Carranza","Carrasco","Carrasquillo","Carrera","Carrero","Carre\xF3n","Carrillo","Carrion","Carvajal","Casanova","Casares","Casas","Casillas","Casta\xF1eda","Castellanos","Castillo","Castro","Cas\xE1rez","Cavazos","Cazares","Ceballos","Cedillo","Ceja","Centeno","Cepeda","Cerda","Cervantes","Cerv\xE1ntez","Chac\xF3n","Chapa","Chavarr\xEDa","Ch\xE1vez","Cintr\xF3n","Cisneros","Collado","Collazo","Colunga","Col\xF3n","Concepci\xF3n","Contreras","Cordero","Cornejo","Corona","Coronado","Corral","Corrales","Correa","Cortez","Cort\xE9s","Cotto","Covarrubias","Crespo","Cruz","Cuellar","Curiel","C\xF3rdova","Delacr\xFAz","Delafuente","Delagarza","Delao","Delapaz","Delarosa","Delatorre","Dele\xF3n","Delgadillo","Delgado","Delr\xEDo","Delvalle","Dom\xEDnguez","Dom\xEDnquez","Duarte","Due\xF1as","Duran","D\xE1vila","D\xEDaz","Echevarr\xEDa","Elizondo","Enr\xEDquez","Escalante","Escamilla","Escobar","Escobedo","Esparza","Espinal","Espino","Espinosa","Espinoza","Esquibel","Esquivel","Estrada","Est\xE9vez","Fajardo","Far\xEDas","Feliciano","Fern\xE1ndez","Ferrer","Fierro","Figueroa","Flores","Fl\xF3rez","Fonseca","Franco","Fr\xEDas","Fuentes","Gait\xE1n","Galarza","Galindo","Gallardo","Gallegos","Galv\xE1n","Gamboa","Gamez","Gaona","Garay","Garc\xEDa","Garibay","Garica","Garrido","Garza","Gast\xE9lum","Gayt\xE1n","Gil","Gir\xF3n","Godoy","God\xEDnez","Gollum","Gonzales","Gonz\xE1lez","Gracia","Granado","Granados","Griego","Grijalva","Guajardo","Guardado","Guerra","Guerrero","Guevara","Guillen","Gurule","Guti\xE9rrez","Guzm\xE1n","G\xE1lvez","G\xF3mez","Haro","Henr\xEDquez","Heredia","Hernandes","Hern\xE1dez","Hern\xE1ndez","Herrera","Hidalgo","Hinojosa","Holgu\xEDn","Huerta","Huixtlacatl","Hurtado","Ibarra","Iglesias","Irizarry","Jaime","Jaimes","Jaramillo","Jasso","Jim\xE9nez","Jim\xEDnez","Jurado","Ju\xE1rez","J\xE1quez","Kadar rodriguez","Kamal","Kamat","Kanaria","Kanea","Kanimal","Kano","Kanzaki","Kaplan","Kara","Karam","Karan","Kardache soto","Karem","Karen","Khalid","Kindelan","Koenig","Korta","Korta hernandez","Kortajarena","Kranz sans","Krasnova","Krauel natera","Kuzmina","Kyra","Laboy","Lara","Laureano","Leal","Lebr\xF3n","Ledesma","Leiva","Lemus","Lerma","Leyva","Le\xF3n","Lim\xF3n","Linares","Lira","Llamas","Loera","Lomeli","Longoria","Lovato","Loya","Lozada","Lozano","Lucero","Lucio","Luevano","Lugo","Luna","L\xF3pez","Mac\xEDas","Madera","Madrid","Madrigal","Maestas","Maga\xF1a","Malave","Maldonado","Manzanares","Mares","Marrero","Marroqu\xEDn","Mart\xEDnez","Mar\xEDn","Mascare\xF1as","Mata","Mateo","Matos","Mat\xEDas","Maya","Mayorga","Medina","Medrano","Mej\xEDa","Melgar","Mel\xE9ndez","Mena","Menchaca","Mendoza","Men\xE9ndez","Meraz","Mercado","Merino","Mesa","Meza","Miramontes","Miranda","Mireles","Mojica","Molina","Mondrag\xF3n","Monroy","Montalvo","Monta\xF1ez","Monta\xF1o","Montemayor","Montenegro","Montero","Montes","Montez","Montoya","Mora","Morales","Moreno","Mota","Moya","Mungu\xEDa","Murillo","Muro","Mu\xF1iz","Mu\xF1oz","M\xE1rquez","M\xE9ndez","Naranjo","Narv\xE1ez","Nava","Navarrete","Navarro","Nazario","Negrete","Negr\xF3n","Nev\xE1rez","Nieto","Nieves","Ni\xF1o","Noriega","N\xE1jera","N\xFA\xF1ez","Ocampo","Ocasio","Ochoa","Ojeda","Olivares","Olivas","Olivera","Olivo","Oliv\xE1rez","Olmos","Olvera","Ontiveros","Oquendo","Ord\xF3\xF1ez","Orellana","Ornelas","Orosco","Orozco","Orta","Ortega","Ortiz","Osorio","Otero","Ozuna","Pab\xF3n","Pacheco","Padilla","Padr\xF3n","Pagan","Palacios","Palomino","Palomo","Pantoja","Paredes","Parra","Partida","Pati\xF1o","Paz","Pedraza","Pedroza","Pelayo","Perales","Peralta","Perea","Peres","Pe\xF1a","Pichardo","Pineda","Pizarro","Pi\xF1a","Polanco","Ponce","Porras","Portillo","Posada","Prado","Preciado","Prieto","Puente","Puga","Pulido","P\xE1ez","P\xE9rez","Quesada","Quevedo","Quezada","Quinta","Quintairos","Quintana","Quintanilla","Quintero","Quintero cruz","Quintero de la cruz","Quiros","Quiroz","Qui\xF1ones","Qui\xF1\xF3nez","Rael","Ramos","Ram\xEDrez","Ram\xF3n","Rangel","Rasc\xF3n","Raya","Razo","Regalado","Rend\xF3n","Renter\xEDa","Res\xE9ndez","Reyes","Reyna","Reynoso","Rico","Rinc\xF3n","Riojas","Rivas","Rivera","Rivero","Robledo","Robles","Rocha","Rodarte","Rodr\xEDgez","Rodr\xEDguez","Rodr\xEDquez","Rojas","Rojo","Rold\xE1n","Rol\xF3n","Romero","Romo","Roque","Rosado","Rosales","Rosario","Rosas","Roybal","Rubio","Ruelas","Ruiz","R\xEDos","Saavedra","Saiz","Salas","Salazar","Salcedo","Salcido","Salda\xF1a","Saldivar","Salgado","Salinas","Samaniego","Sanabria","Sanches","Sandoval","Santacruz","Santana","Santiago","Santill\xE1n","Sarabia","Sauceda","Saucedo","Sedillo","Segovia","Segura","Sep\xFAlveda","Serna","Serrano","Serrato","Sevilla","Sierra","Sisneros","Solano","Soliz","Solorio","Solorzano","Sol\xEDs","Soria","Sosa","Sotelo","Soto","Su\xE1rez","S\xE1enz","S\xE1nchez","Tafoya","Tamayo","Tamez","Tapia","Tejada","Tejeda","Tello","Terrazas","Ter\xE1n","Tijerina","Tirado","Toledo","Toro","Torres","Tovar","Trejo","Trevi\xF1o","Trujillo","T\xE9llez","T\xF3rrez","Ulibarri","Ulloa","Urbina","Ure\xF1a","Uribe","Urrutia","Ur\xEDas","Vaca","Valadez","Valdez","Valdivia","Vald\xE9s","Valencia","Valent\xEDn","Valenzuela","Valladares","Valle","Vallejo","Valles","Valverde","Vanegas","Varela","Vargas","Vega","Vela","Velasco","Vel\xE1squez","Vel\xE1zquez","Venegas","Vera","Verdugo","Verduzco","Vergara","Viera","Vigil","Villa","Villag\xF3mez","Villalobos","Villalpando","Villanueva","Villareal","Villarreal","Villase\xF1or","Villegas","V\xE1squez","V\xE1zquez","V\xE9lez","V\xE9liz","Xacon","Xairo Belmonte","Xana","Xenia","Xiana","Xicoy","Yago","Yami","Yanes","Ybarra","Yebra","Yunta","Y\xE1\xF1ez","Zabaleta","Zamarreno","Zamarripa","Zambrana","Zambrano","Zamora","Zamudio","Zapata","Zaragoza","Zarate","Zavala","Zayas","Zelaya","Zepeda","Z\xFA\xF1iga","de Anda","de Jes\xFAs","\xC1guilar","\xC1valos","\xC1vila","\xD1a\xF1ez"]};var K={generic:[{value:"{{person.last_name.generic}} {{person.last_name.generic}}",weight:5},{value:"{{person.last_name.generic}} de {{person.last_name.generic}}",weight:1}]};var Z=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:8},{value:"{{person.firstName}} {{person.lastName}} {{person.suffix}}",weight:1}];var w={generic:["Sr.","Sra.","Sta."],female:["Sra.","Sta."],male:["Sr."]};var U=["Jr.","Sr.","I","II","III","IV","V","MD","DDS","PhD","DVM","Ing.","Lic.","Dr.","Mtro."];var ma={first_name:N,job_area:_,job_descriptor:O,job_type:q,last_name:H,last_name_pattern:K,name:Z,prefix:w,suffix:U},Q=ma;var k=["5###-###-###","5##.###.###","5## ### ###","5########"];var X=["+525#########","+525########"];var Y=["5## ### ####","5########"];var pa={human:k,international:X,national:Y},W=pa;var ga={format:W},$=ga;var aa=["hormigas","murci\xE9lagos","osos","abejas","p\xE1jaros","b\xFAfalo","gatos","pollos","ganado","perros","delfines","patos","elefantes","peces","zorros","ranas","gansos","cabras","caballos","canguros","leones","monos","b\xFAhos","bueyes","ping\xFCinos","pueblo","cerdos","conejos","ovejas","tigres","ballenas","lobos","cebras","almas en pena","cuervos","gatos negros","quimeras","fantasmas","conspiradores","dragones","enanos","duendes","encantadores","exorcistas","hijos","enemigos","gigantes","gnomos","grifos","lic\xE1ntropos","n\xE9mesis","ogros","or\xE1culos","profetas","hechiceros","ara\xF1as","esp\xEDritus","vampiros","brujos","zorras","hombres lobo","brujas","adoradores","zombies","druidas"];var ea=["{{location.state}} {{team.creature}}"];var ba={creature:aa,name:ea},ra=ba;var Ca={cell_phone:n,color:t,commerce:d,company:M,internet:S,location:J,lorem:V,metadata:T,person:Q,phone_number:$,team:ra},oa=Ca;var vr=new a({locale:[oa,o,r,e]});export{oa as a,vr as b};
