const { faker } = require('@faker-js/faker');
const config = require('../config/test.config');

class ScreenFactory {
  /**
   * Create custom screen data
   */
  static createCustomScreen() {
    const timestamp = Date.now();

    return {
      name: `test_screen_${timestamp}`,
      title: `Test Screen ${timestamp}`,
      route: `/test-screen-${timestamp}`,
      description: 'Custom screen created for testing',
      icon: 'test_tube',
      requiredPermissions: ['dashboard.view'],
      allowedRoles: ['admin'],
      isEnabled: true,
      showInNavigation: true,
      priority: 10
    };
  }

  /**
   * Create reports screen
   */
  static createReportsScreen() {
    const timestamp = Date.now();

    return {
      name: `custom_reports_${timestamp}`,
      title: 'Custom Reports',
      route: `/custom-reports-${timestamp}`,
      description: 'Custom reporting dashboard',
      icon: 'assessment',
      requiredPermissions: ['reports.generate', 'reports.export'],
      allowedRoles: ['admin', 'property_manager', 'reporting_specialist'],
      isEnabled: true,
      showInNavigation: true,
      priority: 15
    };
  }

  /**
   * Create analytics screen
   */
  static createAnalyticsScreen() {
    const timestamp = Date.now();

    return {
      name: `analytics_${timestamp}`,
      title: 'Analytics Dashboard',
      route: `/analytics-${timestamp}`,
      description: 'Advanced analytics and insights',
      icon: 'analytics',
      requiredPermissions: ['reports.generate', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager'],
      isEnabled: true,
      showInNavigation: true,
      priority: 20
    };
  }

  /**
   * Create admin-only screen
   */
  static createAdminOnlyScreen() {
    const timestamp = Date.now();

    return {
      name: `admin_only_${timestamp}`,
      title: 'Admin Only Screen',
      route: `/admin-only-${timestamp}`,
      description: 'Screen accessible only to administrators',
      icon: 'admin_panel_settings',
      requiredPermissions: ['users.create', 'roles.create'],
      allowedRoles: ['admin'],
      isEnabled: true,
      showInNavigation: true,
      priority: 5
    };
  }

  /**
   * Create maintenance screen
   */
  static createMaintenanceScreen() {
    const timestamp = Date.now();

    return {
      name: `maintenance_dashboard_${timestamp}`,
      title: 'Maintenance Dashboard',
      route: `/maintenance-dashboard-${timestamp}`,
      description: 'Specialized maintenance management interface',
      icon: 'build',
      requiredPermissions: ['maintenance.read', 'maintenance.create'],
      allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
      isEnabled: true,
      showInNavigation: true,
      priority: 12
    };
  }

  /**
   * Create security screen
   */
  static createSecurityScreen() {
    const timestamp = Date.now();

    return {
      name: `security_dashboard_${timestamp}`,
      title: 'Security Dashboard',
      route: `/security-dashboard-${timestamp}`,
      description: 'Security monitoring and incident management',
      icon: 'security',
      requiredPermissions: ['security.read', 'incidents.manage'],
      allowedRoles: ['admin', 'security_supervisor'],
      isEnabled: true,
      showInNavigation: true,
      priority: 18
    };
  }

  /**
   * Create screen with minimal permissions
   */
  static createMinimalScreen() {
    const timestamp = Date.now();

    return {
      name: `minimal_screen_${timestamp}`,
      title: 'Minimal Screen',
      route: `/minimal-screen-${timestamp}`,
      description: 'Screen with minimal permission requirements',
      icon: 'visibility',
      requiredPermissions: ['dashboard.view'],
      allowedRoles: ['admin', 'property_manager', 'maintenance_staff', 'viewer'],
      isEnabled: true,
      showInNavigation: true,
      priority: 25
    };
  }

  /**
   * Create screen with multiple permission requirements
   */
  static createMultiPermissionScreen() {
    const timestamp = Date.now();

    return {
      name: `multi_permission_screen_${timestamp}`,
      title: 'Multi Permission Screen',
      route: `/multi-permission-screen-${timestamp}`,
      description: 'Screen requiring multiple permissions',
      icon: 'verified_user',
      requiredPermissions: [
        'properties.read',
        'maintenance.read',
        'reports.generate'
      ],
      allowedRoles: ['admin', 'property_manager'],
      isEnabled: true,
      showInNavigation: true,
      priority: 8,
      requireAllPermissions: true
    };
  }

  /**
   * Create disabled screen
   */
  static createDisabledScreen() {
    const timestamp = Date.now();

    return {
      name: `disabled_screen_${timestamp}`,
      title: 'Disabled Screen',
      route: `/disabled-screen-${timestamp}`,
      description: 'Screen that is disabled for testing',
      icon: 'block',
      requiredPermissions: ['dashboard.view'],
      allowedRoles: ['admin'],
      isEnabled: false,
      showInNavigation: false,
      priority: 30
    };
  }

  /**
   * Create screen with invalid data for validation testing
   */
  static createInvalid() {
    return {
      name: '', // Invalid: empty name
      title: 'Invalid Screen',
      route: '/invalid',
      description: 'Invalid screen for testing validation',
      icon: 'error',
      requiredPermissions: [],
      allowedRoles: [],
      isEnabled: true,
      showInNavigation: true,
      priority: 0
    };
  }

  /**
   * Create screen with duplicate route for testing
   */
  static createDuplicate(existingRoute) {
    const timestamp = Date.now();

    return {
      name: `duplicate_screen_${timestamp}`,
      title: 'Duplicate Route Screen',
      route: existingRoute, // Duplicate route
      description: 'Screen with duplicate route for testing',
      icon: 'content_copy',
      requiredPermissions: ['dashboard.view'],
      allowedRoles: ['admin'],
      isEnabled: true,
      showInNavigation: true,
      priority: 10
    };
  }

  /**
   * Create multiple screens for bulk operations
   */
  static createBulkScreens(count) {
    const screens = [];

    for (let i = 0; i < count; i++) {
      const timestamp = Date.now() + i;

      screens.push({
        name: `bulk_screen_${i + 1}_${timestamp}`,
        title: `Bulk Screen ${i + 1}`,
        route: `/bulk-screen-${i + 1}-${timestamp}`,
        description: `Bulk screen ${i + 1} for testing`,
        icon: 'apps',
        requiredPermissions: ['dashboard.view'],
        allowedRoles: ['admin'],
        isEnabled: true,
        showInNavigation: true,
        priority: 20 + i
      });
    }

    return screens;
  }

  /**
   * Create screen update data
   */
  static createUpdateData() {
    return {
      title: `Updated Screen Title ${Date.now()}`,
      description: `Updated description ${Date.now()}`,
      requiredPermissions: [
        'properties.read',
        'maintenance.read',
        'reports.generate'
      ],
      allowedRoles: ['admin', 'property_manager'],
      priority: 15
    };
  }

  /**
   * Create screen for specific test scenario
   */
  static createForScenario(scenario) {
    const timestamp = Date.now();

    const baseScreen = {
      name: `${scenario}_screen_${timestamp}`,
      title: `${scenario} Screen`,
      route: `/${scenario}-screen-${timestamp}`,
      description: `Screen created for ${scenario} testing`,
      icon: 'science',
      isEnabled: true,
      showInNavigation: true,
      priority: 10
    };

    switch (scenario.toLowerCase()) {
      case 'permission_testing':
        return {
          ...baseScreen,
          requiredPermissions: ['properties.read', 'maintenance.read'],
          allowedRoles: ['admin', 'property_manager']
        };
      case 'role_testing':
        return {
          ...baseScreen,
          requiredPermissions: ['dashboard.view'],
          allowedRoles: ['admin', 'property_manager', 'maintenance_staff']
        };
      case 'access_control':
        return {
          ...baseScreen,
          requiredPermissions: ['users.read', 'roles.read'],
          allowedRoles: ['admin']
        };
      case 'multi_role':
        return {
          ...baseScreen,
          requiredPermissions: ['reports.generate'],
          allowedRoles: ['admin', 'property_manager', 'reporting_specialist']
        };
      default:
        return {
          ...baseScreen,
          requiredPermissions: ['dashboard.view'],
          allowedRoles: ['admin']
        };
    }
  }

  /**
   * Generate random screen data
   */
  static generateRandom() {
    const timestamp = Date.now();
    const icons = ['dashboard', 'analytics', 'assessment', 'build', 'security', 'visibility'];
    const permissions = [
      'properties.read', 'maintenance.read', 'dashboard.view',
      'reports.generate', 'attendance.read', 'fuel.read'
    ];
    const roles = ['admin', 'property_manager', 'maintenance_staff', 'viewer'];

    // Select random subset of permissions and roles
    const numPermissions = Math.floor(Math.random() * 3) + 1;
    const numRoles = Math.floor(Math.random() * 3) + 1;

    const selectedPermissions = [];
    const selectedRoles = [];

    for (let i = 0; i < numPermissions; i++) {
      const randomPermission = permissions[Math.floor(Math.random() * permissions.length)];
      if (!selectedPermissions.includes(randomPermission)) {
        selectedPermissions.push(randomPermission);
      }
    }

    for (let i = 0; i < numRoles; i++) {
      const randomRole = roles[Math.floor(Math.random() * roles.length)];
      if (!selectedRoles.includes(randomRole)) {
        selectedRoles.push(randomRole);
      }
    }

    return {
      name: `random_screen_${timestamp}`,
      title: faker.lorem.words(2),
      route: `/random-screen-${timestamp}`,
      description: faker.lorem.sentence(),
      icon: icons[Math.floor(Math.random() * icons.length)],
      requiredPermissions: selectedPermissions,
      allowedRoles: selectedRoles,
      isEnabled: faker.datatype.boolean(),
      showInNavigation: faker.datatype.boolean(),
      priority: Math.floor(Math.random() * 30) + 1
    };
  }
}

module.exports = ScreenFactory;
