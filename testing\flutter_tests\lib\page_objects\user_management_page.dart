import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../models/user_data.dart';

class UserManagementPage {
  final WidgetTester tester;
  static final _logger = Logger();

  UserManagementPage(this.tester);

  // Main screen locators
  Finder get screen => find.byKey(const Key('user_management_screen'));
  Finder get addUserFab => find.byKey(const Key('add_user_fab'));
  Finder get usersList => find.byKey(const Key('users_list'));
  Finder get searchField => find.byKey(const Key('search_field'));
  Finder get filterButton => find.byKey(const Key('filter_button'));
  Finder get refreshButton => find.byKey(const Key('refresh_button'));

  // Tab locators
  Finder get allUsersTab => find.text('All Users');
  Finder get pendingApprovalTab => find.text('Pending Approval');
  Finder get activeUsersTab => find.text('Active');
  Finder get inactiveUsersTab => find.text('Inactive');

  // Create user dialog locators
  Finder get createUserDialog => find.byKey(const Key('create_user_dialog'));
  Finder get fullNameField => find.byKey(const Key('full_name_field'));
  Finder get emailField => find.byKey(const Key('email_field'));
  Finder get usernameField => find.byKey(const Key('username_field'));
  Finder get phoneField => find.byKey(const Key('phone_field'));
  Finder get passwordField => find.byKey(const Key('password_field'));
  Finder get confirmPasswordField => find.byKey(const Key('confirm_password_field'));
  Finder get createUserButton => find.text('Create User');
  Finder get cancelButton => find.text('Cancel');

  // Role selection locators
  Finder get roleSelectionSection => find.byKey(const Key('role_selection_section'));
  Finder roleCheckbox(String role) => find.byKey(Key('role_checkbox_$role'));

  // User actions locators
  Finder editUserButton(String email) => find.byKey(Key('edit_user_$email'));
  Finder deleteUserButton(String email) => find.byKey(Key('delete_user_$email'));
  Finder approveUserButton(String email) => find.byKey(Key('approve_user_$email'));
  Finder rejectUserButton(String email) => find.byKey(Key('reject_user_$email'));
  Finder activateUserButton(String email) => find.byKey(Key('activate_user_$email'));
  Finder deactivateUserButton(String email) => find.byKey(Key('deactivate_user_$email'));

  // Verification locators
  Finder get successMessage => find.byKey(const Key('success_message'));
  Finder get errorMessage => find.byKey(const Key('error_message'));
  Finder userCard(String email) => find.byKey(Key('user_card_$email'));

  /// Navigate to user management screen
  Future<void> navigateToUserManagement() async {
    _logger.i('Navigating to user management screen');
    
    // This would typically be handled by NavigationHelper
    // but included here for completeness
    final adminMenu = find.byKey(const Key('admin_menu'));
    if (adminMenu.evaluate().isNotEmpty) {
      await tester.tap(adminMenu);
      await tester.pumpAndSettle();
    }
    
    final userManagementItem = find.text('User Management');
    if (userManagementItem.evaluate().isNotEmpty) {
      await tester.tap(userManagementItem);
      await tester.pumpAndSettle();
    }
    
    await _verifyScreenLoaded();
  }

  /// Create a new user
  Future<void> createUser(UserData userData) async {
    _logger.i('Creating user: ${userData.email}');
    
    await _openCreateUserDialog();
    await _fillUserForm(userData);
    await _submitUserForm();
    await _verifyUserCreated(userData.email);
  }

  /// Open create user dialog
  Future<void> _openCreateUserDialog() async {
    _logger.d('Opening create user dialog');
    
    expect(addUserFab, findsOneWidget, reason: 'Add user FAB should be present');
    await tester.tap(addUserFab);
    await tester.pumpAndSettle();
    
    expect(createUserDialog, findsOneWidget, reason: 'Create user dialog should open');
  }

  /// Fill user creation form
  Future<void> _fillUserForm(UserData userData) async {
    _logger.d('Filling user form for: ${userData.email}');
    
    // Fill required fields
    await tester.enterText(fullNameField, userData.fullName);
    await tester.enterText(emailField, userData.email);
    await tester.enterText(passwordField, userData.password);
    await tester.enterText(confirmPasswordField, userData.password);
    
    // Fill optional fields if provided
    if (userData.username != null) {
      await tester.enterText(usernameField, userData.username!);
    }
    
    if (userData.phone != null) {
      await tester.enterText(phoneField, userData.phone!);
    }
    
    // Select roles
    await _selectRoles(userData.roles);
    
    await tester.pumpAndSettle();
  }

  /// Select roles for user
  Future<void> _selectRoles(List<String> roles) async {
    _logger.d('Selecting roles: $roles');
    
    for (final role in roles) {
      final checkbox = roleCheckbox(role);
      if (checkbox.evaluate().isNotEmpty) {
        await tester.tap(checkbox);
        await tester.pumpAndSettle();
      } else {
        _logger.w('Role checkbox not found: $role');
      }
    }
  }

  /// Submit user creation form
  Future<void> _submitUserForm() async {
    _logger.d('Submitting user form');
    
    expect(createUserButton, findsOneWidget, reason: 'Create user button should be present');
    await tester.tap(createUserButton);
    await tester.pumpAndSettle();
    
    // Wait for form processing
    await tester.pump(const Duration(seconds: 2));
    await tester.pumpAndSettle();
  }

  /// Verify user was created successfully
  Future<void> _verifyUserCreated(String email) async {
    _logger.d('Verifying user created: $email');
    
    // Check for success message
    expect(successMessage, findsOneWidget, reason: 'Success message should appear');
    
    // Verify user appears in list
    await verifyUserExists(email);
  }

  /// Verify user exists in the list
  Future<void> verifyUserExists(String email) async {
    _logger.d('Verifying user exists: $email');
    
    // Search for user if search field is available
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, email);
      await tester.pumpAndSettle();
    }
    
    // Scroll to find user if needed
    await _scrollToFindUser(email);
    
    expect(find.text(email), findsOneWidget, reason: 'User $email should be visible in list');
  }

  /// Scroll to find user in list
  Future<void> _scrollToFindUser(String email) async {
    final userText = find.text(email);
    
    if (userText.evaluate().isEmpty && usersList.evaluate().isNotEmpty) {
      await tester.dragUntilVisible(
        userText,
        usersList,
        const Offset(0, -300),
        maxIteration: 10,
      );
    }
  }

  /// Edit existing user
  Future<void> editUser(String email, UserData newData) async {
    _logger.i('Editing user: $email');
    
    await _findAndTapEditButton(email);
    await _fillUserForm(newData);
    await _submitEditForm();
    await _verifyUserUpdated(newData.email);
  }

  /// Find and tap edit button for user
  Future<void> _findAndTapEditButton(String email) async {
    await _scrollToFindUser(email);
    
    final editButton = editUserButton(email);
    expect(editButton, findsOneWidget, reason: 'Edit button should be present for user $email');
    
    await tester.tap(editButton);
    await tester.pumpAndSettle();
  }

  /// Submit edit form
  Future<void> _submitEditForm() async {
    final saveButton = find.text('Save Changes');
    expect(saveButton, findsOneWidget, reason: 'Save changes button should be present');
    
    await tester.tap(saveButton);
    await tester.pumpAndSettle();
  }

  /// Verify user was updated
  Future<void> _verifyUserUpdated(String email) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after update');
    await verifyUserExists(email);
  }

  /// Delete user
  Future<void> deleteUser(String email) async {
    _logger.i('Deleting user: $email');
    
    await _findAndTapDeleteButton(email);
    await _confirmDeletion();
    await _verifyUserDeleted(email);
  }

  /// Find and tap delete button for user
  Future<void> _findAndTapDeleteButton(String email) async {
    await _scrollToFindUser(email);
    
    final deleteButton = deleteUserButton(email);
    expect(deleteButton, findsOneWidget, reason: 'Delete button should be present for user $email');
    
    await tester.tap(deleteButton);
    await tester.pumpAndSettle();
  }

  /// Confirm deletion in dialog
  Future<void> _confirmDeletion() async {
    final confirmButton = find.text('Delete');
    expect(confirmButton, findsOneWidget, reason: 'Confirm delete button should be present');
    
    await tester.tap(confirmButton);
    await tester.pumpAndSettle();
  }

  /// Verify user was deleted
  Future<void> _verifyUserDeleted(String email) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after deletion');
    expect(find.text(email), findsNothing, reason: 'User $email should no longer be visible');
  }

  /// Approve pending user
  Future<void> approveUser(String email, List<String> roles) async {
    _logger.i('Approving user: $email');
    
    await _switchToPendingTab();
    await _findAndTapApproveButton(email);
    await _selectRolesForApproval(roles);
    await _confirmApproval();
    await _verifyUserApproved(email);
  }

  /// Switch to pending approval tab
  Future<void> _switchToPendingTab() async {
    if (pendingApprovalTab.evaluate().isNotEmpty) {
      await tester.tap(pendingApprovalTab);
      await tester.pumpAndSettle();
    }
  }

  /// Find and tap approve button
  Future<void> _findAndTapApproveButton(String email) async {
    final approveButton = approveUserButton(email);
    expect(approveButton, findsOneWidget, reason: 'Approve button should be present');
    
    await tester.tap(approveButton);
    await tester.pumpAndSettle();
  }

  /// Select roles during approval
  Future<void> _selectRolesForApproval(List<String> roles) async {
    await _selectRoles(roles);
  }

  /// Confirm user approval
  Future<void> _confirmApproval() async {
    final approveButton = find.text('Approve User');
    expect(approveButton, findsOneWidget, reason: 'Approve user button should be present');
    
    await tester.tap(approveButton);
    await tester.pumpAndSettle();
  }

  /// Verify user was approved
  Future<void> _verifyUserApproved(String email) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after approval');
    
    // Switch to active users tab to verify
    if (activeUsersTab.evaluate().isNotEmpty) {
      await tester.tap(activeUsersTab);
      await tester.pumpAndSettle();
    }
    
    await verifyUserExists(email);
  }

  /// Search for users
  Future<void> searchUsers(String query) async {
    _logger.d('Searching for users: $query');
    
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, query);
      await tester.pumpAndSettle();
    }
  }

  /// Clear search
  Future<void> clearSearch() async {
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, '');
      await tester.pumpAndSettle();
    }
  }

  /// Refresh user list
  Future<void> refreshUserList() async {
    if (refreshButton.evaluate().isNotEmpty) {
      await tester.tap(refreshButton);
      await tester.pumpAndSettle();
    }
  }

  /// Verify screen is loaded
  Future<void> _verifyScreenLoaded() async {
    expect(screen, findsOneWidget, reason: 'User management screen should be loaded');
  }

  /// Get user count from UI
  Future<int> getUserCount() async {
    // This would depend on how user count is displayed in UI
    final countText = find.byKey(const Key('user_count'));
    if (countText.evaluate().isNotEmpty) {
      // Parse count from text
      // Implementation would depend on UI format
    }
    return 0; // Placeholder
  }

  /// Verify form validation errors
  Future<void> verifyValidationError(String expectedError) async {
    expect(find.text(expectedError), findsOneWidget, 
           reason: 'Validation error should be displayed: $expectedError');
  }

  /// Cancel user creation
  Future<void> cancelUserCreation() async {
    if (cancelButton.evaluate().isNotEmpty) {
      await tester.tap(cancelButton);
      await tester.pumpAndSettle();
    }
  }
}
