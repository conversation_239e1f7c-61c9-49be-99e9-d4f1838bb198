const faker = require('faker');
const config = require('../config/test.config');

class WidgetFactory {
  /**
   * Create custom widget data
   */
  static createCustomWidget() {
    const timestamp = Date.now();
    
    return {
      name: `test_widget_${timestamp}`,
      title: `Test Widget ${timestamp}`,
      type: 'statCard',
      screen: 'dashboard',
      description: 'Custom widget created for testing',
      requiredPermissions: ['dashboard.view'],
      allowedRoles: ['admin'],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 0,
      width: 1,
      height: 1,
      order: 1,
      properties: {}
    };
  }

  /**
   * Create chart widget
   */
  static createChartWidget() {
    const timestamp = Date.now();
    
    return {
      name: `efficiency_chart_${timestamp}`,
      title: 'Efficiency Chart',
      type: 'chart',
      screen: 'dashboard',
      description: 'Displays efficiency metrics',
      requiredPermissions: ['reports.generate', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager'],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 1,
      width: 2,
      height: 1,
      order: 2,
      properties: {
        chartType: 'line',
        dataSource: 'efficiency_metrics',
        refreshInterval: 300
      }
    };
  }

  /**
   * Create table widget
   */
  static createTableWidget() {
    const timestamp = Date.now();
    
    return {
      name: `recent_issues_table_${timestamp}`,
      title: 'Recent Issues',
      type: 'table',
      screen: 'dashboard',
      description: 'Shows recent maintenance issues',
      requiredPermissions: ['maintenance.read', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
      isVisible: true,
      isEnabled: true,
      positionX: 2,
      positionY: 0,
      width: 2,
      height: 2,
      order: 3,
      properties: {
        maxRows: 10,
        sortBy: 'created_at',
        sortOrder: 'desc'
      }
    };
  }

  /**
   * Create stat card widget
   */
  static createStatCardWidget() {
    const timestamp = Date.now();
    
    return {
      name: `property_count_card_${timestamp}`,
      title: 'Property Count',
      type: 'statCard',
      screen: 'dashboard',
      description: 'Shows total number of properties',
      requiredPermissions: ['properties.read', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager'],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 0,
      width: 1,
      height: 1,
      order: 1,
      properties: {
        metric: 'property_count',
        icon: 'business',
        color: 'primary'
      }
    };
  }

  /**
   * Create gauge widget
   */
  static createGaugeWidget() {
    const timestamp = Date.now();
    
    return {
      name: `fuel_level_gauge_${timestamp}`,
      title: 'Fuel Level',
      type: 'gauge',
      screen: 'dashboard',
      description: 'Shows current fuel level percentage',
      requiredPermissions: ['fuel.read', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
      isVisible: true,
      isEnabled: true,
      positionX: 1,
      positionY: 0,
      width: 1,
      height: 1,
      order: 4,
      properties: {
        metric: 'fuel_level_percentage',
        minValue: 0,
        maxValue: 100,
        thresholds: {
          low: 20,
          medium: 50,
          high: 80
        }
      }
    };
  }

  /**
   * Create list widget
   */
  static createListWidget() {
    const timestamp = Date.now();
    
    return {
      name: `attendance_list_${timestamp}`,
      title: 'Today\'s Attendance',
      type: 'list',
      screen: 'dashboard',
      description: 'Shows today\'s attendance records',
      requiredPermissions: ['attendance.read', 'dashboard.view'],
      allowedRoles: ['admin', 'property_manager'],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 2,
      width: 2,
      height: 1,
      order: 5,
      properties: {
        maxItems: 5,
        showTimestamp: true,
        filterBy: 'today'
      }
    };
  }

  /**
   * Create map widget
   */
  static createMapWidget() {
    const timestamp = Date.now();
    
    return {
      name: `property_map_${timestamp}`,
      title: 'Property Locations',
      type: 'map',
      screen: 'properties',
      description: 'Shows property locations on map',
      requiredPermissions: ['properties.read'],
      allowedRoles: ['admin', 'property_manager'],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 0,
      width: 4,
      height: 3,
      order: 1,
      properties: {
        mapType: 'satellite',
        showMarkers: true,
        enableClustering: true
      }
    };
  }

  /**
   * Create admin-only widget
   */
  static createAdminOnlyWidget() {
    const timestamp = Date.now();
    
    return {
      name: `admin_controls_${timestamp}`,
      title: 'Admin Controls',
      type: 'controls',
      screen: 'admin',
      description: 'Administrative control panel',
      requiredPermissions: ['users.create', 'roles.create'],
      allowedRoles: ['admin'],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 0,
      width: 2,
      height: 1,
      order: 1,
      properties: {
        showUserCount: true,
        showRoleCount: true,
        showSystemStatus: true
      }
    };
  }

  /**
   * Create maintenance-specific widget
   */
  static createMaintenanceWidget() {
    const timestamp = Date.now();
    
    return {
      name: `maintenance_summary_${timestamp}`,
      title: 'Maintenance Summary',
      type: 'summary',
      screen: 'maintenance',
      description: 'Summary of maintenance activities',
      requiredPermissions: ['maintenance.read'],
      allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 0,
      width: 2,
      height: 1,
      order: 1,
      properties: {
        showPending: true,
        showInProgress: true,
        showCompleted: true,
        timeframe: 'week'
      }
    };
  }

  /**
   * Create widget with invalid data for validation testing
   */
  static createInvalid() {
    return {
      name: '', // Invalid: empty name
      title: 'Invalid Widget',
      type: 'statCard',
      screen: 'dashboard',
      description: 'Invalid widget for testing validation',
      requiredPermissions: [],
      allowedRoles: [],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 0,
      width: 1,
      height: 1,
      order: 1
    };
  }

  /**
   * Create disabled widget
   */
  static createDisabledWidget() {
    const timestamp = Date.now();
    
    return {
      name: `disabled_widget_${timestamp}`,
      title: 'Disabled Widget',
      type: 'statCard',
      screen: 'dashboard',
      description: 'Widget that is disabled for testing',
      requiredPermissions: ['dashboard.view'],
      allowedRoles: ['admin'],
      isVisible: false,
      isEnabled: false,
      positionX: 0,
      positionY: 0,
      width: 1,
      height: 1,
      order: 99
    };
  }

  /**
   * Create multiple widgets for bulk operations
   */
  static createBulkWidgets(count) {
    const widgets = [];
    const types = ['statCard', 'chart', 'table', 'gauge', 'list'];
    
    for (let i = 0; i < count; i++) {
      const timestamp = Date.now() + i;
      const type = types[i % types.length];
      
      widgets.push({
        name: `bulk_widget_${i + 1}_${timestamp}`,
        title: `Bulk Widget ${i + 1}`,
        type: type,
        screen: 'dashboard',
        description: `Bulk widget ${i + 1} for testing`,
        requiredPermissions: ['dashboard.view'],
        allowedRoles: ['admin'],
        isVisible: true,
        isEnabled: true,
        positionX: i % 4,
        positionY: Math.floor(i / 4),
        width: 1,
        height: 1,
        order: i + 1
      });
    }
    
    return widgets;
  }

  /**
   * Create widget update data
   */
  static createUpdateData() {
    return {
      title: `Updated Widget Title ${Date.now()}`,
      description: `Updated description ${Date.now()}`,
      requiredPermissions: ['maintenance.read', 'maintenance.create'],
      allowedRoles: ['admin', 'maintenance_staff'],
      isVisible: false,
      properties: {
        refreshInterval: 600,
        showDetails: true
      }
    };
  }

  /**
   * Create widget for specific screen
   */
  static createForScreen(screenName) {
    const timestamp = Date.now();
    
    return {
      name: `${screenName}_widget_${timestamp}`,
      title: `${screenName} Widget`,
      type: 'statCard',
      screen: screenName,
      description: `Widget created for ${screenName} screen`,
      requiredPermissions: ['dashboard.view'],
      allowedRoles: ['admin'],
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 0,
      width: 1,
      height: 1,
      order: 1
    };
  }

  /**
   * Create widget for specific test scenario
   */
  static createForScenario(scenario) {
    const timestamp = Date.now();
    
    const baseWidget = {
      name: `${scenario}_widget_${timestamp}`,
      title: `${scenario} Widget`,
      type: 'statCard',
      screen: 'dashboard',
      description: `Widget created for ${scenario} testing`,
      isVisible: true,
      isEnabled: true,
      positionX: 0,
      positionY: 0,
      width: 1,
      height: 1,
      order: 1
    };

    switch (scenario.toLowerCase()) {
      case 'permission_testing':
        return {
          ...baseWidget,
          requiredPermissions: ['maintenance.read'],
          allowedRoles: ['admin', 'maintenance_staff']
        };
      case 'role_testing':
        return {
          ...baseWidget,
          requiredPermissions: ['dashboard.view'],
          allowedRoles: ['admin', 'property_manager', 'maintenance_staff']
        };
      case 'visibility_testing':
        return {
          ...baseWidget,
          requiredPermissions: ['properties.read'],
          allowedRoles: ['admin', 'property_manager'],
          isVisible: true
        };
      case 'admin_only':
        return {
          ...baseWidget,
          requiredPermissions: ['users.read', 'roles.read'],
          allowedRoles: ['admin']
        };
      default:
        return {
          ...baseWidget,
          requiredPermissions: ['dashboard.view'],
          allowedRoles: ['admin']
        };
    }
  }

  /**
   * Generate random widget data
   */
  static generateRandom() {
    const timestamp = Date.now();
    const types = ['statCard', 'chart', 'table', 'gauge', 'list', 'map'];
    const screens = ['dashboard', 'properties', 'maintenance', 'attendance', 'fuel'];
    const permissions = [
      'properties.read', 'maintenance.read', 'dashboard.view',
      'reports.generate', 'attendance.read', 'fuel.read'
    ];
    const roles = ['admin', 'property_manager', 'maintenance_staff', 'viewer'];
    
    // Select random values
    const type = types[Math.floor(Math.random() * types.length)];
    const screen = screens[Math.floor(Math.random() * screens.length)];
    
    // Select random subset of permissions and roles
    const numPermissions = Math.floor(Math.random() * 2) + 1;
    const numRoles = Math.floor(Math.random() * 3) + 1;
    
    const selectedPermissions = [];
    const selectedRoles = [];
    
    for (let i = 0; i < numPermissions; i++) {
      const randomPermission = permissions[Math.floor(Math.random() * permissions.length)];
      if (!selectedPermissions.includes(randomPermission)) {
        selectedPermissions.push(randomPermission);
      }
    }
    
    for (let i = 0; i < numRoles; i++) {
      const randomRole = roles[Math.floor(Math.random() * roles.length)];
      if (!selectedRoles.includes(randomRole)) {
        selectedRoles.push(randomRole);
      }
    }
    
    return {
      name: `random_widget_${timestamp}`,
      title: faker.lorem.words(2),
      type: type,
      screen: screen,
      description: faker.lorem.sentence(),
      requiredPermissions: selectedPermissions,
      allowedRoles: selectedRoles,
      isVisible: faker.datatype.boolean(),
      isEnabled: faker.datatype.boolean(),
      positionX: Math.floor(Math.random() * 4),
      positionY: Math.floor(Math.random() * 3),
      width: Math.floor(Math.random() * 2) + 1,
      height: Math.floor(Math.random() * 2) + 1,
      order: Math.floor(Math.random() * 10) + 1,
      properties: {
        refreshInterval: Math.floor(Math.random() * 600) + 60
      }
    };
  }
}

module.exports = WidgetFactory;
