name: srsr_testing
description: Automated testing suite for SRSR Property Management - Admin Dashboard Focus
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # HTTP & API Testing
  dio: ^5.4.3+1
  http: ^1.1.0
  
  # State Management (for test app)
  flutter_riverpod: ^2.5.1
  
  # Utilities
  intl: ^0.20.2
  uuid: ^4.4.0
  logger: ^2.3.0
  
  # Test Data Generation
  faker: ^2.1.0
  
  # JSON Handling
  json_annotation: ^4.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Integration Testing
  integration_test:
    sdk: flutter
  
  # Driver Testing
  flutter_driver:
    sdk: flutter
  
  # API Testing
  test: ^1.24.0
  mockito: ^5.4.4
  
  # Test Utilities
  golden_toolkit: ^0.15.0
  patrol: ^3.0.0
  
  # Code Generation
  build_runner: ^2.4.9
  json_serializable: ^6.8.0
  
  # Linting
  flutter_lints: ^6.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/test_data/
    - assets/screenshots/

flutter_intl:
  enabled: true
