import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../lib/helpers/auth_helper.dart';
import '../../lib/helpers/navigation_helper.dart';
import '../../lib/page_objects/maintenance_page.dart';
import '../../lib/factories/user_data_factory.dart';
import '../../lib/models/user_data.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Maintenance Screen Tests', () {
    late MaintenancePage maintenancePage;
    final List<String> createdIssueTitles = [];

    setUpAll(() async {
      // Setup test environment
    });

    setUp(() async {
      // Start fresh for each test
    });

    tearDown(() async {
      // Cleanup created issues after each test
      if (createdIssueTitles.isNotEmpty) {
        try {
          await AuthHelper.loginAsAdmin(tester);
          await NavigationHelper.navigateToMaintenance(tester);
          maintenancePage = MaintenancePage(tester);
          
          for (final issueTitle in createdIssueTitles) {
            try {
              await maintenancePage.deleteIssue(issueTitle);
            } catch (e) {
              print('Failed to cleanup issue $issueTitle: $e');
            }
          }
          createdIssueTitles.clear();
        } catch (e) {
          print('Failed to cleanup test data: $e');
        }
      }
    });

    testWidgets('Admin can create high priority maintenance issue', (tester) async {
      // Step 1: Login as admin
      await AuthHelper.loginAsAdmin(tester);
      
      // Step 2: Navigate to maintenance
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Step 3: Create test issue data
      final issueData = MaintenanceIssueDataFactory.createHighPriority();
      createdIssueTitles.add(issueData.title);
      
      // Step 4: Create issue through UI
      await maintenancePage.createMaintenanceIssue(issueData);
      
      // Step 5: Verify issue creation
      await maintenancePage.verifyIssueExists(issueData.title);
    });

    testWidgets('Property manager can create medium priority issue', (tester) async {
      await AuthHelper.loginWithRole(tester, 'property_manager');
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      final issueData = MaintenanceIssueDataFactory.createMediumPriority();
      createdIssueTitles.add(issueData.title);
      
      await maintenancePage.createMaintenanceIssue(issueData);
      await maintenancePage.verifyIssueExists(issueData.title);
    });

    testWidgets('Maintenance staff can create and update issues', (tester) async {
      await AuthHelper.loginWithRole(tester, 'maintenance_staff');
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      final issueData = MaintenanceIssueDataFactory.createLowPriority();
      createdIssueTitles.add(issueData.title);
      
      await maintenancePage.createMaintenanceIssue(issueData);
      await maintenancePage.verifyIssueExists(issueData.title);
      
      // Update issue status
      await maintenancePage.updateIssueStatus(issueData.title, 'In Progress');
    });

    testWidgets('Issue creation validates required fields', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Try to create issue with empty form
      await tester.tap(maintenancePage.addIssueFab);
      await tester.pumpAndSettle();
      
      await tester.tap(maintenancePage.saveIssueButton);
      await tester.pumpAndSettle();
      
      // Verify validation errors
      await maintenancePage.verifyValidationError('Issue title is required');
      await maintenancePage.verifyValidationError('Description is required');
    });

    testWidgets('Admin can create HVAC maintenance issue', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      final issueData = MaintenanceIssueDataFactory.createHVACIssue();
      createdIssueTitles.add(issueData.title);
      
      await maintenancePage.createMaintenanceIssue(issueData);
      await maintenancePage.verifyIssueExists(issueData.title);
    });

    testWidgets('Admin can filter issues by status', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create issues with different statuses
      final pendingIssue = MaintenanceIssueDataFactory.createHighPriority();
      final inProgressIssue = MaintenanceIssueDataFactory.createMediumPriority();
      
      createdIssueTitles.addAll([pendingIssue.title, inProgressIssue.title]);
      
      await maintenancePage.createMaintenanceIssue(pendingIssue);
      await maintenancePage.createMaintenanceIssue(inProgressIssue);
      
      // Update one issue to in progress
      await maintenancePage.updateIssueStatus(inProgressIssue.title, 'In Progress');
      
      // Test filtering
      await maintenancePage.filterIssuesByStatus('pending');
      await maintenancePage.verifyIssueExists(pendingIssue.title);
      
      await maintenancePage.filterIssuesByStatus('in_progress');
      await maintenancePage.verifyIssueExists(inProgressIssue.title);
      
      await maintenancePage.filterIssuesByStatus('all');
      await maintenancePage.verifyIssueExists(pendingIssue.title);
      await maintenancePage.verifyIssueExists(inProgressIssue.title);
    });

    testWidgets('Admin can search for maintenance issues', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create test issue
      final issueData = MaintenanceIssueDataFactory.createHighPriority();
      createdIssueTitles.add(issueData.title);
      await maintenancePage.createMaintenanceIssue(issueData);
      
      // Search for issue
      await maintenancePage.searchIssues(issueData.title);
      await maintenancePage.verifyIssueExists(issueData.title);
      
      // Clear search
      await maintenancePage.clearSearch();
    });

    testWidgets('Admin can assign maintenance issue to staff', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create test issue
      final issueData = MaintenanceIssueDataFactory.createMediumPriority();
      createdIssueTitles.add(issueData.title);
      await maintenancePage.createMaintenanceIssue(issueData);
      
      // Assign issue to maintenance staff
      await maintenancePage.assignIssue(issueData.title, 'Test Maintenance Staff');
    });

    testWidgets('Admin can escalate maintenance issue', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create test issue
      final issueData = MaintenanceIssueDataFactory.createLowPriority();
      createdIssueTitles.add(issueData.title);
      await maintenancePage.createMaintenanceIssue(issueData);
      
      // Escalate issue
      await maintenancePage.escalateIssue(issueData.title, 'Issue requires immediate attention');
    });

    testWidgets('Staff can add comments to maintenance issues', (tester) async {
      await AuthHelper.loginWithRole(tester, 'maintenance_staff');
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create test issue
      final issueData = MaintenanceIssueDataFactory.createHighPriority();
      createdIssueTitles.add(issueData.title);
      await maintenancePage.createMaintenanceIssue(issueData);
      
      // Add comment to issue
      await maintenancePage.addCommentToIssue(issueData.title, 'Working on this issue now');
    });

    testWidgets('Admin can edit existing maintenance issue', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create issue first
      final originalIssueData = MaintenanceIssueDataFactory.createMediumPriority();
      createdIssueTitles.add(originalIssueData.title);
      await maintenancePage.createMaintenanceIssue(originalIssueData);
      
      // Edit issue
      final updatedIssueData = originalIssueData.copyWith(
        description: 'Updated issue description for testing',
        priority: 'High',
      );
      
      await maintenancePage.editIssue(originalIssueData.title, updatedIssueData);
      await maintenancePage.verifyIssueExists(updatedIssueData.title);
    });

    testWidgets('Maintenance issue workflow from creation to completion', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Step 1: Create issue
      final issueData = MaintenanceIssueDataFactory.createHighPriority();
      createdIssueTitles.add(issueData.title);
      await maintenancePage.createMaintenanceIssue(issueData);
      
      // Step 2: Assign to maintenance staff
      await maintenancePage.assignIssue(issueData.title, 'Test Maintenance Staff');
      
      // Step 3: Update status to in progress
      await maintenancePage.updateIssueStatus(issueData.title, 'In Progress');
      
      // Step 4: Add progress comment
      await maintenancePage.addCommentToIssue(issueData.title, 'Started working on the issue');
      
      // Step 5: Complete the issue
      await maintenancePage.updateIssueStatus(issueData.title, 'Completed');
      
      // Step 6: Add completion comment
      await maintenancePage.addCommentToIssue(issueData.title, 'Issue has been resolved');
    });

    testWidgets('Urgent issues are properly highlighted', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create urgent issue
      final urgentIssue = MaintenanceIssueDataFactory.createHighPriority();
      createdIssueTitles.add(urgentIssue.title);
      await maintenancePage.createMaintenanceIssue(urgentIssue);
      
      // Filter by urgent to verify highlighting
      await maintenancePage.filterIssuesByStatus('urgent');
      await maintenancePage.verifyIssueExists(urgentIssue.title);
    });

    testWidgets('Viewer has read-only access to maintenance issues', (tester) async {
      // Login as viewer
      await AuthHelper.loginWithRole(tester, 'viewer');
      
      // Verify viewer has limited access to maintenance
      await NavigationHelper.verifyRestrictedScreens(tester, ['maintenance']);
    });

    testWidgets('Maintenance screen handles empty state correctly', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // If no issues exist, should show appropriate message
      // This test would depend on the actual implementation
      await maintenancePage.navigateToMaintenance();
    });

    testWidgets('Maintenance screen supports refresh functionality', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Test refresh functionality
      await maintenancePage.refreshIssues();
    });

    testWidgets('Issue deletion requires confirmation', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create issue to delete
      final issueData = MaintenanceIssueDataFactory.createLowPriority();
      await maintenancePage.createMaintenanceIssue(issueData);
      
      // Delete issue (no need to add to cleanup list since it's being deleted)
      await maintenancePage.deleteIssue(issueData.title);
      
      // Verify issue is deleted
      expect(find.text(issueData.title), findsNothing);
    });

    testWidgets('Maintenance issues support priority-based sorting', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      // Create issues with different priorities
      final highPriorityIssue = MaintenanceIssueDataFactory.createHighPriority();
      final mediumPriorityIssue = MaintenanceIssueDataFactory.createMediumPriority();
      final lowPriorityIssue = MaintenanceIssueDataFactory.createLowPriority();
      
      createdIssueTitles.addAll([
        highPriorityIssue.title,
        mediumPriorityIssue.title,
        lowPriorityIssue.title,
      ]);
      
      // Create issues in reverse priority order
      await maintenancePage.createMaintenanceIssue(lowPriorityIssue);
      await maintenancePage.createMaintenanceIssue(mediumPriorityIssue);
      await maintenancePage.createMaintenanceIssue(highPriorityIssue);
      
      // Verify all issues exist
      await maintenancePage.verifyIssueExists(highPriorityIssue.title);
      await maintenancePage.verifyIssueExists(mediumPriorityIssue.title);
      await maintenancePage.verifyIssueExists(lowPriorityIssue.title);
      
      // High priority issues should appear first (implementation dependent)
    });
  });
}
