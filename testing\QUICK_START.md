# 🚀 SRSR Testing Project - Quick Start Guide

## 📋 Overview

This dedicated testing project focuses on **Admin Dashboard Features** as the top priority, providing comprehensive automation testing for the SRSR Property Management system.

## 🎯 Admin Dashboard Testing Priority

### **Phase 1: Core Admin Features** ⭐ **CURRENT FOCUS**
- ✅ **User Management** - Create, edit, delete, role assignment
- ✅ **Role Management** - Create roles, assign permissions  
- ✅ **Permission Configuration** - Screen and widget permissions
- ✅ **Dashboard Overview** - Admin dashboard widgets and stats

### **Phase 2: Advanced Admin Features** (Next)
- 🔄 **Screen Management** - Custom screen creation
- 🔄 **Widget Management** - Widget configuration and permissions
- 🔄 **Real-time Updates** - Live permission changes
- 🔄 **Security Testing** - Permission enforcement

## 🛠️ Prerequisites

### **Required Software**
- **Flutter SDK** 3.16.0+
- **Node.js** 18+
- **Git** (for version control)

### **Backend Requirements**
- Backend server running on `localhost:3000`
- PostgreSQL database with test data
- Admin user: `<EMAIL>` / `admin123`

## ⚡ Quick Setup (5 minutes)

### **1. Navigate to Testing Directory**
```bash
cd testing
```

### **2. Setup Flutter Tests**
```bash
cd flutter_tests
flutter pub get
cd ..
```

### **3. Setup API Tests**
```bash
cd api_tests
npm install
cd ..
```

### **4. Verify Backend is Running**
```bash
# Check if backend is accessible
curl http://localhost:3000/api

# If not running, start backend:
cd ../backend
npm run dev &
cd ../testing
```

### **5. Setup Test Environment**
```bash
cd api_tests
npm run setup
cd ..
```

## 🧪 Run Admin Dashboard Tests

### **🎯 Run All Admin Tests (Recommended)**
```bash
# Run complete admin test suite
./scripts/run-admin-tests.sh
```

### **📱 Run Flutter Integration Tests Only**
```bash
cd flutter_tests
flutter test integration_test/admin/
```

### **🌐 Run API Tests Only**
```bash
cd api_tests
npm run test:admin
```

### **👤 Run Specific Admin Feature Tests**
```bash
# User Management tests
cd api_tests
npm run test:user-management

# Role Management tests
npm run test:role-management

# Permission Configuration tests
npm run test:permission-config
```

## 📊 Test Results

### **View Test Reports**
```bash
# HTML reports
open reports/html/admin-api-test-report.html

# Coverage reports
open reports/coverage/index.html

# JSON results
cat reports/json/admin-test-results.json
```

### **Real-time Test Monitoring**
```bash
# Watch mode for API tests
cd api_tests
npm run test:watch

# Watch mode for Flutter tests
cd flutter_tests
flutter test --watch
```

## 🎯 Current Test Coverage

### **✅ Implemented Tests**

#### **User Management API Tests**
- ✅ Create users (all roles)
- ✅ User validation (email, password, required fields)
- ✅ Role assignment (single/multiple roles)
- ✅ User updates and deletion
- ✅ Permission enforcement
- ✅ Search and filtering

#### **Flutter Integration Tests**
- ✅ Admin login and navigation
- ✅ User creation workflow
- ✅ Form validation testing
- ✅ Role-based access control
- ✅ Permission verification

### **🔄 In Progress**
- Role Management API tests
- Permission Configuration tests
- Screen Management tests
- Widget Management tests

## 🐛 Troubleshooting

### **Common Issues**

#### **Backend Not Running**
```bash
# Check backend status
curl http://localhost:3000/api

# Start backend
cd ../backend && npm run dev
```

#### **Test Dependencies Missing**
```bash
# Flutter dependencies
cd flutter_tests && flutter pub get

# API test dependencies
cd api_tests && npm install
```

#### **Database Issues**
```bash
# Reset test database
cd ../backend && npm run db:reset:test
```

#### **Permission Errors**
```bash
# Make scripts executable
chmod +x scripts/*.sh

# Run with explicit bash
bash scripts/run-admin-tests.sh
```

### **Debug Mode**
```bash
# Enable detailed logging
export LOG_LEVEL=debug
export LOG_REQUESTS=true
export LOG_RESPONSES=true

# Run tests with debug info
./scripts/run-admin-tests.sh
```

## 📈 Test Metrics

### **Current Coverage Goals**
- **User Management**: 100% ✅
- **Role Management**: 90% 🔄
- **Permission Config**: 90% 🔄
- **Dashboard Widgets**: 85% 🔄
- **Security Testing**: 95% 🔄

### **Performance Targets**
- **API Response Time**: < 500ms
- **Test Execution Time**: < 5 minutes
- **Test Success Rate**: > 95%

## 🔧 Configuration

### **Environment Variables**
```bash
# Create .env file in api_tests/
TEST_ENV=local
TEST_BACKEND_HOST=localhost
TEST_BACKEND_PORT=3000
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=admin123
LOG_LEVEL=info
```

### **Test Data Configuration**
```javascript
// api_tests/src/config/test.config.js
testData: {
  userPrefix: 'test.user',
  cleanupAfterTests: true,
  generateRandomData: true
}
```

## 🎯 Next Steps

### **Immediate Actions**
1. **Run admin tests**: `./scripts/run-admin-tests.sh`
2. **Review test results** in `reports/` directory
3. **Check coverage** for admin features
4. **Report any issues** found

### **Development Workflow**
1. **Add new test cases** for admin features
2. **Extend page objects** for new UI elements
3. **Update test data factories** as needed
4. **Maintain test documentation**

## 📞 Support

### **Test Execution Issues**
- Check backend connectivity
- Verify test data setup
- Review error logs in `reports/`

### **Test Development**
- Follow page object pattern
- Use test data factories
- Maintain test isolation
- Document test scenarios

## 🎉 Success Criteria

### **Admin Dashboard Testing Complete When:**
- ✅ All user management workflows automated
- ✅ Role and permission testing comprehensive
- ✅ Screen and widget management covered
- ✅ Security testing validates all access controls
- ✅ Real-time updates verified
- ✅ Test coverage > 95% for admin features

---

**🎯 Focus: Admin Dashboard Features**  
**📊 Current Status: User Management Complete, Role Management In Progress**  
**🚀 Next: Role Management and Permission Configuration Testing**
