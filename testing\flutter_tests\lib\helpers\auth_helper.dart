import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../models/user_data.dart';
import 'test_config.dart';

class AuthHelper {
  static final _logger = Logger();

  /// Default admin credentials
  static const String adminEmail = '<EMAIL>';
  static const String adminPassword = 'admin123';

  /// Test user credentials
  static const Map<String, Map<String, String>> testCredentials = {
    'admin': {
      'email': '<EMAIL>',
      'password': 'admin123',
    },
    'property_manager': {
      'email': '<EMAIL>',
      'password': 'admin123',
    },
    'maintenance_staff': {
      'email': '<EMAIL>',
      'password': 'admin123',
    },
    'security_guard': {
      'email': '<EMAIL>',
      'password': 'admin123',
    },
    'viewer': {
      'email': '<EMAIL>',
      'password': 'admin123',
    },
  };

  /// Login as admin user
  static Future<void> loginAsAdmin(WidgetTester tester) async {
    await loginWithCredentials(tester, adminEmail, adminPassword);
  }

  /// Login with specific role
  static Future<void> loginWithRole(WidgetTester tester, String role) async {
    final credentials = testCredentials[role];
    if (credentials == null) {
      throw Exception('No test credentials found for role: $role');
    }
    
    await loginWithCredentials(
      tester, 
      credentials['email']!, 
      credentials['password']!,
    );
  }

  /// Login with specific credentials
  static Future<void> loginWithCredentials(
    WidgetTester tester, 
    String email, 
    String password,
  ) async {
    _logger.i('Attempting login with email: $email');

    try {
      // Wait for login screen to be ready
      await tester.pumpAndSettle();
      
      // Find and clear email field
      final emailField = find.byKey(const Key('email_field'));
      expect(emailField, findsOneWidget, reason: 'Email field should be present');
      
      await tester.tap(emailField);
      await tester.pumpAndSettle();
      await tester.enterText(emailField, '');
      await tester.enterText(emailField, email);
      
      // Find and clear password field
      final passwordField = find.byKey(const Key('password_field'));
      expect(passwordField, findsOneWidget, reason: 'Password field should be present');
      
      await tester.tap(passwordField);
      await tester.pumpAndSettle();
      await tester.enterText(passwordField, '');
      await tester.enterText(passwordField, password);
      
      await tester.pumpAndSettle();
      
      // Tap login button
      final loginButton = find.byKey(const Key('login_button'));
      expect(loginButton, findsOneWidget, reason: 'Login button should be present');
      
      await tester.tap(loginButton);
      await tester.pumpAndSettle();
      
      // Wait for navigation to complete
      await tester.pump(const Duration(seconds: 2));
      await tester.pumpAndSettle();
      
      // Verify successful login by checking for dashboard
      await _verifyLoginSuccess(tester);
      
      _logger.i('Login successful for: $email');
      
    } catch (e) {
      _logger.e('Login failed for $email: $e');
      
      // Take screenshot for debugging
      await _takeScreenshot(tester, 'login_failure_${email.replaceAll('@', '_at_')}');
      
      rethrow;
    }
  }

  /// Login with UserData object
  static Future<void> loginWithUserData(WidgetTester tester, UserData userData) async {
    await loginWithCredentials(tester, userData.email, userData.password);
  }

  /// Logout current user
  static Future<void> logout(WidgetTester tester) async {
    _logger.i('Attempting logout');

    try {
      // Try to find logout button in various locations
      Finder? logoutButton;
      
      // Check if profile menu exists
      final profileMenu = find.byKey(const Key('profile_menu'));
      if (profileMenu.evaluate().isNotEmpty) {
        await tester.tap(profileMenu);
        await tester.pumpAndSettle();
        
        logoutButton = find.byKey(const Key('logout_button'));
      }
      
      // Check if hamburger menu exists
      if (logoutButton == null || logoutButton.evaluate().isEmpty) {
        final hamburgerMenu = find.byKey(const Key('hamburger_menu'));
        if (hamburgerMenu.evaluate().isNotEmpty) {
          await tester.tap(hamburgerMenu);
          await tester.pumpAndSettle();
          
          logoutButton = find.byKey(const Key('logout_button'));
        }
      }
      
      // Check if logout is directly accessible
      if (logoutButton == null || logoutButton.evaluate().isEmpty) {
        logoutButton = find.byKey(const Key('logout_button'));
      }
      
      // Check for logout text
      if (logoutButton == null || logoutButton.evaluate().isEmpty) {
        logoutButton = find.text('Logout');
      }
      
      if (logoutButton != null && logoutButton.evaluate().isNotEmpty) {
        await tester.tap(logoutButton);
        await tester.pumpAndSettle();
        
        // Wait for logout to complete
        await tester.pump(const Duration(seconds: 1));
        await tester.pumpAndSettle();
        
        // Verify logout by checking for login screen
        await _verifyLogoutSuccess(tester);
        
        _logger.i('Logout successful');
      } else {
        _logger.w('Logout button not found, assuming already logged out');
      }
      
    } catch (e) {
      _logger.e('Logout failed: $e');
      
      // Take screenshot for debugging
      await _takeScreenshot(tester, 'logout_failure');
      
      rethrow;
    }
  }

  /// Verify login was successful
  static Future<void> _verifyLoginSuccess(WidgetTester tester) async {
    // Wait a bit for navigation
    await tester.pump(const Duration(seconds: 1));
    await tester.pumpAndSettle();
    
    // Check for dashboard indicators
    final dashboardIndicators = [
      find.text('Dashboard'),
      find.byKey(const Key('dashboard_screen')),
      find.byKey(const Key('bottom_navigation')),
      find.byKey(const Key('app_bar')),
    ];
    
    bool loginSuccessful = false;
    for (final indicator in dashboardIndicators) {
      if (indicator.evaluate().isNotEmpty) {
        loginSuccessful = true;
        break;
      }
    }
    
    if (!loginSuccessful) {
      // Check for error messages
      final errorMessages = [
        find.text('Invalid credentials'),
        find.text('Login failed'),
        find.text('Error'),
        find.byKey(const Key('error_message')),
      ];
      
      for (final errorMsg in errorMessages) {
        if (errorMsg.evaluate().isNotEmpty) {
          throw Exception('Login failed with error message visible');
        }
      }
      
      throw Exception('Login verification failed - no dashboard indicators found');
    }
  }

  /// Verify logout was successful
  static Future<void> _verifyLogoutSuccess(WidgetTester tester) async {
    // Wait a bit for navigation
    await tester.pump(const Duration(seconds: 1));
    await tester.pumpAndSettle();
    
    // Check for login screen indicators
    final loginIndicators = [
      find.byKey(const Key('login_screen')),
      find.byKey(const Key('email_field')),
      find.byKey(const Key('password_field')),
      find.byKey(const Key('login_button')),
      find.text('Login'),
    ];
    
    bool logoutSuccessful = false;
    for (final indicator in loginIndicators) {
      if (indicator.evaluate().isNotEmpty) {
        logoutSuccessful = true;
        break;
      }
    }
    
    if (!logoutSuccessful) {
      throw Exception('Logout verification failed - no login screen indicators found');
    }
  }

  /// Take screenshot for debugging
  static Future<void> _takeScreenshot(WidgetTester tester, String name) async {
    try {
      // This would be implemented based on your screenshot utility
      _logger.i('Taking screenshot: $name');
      // await ScreenshotHelper.takeScreenshot(tester, name);
    } catch (e) {
      _logger.w('Failed to take screenshot: $e');
    }
  }

  /// Verify user has expected permissions
  static Future<void> verifyUserPermissions(
    WidgetTester tester, 
    UserData userData,
  ) async {
    _logger.i('Verifying permissions for user: ${userData.email}');
    
    // Check accessible screens
    for (final screen in userData.expectedScreens) {
      await _verifyScreenAccessible(tester, screen);
    }
    
    // Check restricted screens
    for (final screen in userData.restrictedScreens) {
      await _verifyScreenRestricted(tester, screen);
    }
    
    // Check widget visibility
    for (final entry in userData.expectedWidgets.entries) {
      await _verifyWidgetVisibility(tester, entry.key, entry.value);
    }
  }

  /// Verify screen is accessible
  static Future<void> _verifyScreenAccessible(WidgetTester tester, String screen) async {
    // This would navigate to the screen and verify access
    _logger.d('Verifying screen accessible: $screen');
    
    // Implementation would depend on navigation structure
    // For now, just check if navigation item exists
    final navItem = find.byKey(Key('nav_$screen'));
    if (navItem.evaluate().isNotEmpty) {
      _logger.d('Screen navigation item found: $screen');
    }
  }

  /// Verify screen is restricted
  static Future<void> _verifyScreenRestricted(WidgetTester tester, String screen) async {
    _logger.d('Verifying screen restricted: $screen');
    
    // Check that navigation item is not present
    final navItem = find.byKey(Key('nav_$screen'));
    expect(navItem, findsNothing, reason: 'Screen $screen should not be accessible');
  }

  /// Verify widget visibility
  static Future<void> _verifyWidgetVisibility(
    WidgetTester tester, 
    String widgetKey, 
    bool shouldBeVisible,
  ) async {
    _logger.d('Verifying widget visibility: $widgetKey = $shouldBeVisible');
    
    final widget = find.byKey(Key(widgetKey));
    
    if (shouldBeVisible) {
      expect(widget, findsOneWidget, reason: 'Widget $widgetKey should be visible');
    } else {
      expect(widget, findsNothing, reason: 'Widget $widgetKey should not be visible');
    }
  }

  /// Wait for loading to complete
  static Future<void> waitForLoading(WidgetTester tester) async {
    // Wait for any loading indicators to disappear
    await tester.pumpAndSettle();
    
    final loadingIndicators = [
      find.byType(CircularProgressIndicator),
      find.byKey(const Key('loading_indicator')),
      find.text('Loading...'),
    ];
    
    for (final indicator in loadingIndicators) {
      while (indicator.evaluate().isNotEmpty) {
        await tester.pump(const Duration(milliseconds: 100));
      }
    }
    
    await tester.pumpAndSettle();
  }

  /// Check if user is currently logged in
  static bool isLoggedIn(WidgetTester tester) {
    final loginIndicators = [
      find.byKey(const Key('login_screen')),
      find.byKey(const Key('email_field')),
      find.byKey(const Key('password_field')),
    ];
    
    for (final indicator in loginIndicators) {
      if (indicator.evaluate().isNotEmpty) {
        return false; // Login screen is visible, so not logged in
      }
    }
    
    return true; // No login screen visible, assume logged in
  }
}
