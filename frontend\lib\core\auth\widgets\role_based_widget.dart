import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_role.dart';
import '../../../features/auth/presentation/providers/auth_providers.dart';


/// A widget that conditionally shows content based on user roles and permissions
class RoleBasedWidget extends ConsumerWidget {
  final List<UserRole>? allowedRoles;
  final List<String>? requiredPermissions;
  final Widget child;
  final Widget? fallback;
  final bool requireAll; // If true, user must have ALL permissions/roles

  const RoleBasedWidget({
    super.key,
    this.allowedRoles,
    this.requiredPermissions,
    required this.child,
    this.fallback,
    this.requireAll = false,
  }) : assert(allowedRoles != null || requiredPermissions != null,
            'Either allowedRoles or requiredPermissions must be provided');

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    if (!authState.isAuthenticated || authState.user == null) {
      return fallback ?? const SizedBox.shrink();
    }

    final userRole = authState.user!.role;

    bool hasAccess = false;

    // Check role-based access
    if (allowedRoles != null) {
      if (requireAll) {
        hasAccess = allowedRoles!.every((role) => role == userRole);
      } else {
        hasAccess = allowedRoles!.contains(userRole);
      }
    }

    // Check permission-based access
    if (requiredPermissions != null) {
      if (requireAll) {
        hasAccess = hasAccess && requiredPermissions!.every(userRole.hasPermission);
      } else {
        hasAccess = hasAccess || requiredPermissions!.any(userRole.hasPermission);
      }
    }

    return hasAccess ? child : (fallback ?? const SizedBox.shrink());
  }
}

/// A specialized app bar that shows role-based information
class RoleBasedAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool showRoleIndicator;
  final bool automaticallyImplyLeading;

  const RoleBasedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showRoleIndicator = true,
    this.automaticallyImplyLeading = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    if (!authState.isAuthenticated || authState.user == null) {
      return AppBar(title: Text(title));
    }

    final userRole = authState.user!.role;

    return AppBar(
      title: Row(
        children: [
          Expanded(child: Text(title)),
          if (showRoleIndicator) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: userRole.primaryColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: userRole.primaryColor, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    userRole.icon,
                    size: 16,
                    color: userRole.primaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    userRole.name,
                    style: TextStyle(
                      fontSize: 12,
                      color: userRole.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
      actions: actions,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: userRole.primaryColor,
      foregroundColor: Colors.white,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// A floating action button that adapts based on user role
class RoleBasedFAB extends ConsumerWidget {
  final VoidCallback? onPressed;
  final Widget? child;
  final String? tooltip;
  final List<UserRole>? allowedRoles;
  final List<String>? requiredPermissions;

  const RoleBasedFAB({
    super.key,
    this.onPressed,
    this.child,
    this.tooltip,
    this.allowedRoles,
    this.requiredPermissions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RoleBasedWidget(
      allowedRoles: allowedRoles,
      requiredPermissions: requiredPermissions,
      child: FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        child: child,
      ),
    );
  }
}

/// A drawer that shows role-appropriate menu items
class RoleBasedDrawer extends ConsumerWidget {
  const RoleBasedDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    if (!authState.isAuthenticated || authState.user == null) {
      return const Drawer(child: SizedBox.shrink());
    }

    final userRole = authState.user!.role;

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          UserAccountsDrawerHeader(
            accountName: Text(authState.user!.fullName),
            accountEmail: Text(authState.user!.email),
            currentAccountPicture: CircleAvatar(
              backgroundColor: userRole.primaryColor,
              child: Icon(
                userRole.icon,
                color: Colors.white,
                size: 32,
              ),
            ),
            decoration: BoxDecoration(
              color: userRole.primaryColor,
            ),
          ),
          ListTile(
            leading: const Icon(Icons.dashboard),
            title: const Text('Dashboard'),
            onTap: () => Navigator.pushReplacementNamed(context, '/dashboard'),
          ),
          RoleBasedWidget(
            requiredPermissions: const ['properties.read'],
            child: ListTile(
              leading: const Icon(Icons.business),
              title: const Text('Properties'),
              onTap: () => Navigator.pushNamed(context, '/properties'),
            ),
          ),
          RoleBasedWidget(
            requiredPermissions: const ['maintenance.read'],
            child: ListTile(
              leading: const Icon(Icons.build),
              title: const Text('Maintenance'),
              onTap: () => Navigator.pushNamed(context, '/maintenance'),
            ),
          ),
          RoleBasedWidget(
            requiredPermissions: const ['attendance.read'],
            child: ListTile(
              leading: const Icon(Icons.people),
              title: const Text('Attendance'),
              onTap: () => Navigator.pushNamed(context, '/attendance'),
            ),
          ),
          RoleBasedWidget(
            requiredPermissions: const ['fuel.read'],
            child: ListTile(
              leading: const Icon(Icons.local_gas_station),
              title: const Text('Fuel Management'),
              onTap: () => Navigator.pushNamed(context, '/fuel'),
            ),
          ),
          RoleBasedWidget(
            allowedRoles: const [UserRole.admin],
            child: const Divider(),
          ),
          RoleBasedWidget(
            allowedRoles: const [UserRole.admin],
            child: ListTile(
              leading: const Icon(Icons.admin_panel_settings),
              title: const Text('Admin Panel'),
              onTap: () => Navigator.pushNamed(context, '/admin'),
            ),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Logout'),
            onTap: () {
              ref.read(authStateProvider.notifier).logout();
              Navigator.pushReplacementNamed(context, '/login');
            },
          ),
        ],
      ),
    );
  }
}
