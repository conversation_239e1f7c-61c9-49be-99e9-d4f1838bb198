const AuthHelper = require('../../src/helpers/auth_helper');
const UserFactory = require('../../src/factories/user_factory');
const config = require('../../src/config/test.config');

describe('Admin User Management API Tests', () => {
  let adminToken;
  let testUsers = [];

  beforeAll(async () => {
    // Setup test environment
    console.log('🔧 Setting up Admin User Management API tests...');
    
    try {
      adminToken = await AuthHelper.getAdminToken();
      console.log('✅ Admin authentication successful');
    } catch (error) {
      console.error('❌ Failed to authenticate admin user:', error.message);
      throw error;
    }
  });

  afterAll(async () => {
    // Cleanup test users
    console.log('🧹 Cleaning up test users...');
    
    for (const user of testUsers) {
      try {
        await AuthHelper.deleteTestUser(user.id);
        console.log(`✅ Deleted test user: ${user.email}`);
      } catch (error) {
        console.warn(`⚠️ Failed to cleanup user ${user.email}:`, error.message);
      }
    }
    
    testUsers = [];
    console.log('✅ Cleanup completed');
  });

  describe('User Creation', () => {
    test('should create admin user with valid data', async () => {
      const userData = UserFactory.createAdmin();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.email).toBe(userData.email);
      expect(response.data.data.fullName).toBe(userData.fullName);
      expect(response.data.data.roles).toEqual(expect.arrayContaining(userData.roles));
      expect(response.data.data.isActive).toBe(true);

      testUsers.push(response.data.data);
    });

    test('should create property manager user with valid data', async () => {
      const userData = UserFactory.createPropertyManager();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.email).toBe(userData.email);
      expect(response.data.data.roles).toEqual(expect.arrayContaining(['property_manager']));

      testUsers.push(response.data.data);
    });

    test('should create maintenance staff user with valid data', async () => {
      const userData = UserFactory.createMaintenanceStaff();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.email).toBe(userData.email);
      expect(response.data.data.roles).toEqual(expect.arrayContaining(['maintenance_staff']));

      testUsers.push(response.data.data);
    });

    test('should create viewer user with valid data', async () => {
      const userData = UserFactory.createViewer();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.email).toBe(userData.email);
      expect(response.data.data.roles).toEqual(expect.arrayContaining(['viewer']));

      testUsers.push(response.data.data);
    });

    test('should reject user with invalid email', async () => {
      const userData = UserFactory.createWithInvalidEmail();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/email/i);
    });

    test('should reject user with weak password', async () => {
      const userData = UserFactory.createWithWeakPassword();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/password/i);
    });

    test('should reject user with missing required fields', async () => {
      const userData = UserFactory.createIncomplete();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toBeDefined();
    });

    test('should reject duplicate email', async () => {
      const userData = UserFactory.createPropertyManager();
      
      // Create first user
      const firstResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );
      expect(firstResponse.status).toBe(201);
      testUsers.push(firstResponse.data.data);

      // Try to create duplicate
      const duplicateResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(duplicateResponse.status).toBe(409);
      expect(duplicateResponse.data.success).toBe(false);
      expect(duplicateResponse.data.error).toMatch(/already exists|duplicate/i);
    });

    test('should create user with multiple roles', async () => {
      const userData = UserFactory.createWithCustomRoles(['property_manager', 'maintenance_staff']);
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.roles).toHaveLength(2);
      expect(response.data.data.roles).toEqual(expect.arrayContaining(['property_manager', 'maintenance_staff']));

      testUsers.push(response.data.data);
    });
  });

  describe('User Retrieval', () => {
    let testUser;

    beforeAll(async () => {
      // Create a test user for retrieval tests
      const userData = UserFactory.createPropertyManager();
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );
      testUser = response.data.data;
      testUsers.push(testUser);
    });

    test('should get all users', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        '/users'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      expect(response.data.data.length).toBeGreaterThan(0);
    });

    test('should get user by ID', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        `/users/${testUser.id}`
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.id).toBe(testUser.id);
      expect(response.data.data.email).toBe(testUser.email);
    });

    test('should return 404 for non-existent user', async () => {
      const nonExistentId = '99999999-9999-9999-9999-999999999999';
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        `/users/${nonExistentId}`
      );

      expect(response.status).toBe(404);
      expect(response.data.success).toBe(false);
    });

    test('should filter users by role', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        '/users?role=property_manager'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      
      // All returned users should have property_manager role
      response.data.data.forEach(user => {
        expect(user.roles).toContain('property_manager');
      });
    });

    test('should search users by email', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        `/users?search=${testUser.email}`
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      expect(response.data.data.some(user => user.email === testUser.email)).toBe(true);
    });
  });

  describe('User Updates', () => {
    let testUser;

    beforeEach(async () => {
      // Create a fresh test user for each update test
      const userData = UserFactory.createPropertyManager();
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );
      testUser = response.data.data;
      testUsers.push(testUser);
    });

    test('should update user details', async () => {
      const updateData = UserFactory.createUpdateData();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'PUT', 
        `/users/${testUser.id}`, 
        updateData
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.fullName).toBe(updateData.fullName);
      expect(response.data.data.phone).toBe(updateData.phone);
    });

    test('should activate/deactivate user', async () => {
      // Deactivate user
      const deactivateResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'PUT', 
        `/users/${testUser.id}`, 
        { isActive: false }
      );

      expect(deactivateResponse.status).toBe(200);
      expect(deactivateResponse.data.data.isActive).toBe(false);

      // Reactivate user
      const activateResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'PUT', 
        `/users/${testUser.id}`, 
        { isActive: true }
      );

      expect(activateResponse.status).toBe(200);
      expect(activateResponse.data.data.isActive).toBe(true);
    });

    test('should reject update with invalid data', async () => {
      const invalidUpdateData = {
        email: 'invalid-email-format'
      };
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'PUT', 
        `/users/${testUser.id}`, 
        invalidUpdateData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
    });
  });

  describe('Role Assignment', () => {
    let testUser;

    beforeEach(async () => {
      // Create a test user with minimal role
      const userData = UserFactory.createForRoleAssignment();
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );
      testUser = response.data.data;
      testUsers.push(testUser);
    });

    test('should assign single role to user', async () => {
      const roleData = {
        roles: ['property_manager'],
        replaceExisting: true
      };

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        `/users/${testUser.id}/roles`, 
        roleData
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);

      // Verify role assignment
      const userResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        `/users/${testUser.id}`
      );

      expect(userResponse.data.data.roles).toContain('property_manager');
    });

    test('should assign multiple roles to user', async () => {
      const roleData = {
        roles: ['property_manager', 'maintenance_staff'],
        replaceExisting: true
      };

      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        `/users/${testUser.id}/roles`, 
        roleData
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);

      // Verify role assignment
      const userResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        `/users/${testUser.id}`
      );

      expect(userResponse.data.data.roles).toHaveLength(2);
      expect(userResponse.data.data.roles).toEqual(expect.arrayContaining(['property_manager', 'maintenance_staff']));
    });

    test('should add role without replacing existing', async () => {
      // First assign property_manager role
      await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        `/users/${testUser.id}/roles`, 
        { roles: ['property_manager'], replaceExisting: true }
      );

      // Add maintenance_staff role without replacing
      const addRoleResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        `/users/${testUser.id}/roles`, 
        { roles: ['maintenance_staff'], replaceExisting: false }
      );

      expect(addRoleResponse.status).toBe(200);

      // Verify both roles exist
      const userResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        `/users/${testUser.id}`
      );

      expect(userResponse.data.data.roles).toEqual(expect.arrayContaining(['property_manager', 'maintenance_staff']));
    });

    test('should remove role from user', async () => {
      // First assign multiple roles
      await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        `/users/${testUser.id}/roles`, 
        { roles: ['property_manager', 'maintenance_staff'], replaceExisting: true }
      );

      // Remove one role
      const removeResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'DELETE', 
        `/users/${testUser.id}/roles/maintenance_staff`
      );

      expect(removeResponse.status).toBe(200);

      // Verify role removal
      const userResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        `/users/${testUser.id}`
      );

      expect(userResponse.data.data.roles).toContain('property_manager');
      expect(userResponse.data.data.roles).not.toContain('maintenance_staff');
    });
  });

  describe('Permission Enforcement', () => {
    test('should enforce user creation permissions', async () => {
      // Get token for non-admin user
      const viewerToken = await AuthHelper.getViewerToken();
      const userData = UserFactory.createBasicUser();

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken, 
        'POST', 
        '/users', 
        userData
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/permission|unauthorized|forbidden/i);
    });

    test('should enforce user update permissions', async () => {
      const viewerToken = await AuthHelper.getViewerToken();
      const updateData = { fullName: 'Unauthorized Update' };

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken, 
        'PUT', 
        `/users/${testUsers[0]?.id || 'dummy-id'}`, 
        updateData
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
    });

    test('should enforce user deletion permissions', async () => {
      const viewerToken = await AuthHelper.getViewerToken();

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken, 
        'DELETE', 
        `/users/${testUsers[0]?.id || 'dummy-id'}`
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
    });
  });

  describe('User Deletion', () => {
    test('should delete user', async () => {
      // Create user to delete
      const userData = UserFactory.createViewer();
      const createResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'POST', 
        '/users', 
        userData
      );
      const userId = createResponse.data.data.id;

      // Delete user
      const deleteResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'DELETE', 
        `/users/${userId}`
      );

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.data.success).toBe(true);

      // Verify user is deleted
      const getResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'GET', 
        `/users/${userId}`
      );

      expect(getResponse.status).toBe(404);
    });

    test('should return 404 when deleting non-existent user', async () => {
      const nonExistentId = '99999999-9999-9999-9999-999999999999';
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken, 
        'DELETE', 
        `/users/${nonExistentId}`
      );

      expect(response.status).toBe(404);
      expect(response.data.success).toBe(false);
    });
  });
});
