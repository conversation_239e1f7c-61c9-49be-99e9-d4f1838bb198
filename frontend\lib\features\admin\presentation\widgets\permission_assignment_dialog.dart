import 'package:flutter/material.dart';
import '../../data/role_management_api_service.dart';

class PermissionAssignmentDialog extends StatefulWidget {
  final ApiUserRole role;
  final List<Permission> availablePermissions;
  final Function(List<String>)? onPermissionsUpdated;

  const PermissionAssignmentDialog({
    super.key,
    required this.role,
    required this.availablePermissions,
    this.onPermissionsUpdated,
  });

  @override
  State<PermissionAssignmentDialog> createState() => _PermissionAssignmentDialogState();
}

class _PermissionAssignmentDialogState extends State<PermissionAssignmentDialog> {
  late Set<String> selectedPermissions;
  String searchQuery = '';
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    selectedPermissions = widget.role.permissions.map((p) => p.id).toSet();
  }

  @override
  Widget build(BuildContext context) {
    final filteredPermissions = widget.availablePermissions.where((permission) {
      return permission.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
             permission.resource.toLowerCase().contains(searchQuery.toLowerCase()) ||
             permission.action.toLowerCase().contains(searchQuery.toLowerCase());
    }).toList();

    // Group permissions by resource
    final groupedPermissions = <String, List<Permission>>{};
    for (final permission in filteredPermissions) {
      groupedPermissions.putIfAbsent(permission.resource, () => []).add(permission);
    }

    return AlertDialog(
      title: Text('Manage Permissions - ${widget.role.name}'),
      content: SizedBox(
        width: 600,
        height: 500,
        child: Column(
          children: [
            // Search Bar
            TextField(
              decoration: const InputDecoration(
                labelText: 'Search permissions',
                hintText: 'Search by name, resource, or action',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  searchQuery = value;
                });
              },
            ),

            const SizedBox(height: 16),

            // Permission Count
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Selected: ${selectedPermissions.length} / ${widget.availablePermissions.length}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: () {
                        setState(() {
                          selectedPermissions.clear();
                        });
                      },
                      child: const Text('Clear All'),
                    ),
                    TextButton(
                      onPressed: () {
                        setState(() {
                          selectedPermissions.addAll(
                            widget.availablePermissions.map((p) => p.id),
                          );
                        });
                      },
                      child: const Text('Select All'),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Permissions List
            Expanded(
              child: ListView.builder(
                itemCount: groupedPermissions.keys.length,
                itemBuilder: (context, index) {
                  final resource = groupedPermissions.keys.elementAt(index);
                  final permissions = groupedPermissions[resource]!;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ExpansionTile(
                      title: Text(
                        resource.toUpperCase(),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text('${permissions.length} permissions'),
                      children: permissions.map((permission) {
                        final isSelected = selectedPermissions.contains(permission.id);

                        return CheckboxListTile(
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              if (value == true) {
                                selectedPermissions.add(permission.id);
                              } else {
                                selectedPermissions.remove(permission.id);
                              }
                            });
                          },
                          title: Text(permission.name),
                          subtitle: Text('${permission.action} on ${permission.resource}'),
                          secondary: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getActionColor(permission.action).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              permission.action.toUpperCase(),
                              style: TextStyle(
                                color: _getActionColor(permission.action),
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _updatePermissions,
          child: isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Update Permissions'),
        ),
      ],
    );
  }

  Color _getActionColor(String action) {
    switch (action.toLowerCase()) {
      case 'create':
        return Colors.green;
      case 'read':
        return Colors.blue;
      case 'update':
        return Colors.orange;
      case 'delete':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _updatePermissions() async {
    setState(() {
      isLoading = true;
    });

    try {
      widget.onPermissionsUpdated?.call(selectedPermissions.toList());

      if (mounted) {
        Navigator.of(context).pop(selectedPermissions.toList());
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Permissions updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}

// Show dialog helper function
Future<List<String>?> showPermissionAssignmentDialog(
  BuildContext context, {
  required ApiUserRole role,
  required List<Permission> availablePermissions,
  Function(List<String>)? onPermissionsUpdated,
}) {
  return showDialog<List<String>>(
    context: context,
    builder: (context) => PermissionAssignmentDialog(
      role: role,
      availablePermissions: availablePermissions,
      onPermissionsUpdated: onPermissionsUpdated,
    ),
  );
}
