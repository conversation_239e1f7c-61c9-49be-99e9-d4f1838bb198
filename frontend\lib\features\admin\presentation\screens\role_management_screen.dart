import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/auth/widgets/dynamic_role_based_widget.dart';
import '../providers/role_management_providers.dart';
import '../widgets/role_card.dart';
import '../widgets/create_role_dialog.dart';
import '../widgets/permission_assignment_dialog.dart';
import '../../data/role_management_api_service.dart';

class RoleManagementScreen extends ConsumerStatefulWidget {
  const RoleManagementScreen({super.key});

  @override
  ConsumerState<RoleManagementScreen> createState() => _RoleManagementScreenState();
}

class _RoleManagementScreenState extends ConsumerState<RoleManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  bool _showSystemRoles = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Role & Permission Management'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Roles', icon: Icon(Icons.group)),
            Tab(text: 'Permissions', icon: Icon(Icons.security)),
          ],
        ),
        actions: [
          DynamicRoleBasedWidget(
            requiredPermissions: const ['roles.manage'],
            child: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                ref.invalidate(rolesProvider);
                ref.invalidate(permissionsProvider);
              },
              tooltip: 'Refresh',
            ),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRolesTab(),
          _buildPermissionsTab(),
        ],
      ),
      floatingActionButton: DynamicRoleBasedFAB(
        requiredPermissions: const ['roles.manage'],
        onPressed: () => _showCreateRoleDialog(),
        tooltip: 'Create Role',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildRolesTab() {
    return Column(
      children: [
        _buildRoleFilters(),
        Expanded(
          child: _buildRolesList(),
        ),
      ],
    );
  }

  Widget _buildRoleFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            decoration: const InputDecoration(
              labelText: 'Search roles',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Show System Roles'),
                  value: _showSystemRoles,
                  onChanged: (value) {
                    setState(() {
                      _showSystemRoles = value ?? true;
                    });
                  },
                  dense: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRolesList() {
    final rolesAsync = ref.watch(rolesProvider);

    return rolesAsync.when(
      data: (roles) {
        final filteredRoles = roles.where((role) {
          final matchesSearch = _searchQuery.isEmpty ||
              role.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              (role.description?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);

          final matchesSystemFilter = _showSystemRoles || !role.isSystemRole;

          return matchesSearch && matchesSystemFilter;
        }).toList();

        if (filteredRoles.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.group_off, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No roles found',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredRoles.length,
          itemBuilder: (context, index) {
            final role = filteredRoles[index];
            return RoleCard(
              role: role,
              onTap: () => _showRoleDetails(role),
              onEdit: role.isSystemRole ? null : () => _showEditRoleDialog(role),
              onDelete: role.isSystemRole ? null : () => _showDeleteRoleDialog(role),
              onManagePermissions: () => _showPermissionAssignmentDialog(role),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error loading roles: $error',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(rolesProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsTab() {
    final permissionsAsync = ref.watch(permissionsProvider);

    return permissionsAsync.when(
      data: (permissionResponse) {
        final groupedPermissions = permissionResponse.groupedPermissions;

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: groupedPermissions.keys.length,
          itemBuilder: (context, index) {
            final resource = groupedPermissions.keys.elementAt(index);
            final permissions = groupedPermissions[resource]!;

            return Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: ExpansionTile(
                title: Text(
                  resource.toUpperCase(),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text('${permissions.length} permissions'),
                children: permissions.map((permission) {
                  return ListTile(
                    title: Text(permission.displayName),
                    subtitle: Text(permission.description ?? '${permission.action} ${permission.resource}'),
                    trailing: Chip(
                      label: Text(permission.action.toUpperCase()),
                      backgroundColor: _getActionColor(permission.action),
                    ),
                    onTap: () => _showPermissionDetails(permission),
                  );
                }).toList(),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error loading permissions: $error',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.invalidate(permissionsProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Color _getActionColor(String action) {
    switch (action.toLowerCase()) {
      case 'create':
        return Colors.green.shade100;
      case 'read':
        return Colors.blue.shade100;
      case 'update':
        return Colors.orange.shade100;
      case 'delete':
        return Colors.red.shade100;
      default:
        return Colors.grey.shade100;
    }
  }

  void _showCreateRoleDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateRoleDialog(),
    );
  }

  void _showEditRoleDialog(Role role) {
    showDialog(
      context: context,
      builder: (context) => CreateRoleDialog(),
    );
  }

  void _showDeleteRoleDialog(Role role) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Role'),
        content: Text('Are you sure you want to delete the role "${role.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                await ref.read(roleManagementServiceProvider).deleteRole(role.id);
                ref.invalidate(rolesProvider);
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(content: Text('Role deleted successfully')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(content: Text('Error deleting role: $e')),
                  );
                }
              }
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showRoleDetails(Role role) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(role.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (role.description != null) ...[
              Text('Description: ${role.description}'),
              const SizedBox(height: 8),
            ],
            Text('System Role: ${role.isSystemRole ? 'Yes' : 'No'}'),
            const SizedBox(height: 8),
            Text('Users: ${role.userCount ?? 0}'),
            const SizedBox(height: 8),
            Text('Permissions: ${role.permissionCount ?? 0}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPermissionAssignmentDialog(Role role) {
    final permissionsAsync = ref.read(permissionsProvider);
    permissionsAsync.when(
      data: (permissionResponse) {
        // Convert Role to ApiUserRole for the dialog
        final userRole = ApiUserRole(
          id: role.id,
          name: role.name,
          description: role.description,
          isSystemRole: role.isSystemRole,
          isActive: role.isActive,
          assignedAt: DateTime.now(),
          permissions: role.permissions ?? [],
        );

        showDialog(
          context: context,
          builder: (context) => PermissionAssignmentDialog(
            role: userRole,
            availablePermissions: permissionResponse.permissions,
          ),
        );
      },
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Loading permissions...')),
        );
      },
      error: (error, stack) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading permissions: $error')),
        );
      },
    );
  }

  void _showPermissionDetails(Permission permission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(permission.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Resource: ${permission.resource}'),
            const SizedBox(height: 8),
            Text('Action: ${permission.action}'),
            const SizedBox(height: 8),
            if (permission.description != null) ...[
              Text('Description: ${permission.description}'),
              const SizedBox(height: 8),
            ],
            Text('Assigned to: ${permission.roleCount ?? 0} roles'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
