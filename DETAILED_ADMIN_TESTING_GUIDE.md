# 🔧 SRSR Property Management - Detailed Admin Testing Guide

## 📋 Table of Contents
- [🎯 Overview](#overview)
- [🔧 Pre-Testing Setup](#pre-testing-setup)
- [👤 User Management Testing](#user-management-testing)
- [🎭 Role Management Testing](#role-management-testing)
- [🔐 Permission Configuration Testing](#permission-configuration-testing)
- [📱 Screen Management Testing](#screen-management-testing)
- [🧩 Widget Management Testing](#widget-management-testing)
- [🧪 End-to-End Testing Scenarios](#end-to-end-testing-scenarios)

---

## 🎯 Overview

This guide provides detailed step-by-step instructions for testing the admin functionality of the SRSR Property Management system, including user management, role assignment, permission configuration, and screen/widget management.

### **🔑 Admin Access Required**
- **Login**: `<EMAIL>`
- **Password**: `admin123`
- **Required Permissions**: Full admin access

---

## 🔧 Pre-Testing Setup

### **1. Environment Preparation**
```bash
# Backend setup
cd backend
npm run db:reset:full  # Reset with comprehensive test data
npm run dev           # Start backend server

# Frontend setup
cd frontend
flutter pub get
flutter run          # Start Flutter app
```

### **2. Verify Admin Access**
1. **Open Flutter app**
2. **Login with admin credentials**:
   - Email: `<EMAIL>`
   - Password: `admin123`
3. **Navigate to Admin Dashboard**:
   - Tap **hamburger menu** (☰) → **Admin**
   - OR tap **Admin** from bottom navigation
4. **Verify admin dashboard loads** with system overview

---

## 👤 User Management Testing

### **🎯 Test Case 1: Access User Management**

#### **Steps:**
1. **From Admin Dashboard** → Tap **"Manage Users"** button
2. **OR** → Tap **"Total Users"** stat card
3. **Verify User Management screen opens** with tabs:
   - All Users
   - Pending Approval
   - Active
   - Inactive

#### **Expected Results:**
- ✅ Screen loads with user list
- ✅ Tabs are visible and functional
- ✅ User cards display with actions
- ✅ Search and filter options available

### **🎯 Test Case 2: Create New User**

#### **Steps:**
1. **In User Management screen** → Tap **"+"** (Add User) button
2. **Fill out Create User form**:
   ```
   Full Name: "Test Manager"
   Email: "<EMAIL>"
   Username: "testmanager" (if field exists)
   Phone: "+**********" (if field exists)
   Password: "test123"
   Confirm Password: "test123"
   ```
3. **Select Roles** → Check **"Property Manager"** role
4. **Tap "Create User"** button
5. **Verify success message** appears
6. **Check user appears** in user list

#### **Expected Results:**
- ✅ Form validation works (required fields)
- ✅ Password confirmation validation
- ✅ User created successfully
- ✅ New user appears in "All Users" tab
- ✅ User has assigned role

### **🎯 Test Case 3: Edit Existing User**

#### **Steps:**
1. **Find existing user** in user list (e.g., "<EMAIL>")
2. **Tap "Edit" button** on user card
3. **Modify user details**:
   ```
   Full Name: "Updated Property Manager"
   Phone: "+1987654321"
   ```
4. **Change roles** → Add **"Security Guard"** role
5. **Tap "Save Changes"** button
6. **Verify updates** are reflected

#### **Expected Results:**
- ✅ Edit form pre-populated with current data
- ✅ Role checkboxes reflect current assignments
- ✅ Changes saved successfully
- ✅ Updated information displayed in user list

### **🎯 Test Case 4: User Role Assignment**

#### **Steps:**
1. **Select user** → Tap on user card for "<EMAIL>"
2. **Tap "Manage Roles"** button
3. **In Role Assignment dialog**:
   - **Uncheck** "Maintenance Staff"
   - **Check** "Property Manager"
   - **Check** "Security Guard"
4. **Tap "Update Roles"** button
5. **Verify role changes** in user details

#### **Expected Results:**
- ✅ Role assignment dialog opens
- ✅ Current roles are pre-selected
- ✅ Multiple roles can be assigned
- ✅ Changes are saved and reflected immediately

### **🎯 Test Case 5: User Approval Process**

#### **Steps:**
1. **Navigate to "Pending Approval" tab**
2. **If no pending users** → Create one using registration
3. **For pending user** → Tap **"Approve"** button
4. **In approval dialog**:
   ```
   Select Role: "Maintenance Staff"
   Comments: "Approved for maintenance team"
   ```
5. **Tap "Approve User"** button
6. **Verify user moves** to "Active" tab

#### **Expected Results:**
- ✅ Pending users are listed separately
- ✅ Approval dialog allows role selection
- ✅ Comments can be added
- ✅ Approved user becomes active

---

## 🎭 Role Management Testing

### **🎯 Test Case 6: Access Role Management**

#### **Steps:**
1. **From Admin Dashboard** → Tap **"Manage Roles"** button
2. **OR** → Navigate to **Admin** → **Role Management**
3. **Verify Role Management screen** with tabs:
   - Roles
   - Permissions

#### **Expected Results:**
- ✅ Role list displays with system and custom roles
- ✅ Permission list shows all available permissions
- ✅ Search and filter functionality works

### **🎯 Test Case 7: Create Custom Role**

#### **Steps:**
1. **In Roles tab** → Tap **"+"** (Create Role) button
2. **Fill out Create Role form**:
   ```
   Role Name: "Site Coordinator"
   Description: "Coordinates site activities and reports"
   Is System Role: false (unchecked)
   ```
3. **Select Permissions**:
   - ✅ properties.read
   - ✅ maintenance.read
   - ✅ maintenance.create
   - ✅ attendance.read
   - ✅ reports.generate
4. **Tap "Create Role"** button
5. **Verify new role** appears in role list

#### **Expected Results:**
- ✅ Form validation works
- ✅ Permission selection interface functional
- ✅ Role created with selected permissions
- ✅ New role available for user assignment

### **🎯 Test Case 8: Edit Role Permissions**

#### **Steps:**
1. **Find "Property Manager" role** in role list
2. **Tap "Manage Permissions"** button
3. **In Permission Assignment dialog**:
   - **Add** "fuel.create" permission
   - **Add** "security.read" permission
   - **Remove** "users.read" permission (if present)
4. **Tap "Update Permissions"** button
5. **Verify changes** are saved

#### **Expected Results:**
- ✅ Current permissions are pre-selected
- ✅ Permissions can be added/removed
- ✅ Changes are saved immediately
- ✅ Users with this role get updated permissions

### **🎯 Test Case 9: Test Role Hierarchy**

#### **Steps:**
1. **Create test user** with "Site Coordinator" role
2. **Login as test user**:
   ```
   Email: [created user email]
   Password: [created user password]
   ```
3. **Navigate through app** and verify:
   - ✅ Can access Properties (read-only)
   - ✅ Can access Maintenance (read/create)
   - ✅ Can access Attendance (read-only)
   - ❌ Cannot access User Management
   - ❌ Cannot access Admin screens
4. **Logout and return to admin**

#### **Expected Results:**
- ✅ Role permissions are enforced correctly
- ✅ UI elements hidden for unauthorized actions
- ✅ Navigation restricted appropriately
- ✅ No error messages for hidden features

---

## 🔐 Permission Configuration Testing

### **🎯 Test Case 10: Screen Permission Configuration**

#### **Steps:**
1. **Navigate to Admin** → **Permission Config**
2. **Select "Screen Permissions" tab**
3. **Find "Properties" screen** in list
4. **Tap "Edit" button** on Properties screen
5. **Modify permissions**:
   ```
   Required Permissions:
   - properties.read ✅
   - properties.manage ✅

   Allowed Roles:
   - admin ✅
   - property_manager ✅
   - site_coordinator ✅ (add new)
   ```
6. **Tap "Save Changes"** button
7. **Test with different user roles**

#### **Expected Results:**
- ✅ Screen permission editor opens
- ✅ Current permissions and roles displayed
- ✅ Changes can be made and saved
- ✅ Real-time permission enforcement

### **🎯 Test Case 11: Widget Permission Configuration**

#### **Steps:**
1. **In Permission Config** → **"Widget Permissions" tab**
2. **Find "dashboard.property_stats" widget**
3. **Tap "Edit" button**
4. **Configure widget permissions**:
   ```
   Widget Name: property_stats
   Screen: dashboard
   Required Permissions:
   - properties.read ✅
   - dashboard.view ✅

   Allowed Roles:
   - admin ✅
   - property_manager ✅
   - maintenance_staff ✅ (add)

   Visibility: Enabled ✅
   ```
5. **Save changes** and **test visibility**

#### **Expected Results:**
- ✅ Widget permission editor functional
- ✅ Granular permission control
- ✅ Widget visibility changes immediately
- ✅ Role-based widget display

---

## 📱 Screen Management Testing

### **🎯 Test Case 12: Create Custom Screen**

#### **Steps:**
1. **Navigate to Admin** → **Screen Management**
2. **Tap "+" (Create Screen)** button
3. **Fill out Create Screen form**:
   ```
   Screen Name: "site_reports"
   Title: "Site Reports"
   Route: "/site-reports"
   Description: "Site-specific reporting dashboard"
   Icon: "assessment"

   Required Permissions:
   - reports.read ✅
   - sites.read ✅

   Allowed Roles:
   - admin ✅
   - site_coordinator ✅
   ```
4. **Configure layout**:
   ```
   Layout Type: "grid"
   Columns: 2
   ```
5. **Tap "Create Screen"** button

#### **Expected Results:**
- ✅ Screen creation form functional
- ✅ Permission and role selection works
- ✅ Layout configuration options available
- ✅ New screen appears in screen list

### **🎯 Test Case 13: Screen Route Validation**

#### **Steps:**
1. **In Screen Management** → **Create/Edit screen**
2. **Test route validation**:
   ```
   Valid routes to test:
   - "/custom-dashboard" ✅
   - "/reports/custom" ✅
   - "invalid-route" ❌ (should show error)
   - "/existing-route" ❌ (should show conflict)
   ```
3. **Verify validation messages** appear
4. **Test route uniqueness** checking

#### **Expected Results:**
- ✅ Route format validation works
- ✅ Duplicate route detection
- ✅ Clear error messages
- ✅ Valid routes accepted

---

## 🧩 Widget Management Testing

### **🎯 Test Case 14: Create Custom Widget**

#### **Steps:**
1. **Navigate to Admin** → **Widget Management**
2. **Select "Widgets" tab** → **Tap "+" button**
3. **Fill out Create Widget form**:
   ```
   Widget Name: "fuel_efficiency_chart"
   Title: "Fuel Efficiency Chart"
   Type: "chart"
   Description: "Displays fuel efficiency metrics"

   Properties:
   - dataSource: "generator_fuel"
   - chartType: "line"
   - metrics: ["efficiency", "consumption"]

   Required Permissions:
   - fuel.read ✅
   - reports.read ✅

   Allowed Roles:
   - admin ✅
   - property_manager ✅
   ```
4. **Configure positioning**:
   ```
   Position X: 0
   Position Y: 0
   Width: 2
   Height: 1
   ```
5. **Tap "Create Widget"** button

#### **Expected Results:**
- ✅ Widget creation form complete
- ✅ Widget type selection available
- ✅ Properties configuration functional
- ✅ Position and sizing controls work

### **🎯 Test Case 15: Widget Type Management**

#### **Steps:**
1. **In Widget Management** → **"Widget Types" tab**
2. **Review available widget types**:
   - statCard
   - chart
   - table
   - list
   - custom
3. **Test widget type selection** in widget creation
4. **Verify type-specific properties** appear

#### **Expected Results:**
- ✅ Widget types are listed
- ✅ Type selection affects available properties
- ✅ Type-specific configuration options
- ✅ Validation based on widget type

---

## 🧪 End-to-End Testing Scenarios

### **🎯 Scenario 1: Complete User Lifecycle**

#### **Steps:**
1. **Create new role** "Field Supervisor"
2. **Assign permissions** to role
3. **Create new user** with Field Supervisor role
4. **Configure screen permissions** for user's role
5. **Test user login** and verify access
6. **Modify user permissions** through role
7. **Verify real-time permission updates**

### **🎯 Scenario 2: Dynamic Permission Testing**

#### **Steps:**
1. **Login as Property Manager**
2. **Note available features** and UI elements
3. **Switch to Admin** → **Modify Property Manager role**
4. **Add new permissions** (e.g., user.read)
5. **Return to Property Manager account**
6. **Verify new features** are immediately available
7. **Test without app restart**

### **🎯 Scenario 3: Screen and Widget Integration**

#### **Steps:**
1. **Create custom screen** "Maintenance Dashboard"
2. **Create custom widgets** for the screen
3. **Configure widget permissions** and positioning
4. **Assign screen access** to specific roles
5. **Test screen navigation** and widget visibility
6. **Verify responsive layout** and functionality

---

## ✅ Testing Checklist

### **User Management** ☑️
- [ ] Create user with form validation
- [ ] Edit user details and roles
- [ ] User approval process
- [ ] Role assignment and removal
- [ ] User activation/deactivation
- [ ] User deletion (if permitted)

### **Role Management** ☑️
- [ ] Create custom role
- [ ] Edit role permissions
- [ ] Delete custom role
- [ ] System role protection
- [ ] Permission inheritance testing
- [ ] Role hierarchy validation

### **Permission Configuration** ☑️
- [ ] Screen permission editing
- [ ] Widget permission configuration
- [ ] Real-time permission enforcement
- [ ] Permission validation
- [ ] Role-based access control
- [ ] Permission inheritance

### **Screen Management** ☑️
- [ ] Create custom screen
- [ ] Edit screen properties
- [ ] Route validation
- [ ] Screen permission assignment
- [ ] Layout configuration
- [ ] Screen deletion

### **Widget Management** ☑️
- [ ] Create custom widget
- [ ] Widget type selection
- [ ] Property configuration
- [ ] Position and sizing
- [ ] Widget permission assignment
- [ ] Widget deletion

### **Integration Testing** ☑️
- [ ] End-to-end user lifecycle
- [ ] Real-time permission updates
- [ ] Cross-module functionality
- [ ] Data consistency
- [ ] UI responsiveness
- [ ] Error handling

---

## 🐛 Common Issues & Troubleshooting

### **Permission Issues**
- **Problem**: User can't access expected features
- **Solution**: Check role permissions and screen/widget configurations

### **Form Validation**
- **Problem**: Form doesn't validate properly
- **Solution**: Verify required fields and validation rules

### **Real-time Updates**
- **Problem**: Permission changes don't reflect immediately
- **Solution**: Check provider invalidation and state management

### **Navigation Issues**
- **Problem**: Can't navigate to admin screens
- **Solution**: Verify admin role assignment and route permissions

---

## 📝 Detailed Field-by-Field Instructions

### **🎯 User Creation Form - Exact Steps**

#### **Form Fields and Values:**
1. **Open Create User Dialog**:
   - Location: User Management → "+" button
   - Dialog Title: "Create New User"

2. **Fill Required Fields** (in order):
   ```
   Field: "Full Name *"
   Value: "John Site Manager"
   Validation: Must be 2-50 characters

   Field: "Email *"
   Value: "<EMAIL>"
   Validation: Must be valid email format

   Field: "Username" (if visible)
   Value: "johnsitemanager"
   Validation: Must be unique, 3-30 characters

   Field: "Phone" (if visible)
   Value: "+**********"
   Validation: Valid phone format

   Field: "Password *"
   Value: "SecurePass123!"
   Validation: Min 8 chars, must include uppercase, lowercase, number

   Field: "Confirm Password *"
   Value: "SecurePass123!"
   Validation: Must match password exactly
   ```

3. **Role Selection Section**:
   ```
   Section Title: "Assign Roles"
   Available Roles (checkboxes):
   □ Admin
   □ Property Manager ← SELECT THIS
   □ Maintenance Staff
   □ Security Guard
   □ Office Manager
   □ Site Supervisor ← SELECT THIS TOO
   □ Construction Worker
   □ Office Staff
   □ Viewer
   ```

4. **Additional Settings** (if visible):
   ```
   □ "Send welcome email" ← CHECK THIS
   □ "Require password change on first login" ← CHECK THIS
   □ "Account active immediately" ← CHECK THIS
   ```

5. **Submit Process**:
   - **Tap "Create User"** button
   - **Wait for loading spinner** to complete
   - **Verify success message**: "User created successfully"
   - **Check user appears** in "All Users" tab

### **🎯 Role Permission Assignment - Step by Step**

#### **Accessing Permission Assignment:**
1. **Navigate**: Admin Dashboard → Role Management → Roles tab
2. **Find Role**: Scroll to "Property Manager" role card
3. **Action**: Tap "Manage Permissions" button
4. **Dialog Opens**: "Assign Permissions to Property Manager"

#### **Permission Categories and Selections:**
```
📋 USER PERMISSIONS:
□ users.create
□ users.read ← CHECK THIS
□ users.update
□ users.delete
□ users.approve

🏢 PROPERTY PERMISSIONS:
☑ properties.create ← ALREADY CHECKED
☑ properties.read ← ALREADY CHECKED
☑ properties.update ← ALREADY CHECKED
□ properties.delete ← ADD THIS

🔧 MAINTENANCE PERMISSIONS:
☑ maintenance.create ← ALREADY CHECKED
☑ maintenance.read ← ALREADY CHECKED
☑ maintenance.update ← ALREADY CHECKED
□ maintenance.delete
☑ maintenance.assign ← ALREADY CHECKED
□ maintenance.escalate ← ADD THIS

👥 ATTENDANCE PERMISSIONS:
☑ attendance.create ← ALREADY CHECKED
☑ attendance.read ← ALREADY CHECKED
☑ attendance.update ← ALREADY CHECKED
□ attendance.delete

⛽ FUEL PERMISSIONS:
☑ fuel.create ← ALREADY CHECKED
☑ fuel.read ← ALREADY CHECKED
☑ fuel.update ← ALREADY CHECKED
□ fuel.delete

📊 REPORT PERMISSIONS:
☑ reports.generate ← ALREADY CHECKED
□ reports.export ← ADD THIS

⚙️ SETTINGS PERMISSIONS:
□ settings.configure
□ thresholds.configure ← ADD THIS
```

#### **Save and Verify:**
1. **Tap "Update Permissions"** button
2. **Verify success message**: "Permissions updated successfully"
3. **Check role card** shows updated permission count
4. **Test with user** who has this role

### **🎯 Screen Permission Configuration - Detailed Steps**

#### **Accessing Screen Permissions:**
1. **Navigate**: Admin → Permission Config → Screen Permissions tab
2. **Find Screen**: Look for "Properties" in the list
3. **Screen Card Shows**:
   ```
   Title: "Properties"
   Subtitle: "X permissions, Y roles"
   Status: Enabled/Disabled toggle
   ```

#### **Edit Screen Permissions:**
1. **Tap "Edit" button** on Properties screen card
2. **Dialog Opens**: "Configure Screen Permissions"
3. **Form Fields**:
   ```
   Screen Name: "properties" (read-only)
   Display Title: "Properties"
   Description: "Property management and overview"
   Route: "/properties" (read-only)
   Icon: "business"
   ```

4. **Permission Requirements**:
   ```
   Required Permissions (multi-select):
   ☑ properties.read ← KEEP CHECKED
   □ properties.create ← ADD THIS
   □ properties.update ← ADD THIS
   □ dashboard.view
   ```

5. **Role Access**:
   ```
   Allowed Roles (multi-select):
   ☑ admin ← KEEP CHECKED
   ☑ property_manager ← KEEP CHECKED
   □ maintenance_staff ← ADD THIS
   □ security_guard
   □ office_manager ← ADD THIS
   □ site_supervisor
   □ construction_worker
   □ office_staff
   □ viewer
   ```

6. **Advanced Settings**:
   ```
   □ "Require all permissions" ← CHECK (user needs ALL selected permissions)
   ☑ "Screen enabled" ← KEEP CHECKED
   □ "Show in navigation" ← CHECK
   Priority: 10 (number input)
   ```

### **🎯 Widget Permission Testing - Exact Process**

#### **Widget Permission Configuration:**
1. **Navigate**: Admin → Permission Config → Widget Permissions tab
2. **Find Widget**: "dashboard.property_stats"
3. **Widget Card Shows**:
   ```
   Title: "property_stats"
   Subtitle: "Screen: dashboard • X permissions"
   Status: Enabled/Disabled
   ```

#### **Edit Widget Permissions:**
1. **Tap "Edit" button**
2. **Dialog**: "Configure Widget Permissions"
3. **Form Fields**:
   ```
   Widget Name: "property_stats" (read-only)
   Screen: "dashboard" (read-only)
   Display Title: "Property Statistics"
   Widget Type: "statCard"
   ```

4. **Permission Configuration**:
   ```
   Required Permissions:
   ☑ properties.read ← KEEP
   □ dashboard.view ← ADD
   □ statistics.view ← ADD

   Allowed Roles:
   ☑ admin ← KEEP
   ☑ property_manager ← KEEP
   □ maintenance_staff ← ADD
   □ security_guard
   □ office_manager ← ADD
   ```

5. **Visibility Settings**:
   ```
   ☑ "Widget visible" ← KEEP CHECKED
   ☑ "Widget enabled" ← KEEP CHECKED
   Position X: 0
   Position Y: 0
   Width: 1
   Height: 1
   Order: 1
   ```

### **🎯 Real-Time Permission Testing**

#### **Test Scenario: Live Permission Updates**
1. **Setup**: Have two devices/browsers ready
   - **Device A**: Logged in as admin
   - **Device B**: Logged in as property manager

2. **Initial State Check** (Device B):
   ```
   Navigate to Dashboard
   Note visible widgets:
   - Property Statistics ← Should be visible
   - Recent Activities ← Check visibility
   - Maintenance Summary ← Check visibility
   ```

3. **Permission Modification** (Device A):
   ```
   Admin → Permission Config → Widget Permissions
   Find: "dashboard.recent_activities"
   Edit permissions:
   Remove "property_manager" from allowed roles
   Save changes
   ```

4. **Real-Time Verification** (Device B):
   ```
   Refresh dashboard (pull down to refresh)
   Expected Result:
   - Property Statistics ← Still visible
   - Recent Activities ← Should disappear
   - Maintenance Summary ← Still visible
   ```

5. **Restore and Re-test** (Device A):
   ```
   Add "property_manager" back to recent_activities widget
   Save changes
   ```

6. **Final Verification** (Device B):
   ```
   Refresh dashboard again
   Expected Result:
   - Recent Activities ← Should reappear
   ```

---

## 🔍 Validation Points and Expected Behaviors

### **Form Validation Testing**

#### **User Creation Form Validation:**
```
Test Invalid Email:
Input: "invalid-email"
Expected: Red error text "Please enter a valid email"

Test Short Password:
Input: "123"
Expected: "Password must be at least 8 characters"

Test Password Mismatch:
Password: "ValidPass123!"
Confirm: "DifferentPass123!"
Expected: "Passwords do not match"

Test Duplicate Email:
Input: "<EMAIL>" (existing user)
Expected: "Email already exists"

Test Empty Required Fields:
Leave "Full Name" empty
Expected: "Full name is required"
```

#### **Role Creation Validation:**
```
Test Empty Role Name:
Input: ""
Expected: "Role name is required"

Test Short Role Name:
Input: "A"
Expected: "Role name must be at least 2 characters"

Test Duplicate Role Name:
Input: "Admin" (existing role)
Expected: "Role name already exists"

Test No Permissions Selected:
Select no permissions
Expected: Warning "Role should have at least one permission"
```

### **Permission Enforcement Testing**

#### **Screen Access Testing:**
```
Test Unauthorized Screen Access:
1. Login as "viewer" role
2. Try to navigate to /admin/users
Expected: Redirect to dashboard or show "Access Denied"

Test Partial Permissions:
1. Create role with only "properties.read"
2. Login as user with this role
3. Navigate to Properties screen
Expected: Can view properties but no "Create" button visible
```

#### **Widget Visibility Testing:**
```
Test Widget Permission Enforcement:
1. Remove "maintenance.read" from user's role
2. Navigate to dashboard
Expected: Maintenance-related widgets hidden

Test Dynamic Widget Updates:
1. Admin adds permission to user's role
2. User refreshes screen
Expected: Previously hidden widgets now appear
```

---

## 🚨 Critical Test Scenarios

### **🔥 Security Testing**

#### **Test Case: Permission Escalation Prevention**
```
Scenario: User tries to access admin functions
Steps:
1. Login as "maintenance_staff"
2. Try to access /admin/users directly
3. Try to modify URL to access restricted screens
Expected: All attempts blocked, user redirected
```

#### **Test Case: Role Modification Security**
```
Scenario: Non-admin tries to modify roles
Steps:
1. Login as "property_manager"
2. Navigate to any admin screen
Expected: Navigation items hidden, direct access blocked
```

### **🔄 Data Consistency Testing**

#### **Test Case: Role Deletion Impact**
```
Scenario: Delete role assigned to users
Steps:
1. Create custom role "Test Role"
2. Assign to test user
3. Delete the role
Expected: User loses role, permissions updated immediately
```

#### **Test Case: Permission Inheritance**
```
Scenario: User with multiple roles
Steps:
1. Assign user both "viewer" and "property_manager" roles
2. Check effective permissions
Expected: User gets combined permissions from both roles
```

---

## 📊 Testing Results Documentation

### **Test Results Template**
```
Test Case: [Test Case Name]
Date: [Date]
Tester: [Your Name]
Environment: [Flutter App Version]

Steps Executed:
1. [Step 1] ✅/❌
2. [Step 2] ✅/❌
3. [Step 3] ✅/❌

Expected Results:
- [Expected 1] ✅/❌
- [Expected 2] ✅/❌

Actual Results:
[Description of what actually happened]

Issues Found:
- [Issue 1 - Severity: High/Medium/Low]
- [Issue 2 - Severity: High/Medium/Low]

Screenshots:
[Attach relevant screenshots]

Notes:
[Additional observations]
```

### **Success Criteria Checklist**
```
✅ All forms validate input correctly
✅ User creation and editing works
✅ Role assignment functions properly
✅ Permission changes take effect immediately
✅ Screen access is properly controlled
✅ Widget visibility respects permissions
✅ Security measures prevent unauthorized access
✅ Data consistency maintained across operations
✅ UI provides clear feedback for all actions
✅ Error handling is graceful and informative
```

---

*This comprehensive guide provides exact field-by-field instructions for testing all admin functionality. Use the detailed steps and validation points to ensure thorough testing of the user management, role assignment, and permission system.*
