import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../models/user_data.dart';

class PropertiesPage {
  final WidgetTester tester;
  static final _logger = Logger();

  PropertiesPage(this.tester);

  // Main screen locators
  Finder get screen => find.byKey(const Key('properties_screen'));
  Finder get propertiesList => find.byKey(const Key('properties_list'));
  Finder get addPropertyFab => find.byKey(const Key('add_property_fab'));
  Finder get searchField => find.byKey(const Key('search_field'));
  Finder get refreshButton => find.byKey(const Key('refresh_button'));
  
  // Filter locators
  Finder get allFilterChip => find.byKey(const Key('filter_all'));
  Finder get residentialFilterChip => find.byKey(const Key('filter_residential'));
  Finder get officeFilterChip => find.by<PERSON>ey(const Key('filter_office'));
  Finder get constructionFilterChip => find.byKey(const Key('filter_construction'));
  
  // Property card locators
  Finder propertyCard(String propertyName) => find.byKey(Key('property_card_$propertyName'));
  Finder editPropertyButton(String propertyName) => find.byKey(Key('edit_property_$propertyName'));
  Finder deletePropertyButton(String propertyName) => find.byKey(Key('delete_property_$propertyName'));
  
  // Add property dialog locators
  Finder get addPropertyDialog => find.byKey(const Key('add_property_dialog'));
  Finder get propertyNameField => find.byKey(const Key('property_name_field'));
  Finder get propertyTypeDropdown => find.byKey(const Key('property_type_dropdown'));
  Finder get propertyAddressField => find.byKey(const Key('property_address_field'));
  Finder get propertyCityField => find.byKey(const Key('property_city_field'));
  Finder get propertyStateField => find.byKey(const Key('property_state_field'));
  Finder get propertyZipField => find.byKey(const Key('property_zip_field'));
  Finder get propertyDescriptionField => find.byKey(const Key('property_description_field'));
  Finder get savePropertyButton => find.text('Save Property');
  Finder get cancelButton => find.text('Cancel');
  
  // Property details locators
  Finder get propertyDetailsScreen => find.byKey(const Key('property_details_screen'));
  Finder get overviewTab => find.text('Overview');
  Finder get servicesTab => find.text('Services');
  Finder get activityTab => find.text('Activity');
  Finder get reportsTab => find.text('Reports');
  
  // Action chips in property details
  Finder get maintenanceActionChip => find.byKey(const Key('maintenance_action_chip'));
  Finder get attendanceActionChip => find.byKey(const Key('attendance_action_chip'));
  Finder get fuelActionChip => find.byKey(const Key('fuel_action_chip'));
  Finder get reportsActionChip => find.byKey(const Key('reports_action_chip'));
  
  // Verification locators
  Finder get successMessage => find.byKey(const Key('success_message'));
  Finder get errorMessage => find.byKey(const Key('error_message'));
  Finder get noPropertiesMessage => find.text('No properties found');

  /// Navigate to properties screen
  Future<void> navigateToProperties() async {
    _logger.i('Navigating to properties screen');
    
    final propertiesNavItem = find.text('Properties');
    if (propertiesNavItem.evaluate().isNotEmpty) {
      await tester.tap(propertiesNavItem);
      await tester.pumpAndSettle();
    }
    
    await _verifyScreenLoaded();
  }

  /// Create a new property
  Future<void> createProperty(PropertyData propertyData) async {
    _logger.i('Creating property: ${propertyData.name}');
    
    await _openAddPropertyDialog();
    await _fillPropertyForm(propertyData);
    await _submitPropertyForm();
    await _verifyPropertyCreated(propertyData.name);
  }

  /// Open add property dialog
  Future<void> _openAddPropertyDialog() async {
    _logger.d('Opening add property dialog');
    
    expect(addPropertyFab, findsOneWidget, reason: 'Add property FAB should be present');
    await tester.tap(addPropertyFab);
    await tester.pumpAndSettle();
    
    expect(addPropertyDialog, findsOneWidget, reason: 'Add property dialog should open');
  }

  /// Fill property creation form
  Future<void> _fillPropertyForm(PropertyData propertyData) async {
    _logger.d('Filling property form for: ${propertyData.name}');
    
    await tester.enterText(propertyNameField, propertyData.name);
    await tester.enterText(propertyAddressField, propertyData.address);
    await tester.enterText(propertyCityField, propertyData.city);
    await tester.enterText(propertyStateField, propertyData.state);
    await tester.enterText(propertyZipField, propertyData.zipCode);
    await tester.enterText(propertyDescriptionField, propertyData.description);
    
    // Select property type
    await tester.tap(propertyTypeDropdown);
    await tester.pumpAndSettle();
    final typeOption = find.text(propertyData.type);
    if (typeOption.evaluate().isNotEmpty) {
      await tester.tap(typeOption);
      await tester.pumpAndSettle();
    }
    
    await tester.pumpAndSettle();
  }

  /// Submit property creation form
  Future<void> _submitPropertyForm() async {
    _logger.d('Submitting property form');
    
    expect(savePropertyButton, findsOneWidget, reason: 'Save property button should be present');
    await tester.tap(savePropertyButton);
    await tester.pumpAndSettle();
    
    // Wait for form processing
    await tester.pump(const Duration(seconds: 2));
    await tester.pumpAndSettle();
  }

  /// Verify property was created successfully
  Future<void> _verifyPropertyCreated(String propertyName) async {
    _logger.d('Verifying property created: $propertyName');
    
    // Check for success message
    expect(successMessage, findsOneWidget, reason: 'Success message should appear');
    
    // Verify property appears in list
    await verifyPropertyExists(propertyName);
  }

  /// Verify property exists in the list
  Future<void> verifyPropertyExists(String propertyName) async {
    _logger.d('Verifying property exists: $propertyName');
    
    // Search for property if search field is available
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, propertyName);
      await tester.pumpAndSettle();
    }
    
    // Scroll to find property if needed
    await _scrollToFindProperty(propertyName);
    
    expect(find.text(propertyName), findsOneWidget, reason: 'Property $propertyName should be visible in list');
  }

  /// Scroll to find property in list
  Future<void> _scrollToFindProperty(String propertyName) async {
    final propertyText = find.text(propertyName);
    
    if (propertyText.evaluate().isEmpty && propertiesList.evaluate().isNotEmpty) {
      await tester.dragUntilVisible(
        propertyText,
        propertiesList,
        const Offset(0, -300),
        maxIteration: 10,
      );
    }
  }

  /// Filter properties by type
  Future<void> filterPropertiesByType(String type) async {
    _logger.d('Filtering properties by type: $type');
    
    Finder filterChip;
    switch (type.toLowerCase()) {
      case 'residential':
        filterChip = residentialFilterChip;
        break;
      case 'office':
        filterChip = officeFilterChip;
        break;
      case 'construction':
        filterChip = constructionFilterChip;
        break;
      default:
        filterChip = allFilterChip;
    }
    
    if (filterChip.evaluate().isNotEmpty) {
      await tester.tap(filterChip);
      await tester.pumpAndSettle();
    }
  }

  /// Search for properties
  Future<void> searchProperties(String query) async {
    _logger.d('Searching for properties: $query');
    
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, query);
      await tester.pumpAndSettle();
    }
  }

  /// Clear search
  Future<void> clearSearch() async {
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, '');
      await tester.pumpAndSettle();
    }
  }

  /// Navigate to property details
  Future<void> navigateToPropertyDetails(String propertyName) async {
    _logger.i('Navigating to property details: $propertyName');
    
    await _scrollToFindProperty(propertyName);
    
    final propertyCardFinder = propertyCard(propertyName);
    expect(propertyCardFinder, findsOneWidget, reason: 'Property card should be present');
    
    await tester.tap(propertyCardFinder);
    await tester.pumpAndSettle();
    
    expect(propertyDetailsScreen, findsOneWidget, reason: 'Property details screen should load');
  }

  /// Switch to overview tab in property details
  Future<void> switchToOverviewTab() async {
    if (overviewTab.evaluate().isNotEmpty) {
      await tester.tap(overviewTab);
      await tester.pumpAndSettle();
    }
  }

  /// Switch to services tab in property details
  Future<void> switchToServicesTab() async {
    if (servicesTab.evaluate().isNotEmpty) {
      await tester.tap(servicesTab);
      await tester.pumpAndSettle();
    }
  }

  /// Switch to activity tab in property details
  Future<void> switchToActivityTab() async {
    if (activityTab.evaluate().isNotEmpty) {
      await tester.tap(activityTab);
      await tester.pumpAndSettle();
    }
  }

  /// Switch to reports tab in property details
  Future<void> switchToReportsTab() async {
    if (reportsTab.evaluate().isNotEmpty) {
      await tester.tap(reportsTab);
      await tester.pumpAndSettle();
    }
  }

  /// Navigate to maintenance from property details
  Future<void> navigateToMaintenanceFromProperty() async {
    _logger.d('Navigating to maintenance from property');
    
    if (maintenanceActionChip.evaluate().isNotEmpty) {
      await tester.tap(maintenanceActionChip);
      await tester.pumpAndSettle();
    }
  }

  /// Navigate to attendance from property details
  Future<void> navigateToAttendanceFromProperty() async {
    _logger.d('Navigating to attendance from property');
    
    if (attendanceActionChip.evaluate().isNotEmpty) {
      await tester.tap(attendanceActionChip);
      await tester.pumpAndSettle();
    }
  }

  /// Navigate to fuel monitoring from property details
  Future<void> navigateToFuelFromProperty() async {
    _logger.d('Navigating to fuel monitoring from property');
    
    if (fuelActionChip.evaluate().isNotEmpty) {
      await tester.tap(fuelActionChip);
      await tester.pumpAndSettle();
    }
  }

  /// Edit existing property
  Future<void> editProperty(String propertyName, PropertyData newData) async {
    _logger.i('Editing property: $propertyName');
    
    await _findAndTapEditButton(propertyName);
    await _fillPropertyForm(newData);
    await _submitEditForm();
    await _verifyPropertyUpdated(newData.name);
  }

  /// Find and tap edit button for property
  Future<void> _findAndTapEditButton(String propertyName) async {
    await _scrollToFindProperty(propertyName);
    
    final editButton = editPropertyButton(propertyName);
    expect(editButton, findsOneWidget, reason: 'Edit button should be present for property $propertyName');
    
    await tester.tap(editButton);
    await tester.pumpAndSettle();
  }

  /// Submit edit form
  Future<void> _submitEditForm() async {
    final updateButton = find.text('Update Property');
    expect(updateButton, findsOneWidget, reason: 'Update property button should be present');
    
    await tester.tap(updateButton);
    await tester.pumpAndSettle();
  }

  /// Verify property was updated
  Future<void> _verifyPropertyUpdated(String propertyName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after update');
    await verifyPropertyExists(propertyName);
  }

  /// Delete property
  Future<void> deleteProperty(String propertyName) async {
    _logger.i('Deleting property: $propertyName');
    
    await _findAndTapDeleteButton(propertyName);
    await _confirmDeletion();
    await _verifyPropertyDeleted(propertyName);
  }

  /// Find and tap delete button for property
  Future<void> _findAndTapDeleteButton(String propertyName) async {
    await _scrollToFindProperty(propertyName);
    
    final deleteButton = deletePropertyButton(propertyName);
    expect(deleteButton, findsOneWidget, reason: 'Delete button should be present for property $propertyName');
    
    await tester.tap(deleteButton);
    await tester.pumpAndSettle();
  }

  /// Confirm deletion in dialog
  Future<void> _confirmDeletion() async {
    final confirmButton = find.text('Delete');
    expect(confirmButton, findsOneWidget, reason: 'Confirm delete button should be present');
    
    await tester.tap(confirmButton);
    await tester.pumpAndSettle();
  }

  /// Verify property was deleted
  Future<void> _verifyPropertyDeleted(String propertyName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after deletion');
    expect(find.text(propertyName), findsNothing, reason: 'Property $propertyName should no longer be visible');
  }

  /// Refresh properties list
  Future<void> refreshProperties() async {
    _logger.d('Refreshing properties list');
    
    if (refreshButton.evaluate().isNotEmpty) {
      await tester.tap(refreshButton);
      await tester.pumpAndSettle();
    }
  }

  /// Verify screen is loaded
  Future<void> _verifyScreenLoaded() async {
    expect(screen, findsOneWidget, reason: 'Properties screen should be loaded');
  }

  /// Verify form validation errors
  Future<void> verifyValidationError(String expectedError) async {
    expect(find.text(expectedError), findsOneWidget, 
           reason: 'Validation error should be displayed: $expectedError');
  }

  /// Cancel property creation
  Future<void> cancelPropertyCreation() async {
    if (cancelButton.evaluate().isNotEmpty) {
      await tester.tap(cancelButton);
      await tester.pumpAndSettle();
    }
  }

  /// Get property count from UI
  Future<int> getPropertyCount() async {
    final countText = find.byKey(const Key('property_count'));
    if (countText.evaluate().isNotEmpty) {
      // Parse count from text
      // Implementation would depend on UI format
    }
    return 0; // Placeholder
  }

  /// Verify no properties message
  Future<void> verifyNoPropertiesMessage() async {
    expect(noPropertiesMessage, findsOneWidget, reason: 'No properties message should be displayed');
  }
}
