import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../lib/helpers/auth_helper.dart';
import '../../lib/helpers/navigation_helper.dart';
import '../../lib/page_objects/attendance_page.dart';
import '../../lib/factories/user_data_factory.dart';
import '../../lib/models/user_data.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Attendance Screen Tests', () {
    late AttendancePage attendancePage;
    final List<String> createdAttendanceEmployees = [];

    setUpAll(() async {
      // Setup test environment
    });

    setUp(() async {
      // Start fresh for each test
    });

    tearDown(() async {
      // Cleanup created attendance records after each test
      if (createdAttendanceEmployees.isNotEmpty) {
        try {
          await AuthHelper.loginAsAdmin(tester);
          await NavigationHelper.navigateToAttendance(tester);
          attendancePage = AttendancePage(tester);
          
          for (final employeeName in createdAttendanceEmployees) {
            try {
              await attendancePage.deleteAttendanceRecord(employeeName);
            } catch (e) {
              print('Failed to cleanup attendance for $employeeName: $e');
            }
          }
          createdAttendanceEmployees.clear();
        } catch (e) {
          print('Failed to cleanup test data: $e');
        }
      }
    });

    testWidgets('Admin can create present attendance record', (tester) async {
      // Step 1: Login as admin
      await AuthHelper.loginAsAdmin(tester);
      
      // Step 2: Navigate to attendance
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Step 3: Create test attendance data
      final attendanceData = AttendanceDataFactory.createPresent();
      createdAttendanceEmployees.add(attendanceData.employeeName);
      
      // Step 4: Create attendance record through UI
      await attendancePage.createAttendanceRecord(attendanceData);
      
      // Step 5: Verify attendance creation
      await attendancePage.verifyAttendanceExists(attendanceData.employeeName);
    });

    testWidgets('Property manager can create absent attendance record', (tester) async {
      await AuthHelper.loginWithRole(tester, 'property_manager');
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      final attendanceData = AttendanceDataFactory.createAbsent();
      createdAttendanceEmployees.add(attendanceData.employeeName);
      
      await attendancePage.createAttendanceRecord(attendanceData);
      await attendancePage.verifyAttendanceExists(attendanceData.employeeName);
    });

    testWidgets('Admin can create late attendance record', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      final attendanceData = AttendanceDataFactory.createLate();
      createdAttendanceEmployees.add(attendanceData.employeeName);
      
      await attendancePage.createAttendanceRecord(attendanceData);
      await attendancePage.verifyAttendanceExists(attendanceData.employeeName);
    });

    testWidgets('Attendance creation validates required fields', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Try to create attendance with empty form
      await tester.tap(attendancePage.addAttendanceFab);
      await tester.pumpAndSettle();
      
      await tester.tap(attendancePage.saveAttendanceButton);
      await tester.pumpAndSettle();
      
      // Verify validation errors
      await attendancePage.verifyValidationError('Employee is required');
      await attendancePage.verifyValidationError('Date is required');
    });

    testWidgets('Admin can switch between attendance tabs', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Test switching between tabs
      await attendancePage.switchToAllRecordsTab();
      await attendancePage.switchToSiteAttendanceTab();
      await attendancePage.switchToOfficeAttendanceTab();
      await attendancePage.switchToAllRecordsTab();
    });

    testWidgets('Admin can filter attendance by property', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create attendance records for different properties
      final siteAttendance = AttendanceDataFactory.createPresent();
      siteAttendance.copyWith(propertyName: 'Test Site Property');
      
      final officeAttendance = AttendanceDataFactory.createPresent();
      officeAttendance.copyWith(propertyName: 'Test Office Property');
      
      createdAttendanceEmployees.addAll([
        siteAttendance.employeeName,
        officeAttendance.employeeName,
      ]);
      
      await attendancePage.createAttendanceRecord(siteAttendance);
      await attendancePage.createAttendanceRecord(officeAttendance);
      
      // Test filtering by property
      await attendancePage.filterAttendanceByProperty('Test Site Property');
      await attendancePage.verifyAttendanceExists(siteAttendance.employeeName);
      
      await attendancePage.filterAttendanceByProperty('Test Office Property');
      await attendancePage.verifyAttendanceExists(officeAttendance.employeeName);
    });

    testWidgets('Admin can filter attendance by status', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create attendance records with different statuses
      final presentAttendance = AttendanceDataFactory.createPresent();
      final absentAttendance = AttendanceDataFactory.createAbsent();
      final lateAttendance = AttendanceDataFactory.createLate();
      
      createdAttendanceEmployees.addAll([
        presentAttendance.employeeName,
        absentAttendance.employeeName,
        lateAttendance.employeeName,
      ]);
      
      await attendancePage.createAttendanceRecord(presentAttendance);
      await attendancePage.createAttendanceRecord(absentAttendance);
      await attendancePage.createAttendanceRecord(lateAttendance);
      
      // Test filtering by status
      await attendancePage.filterAttendanceByStatus('Present');
      await attendancePage.verifyAttendanceExists(presentAttendance.employeeName);
      
      await attendancePage.filterAttendanceByStatus('Absent');
      await attendancePage.verifyAttendanceExists(absentAttendance.employeeName);
      
      await attendancePage.filterAttendanceByStatus('Late');
      await attendancePage.verifyAttendanceExists(lateAttendance.employeeName);
    });

    testWidgets('Admin can search for attendance records', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create test attendance
      final attendanceData = AttendanceDataFactory.createPresent();
      createdAttendanceEmployees.add(attendanceData.employeeName);
      await attendancePage.createAttendanceRecord(attendanceData);
      
      // Search for attendance
      await attendancePage.searchAttendance(attendanceData.employeeName);
      await attendancePage.verifyAttendanceExists(attendanceData.employeeName);
      
      // Clear search
      await attendancePage.clearSearch();
    });

    testWidgets('User can perform quick check-in and check-out', (tester) async {
      await AuthHelper.loginWithRole(tester, 'property_manager');
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Perform quick check-in
      await attendancePage.performQuickCheckIn();
      
      // Perform quick check-out
      await attendancePage.performQuickCheckOut();
    });

    testWidgets('Admin can view today\'s attendance', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // View today's attendance
      await attendancePage.viewTodayAttendance();
    });

    testWidgets('Admin can edit existing attendance record', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create attendance first
      final originalAttendanceData = AttendanceDataFactory.createPresent();
      createdAttendanceEmployees.add(originalAttendanceData.employeeName);
      await attendancePage.createAttendanceRecord(originalAttendanceData);
      
      // Edit attendance
      final updatedAttendanceData = originalAttendanceData.copyWith(
        status: 'Late',
        notes: 'Updated attendance record for testing',
        checkInTime: '10:30',
      );
      
      await attendancePage.editAttendanceRecord(originalAttendanceData.employeeName, updatedAttendanceData);
      await attendancePage.verifyAttendanceExists(updatedAttendanceData.employeeName);
    });

    testWidgets('Admin can perform bulk mark present operation', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create multiple attendance records
      final employees = ['Employee 1', 'Employee 2', 'Employee 3'];
      createdAttendanceEmployees.addAll(employees);
      
      for (final employee in employees) {
        final attendanceData = AttendanceDataFactory.createAbsent();
        final updatedData = attendanceData.copyWith(employeeName: employee);
        await attendancePage.createAttendanceRecord(updatedData);
      }
      
      // Perform bulk mark present
      await attendancePage.bulkMarkPresent(employees);
    });

    testWidgets('Admin can export attendance data', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create some attendance data
      final attendanceData = AttendanceDataFactory.createPresent();
      createdAttendanceEmployees.add(attendanceData.employeeName);
      await attendancePage.createAttendanceRecord(attendanceData);
      
      // Export attendance data
      await attendancePage.exportAttendanceData();
    });

    testWidgets('Attendance screen displays statistics correctly', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create attendance records with different statuses
      final presentAttendance = AttendanceDataFactory.createPresent();
      final absentAttendance = AttendanceDataFactory.createAbsent();
      final lateAttendance = AttendanceDataFactory.createLate();
      
      createdAttendanceEmployees.addAll([
        presentAttendance.employeeName,
        absentAttendance.employeeName,
        lateAttendance.employeeName,
      ]);
      
      await attendancePage.createAttendanceRecord(presentAttendance);
      await attendancePage.createAttendanceRecord(absentAttendance);
      await attendancePage.createAttendanceRecord(lateAttendance);
      
      // Verify statistics are displayed
      await attendancePage.verifyAttendanceStatistics();
    });

    testWidgets('Maintenance staff has limited attendance access', (tester) async {
      // Login as maintenance staff
      await AuthHelper.loginWithRole(tester, 'maintenance_staff');
      
      // Verify maintenance staff has limited access to attendance
      await NavigationHelper.verifyRestrictedScreens(tester, ['attendance']);
    });

    testWidgets('Viewer has read-only access to attendance', (tester) async {
      // Login as viewer
      await AuthHelper.loginWithRole(tester, 'viewer');
      
      // Verify viewer has limited access to attendance
      await NavigationHelper.verifyRestrictedScreens(tester, ['attendance']);
    });

    testWidgets('Attendance screen handles empty state correctly', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // If no attendance records exist, should show appropriate message
      await attendancePage.navigateToAttendance();
    });

    testWidgets('Attendance screen supports refresh functionality', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Test refresh functionality
      await attendancePage.refreshAttendance();
    });

    testWidgets('Attendance deletion requires confirmation', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create attendance to delete
      final attendanceData = AttendanceDataFactory.createPresent();
      await attendancePage.createAttendanceRecord(attendanceData);
      
      // Delete attendance (no need to add to cleanup list since it's being deleted)
      await attendancePage.deleteAttendanceRecord(attendanceData.employeeName);
      
      // Verify attendance is deleted
      expect(find.text(attendanceData.employeeName), findsNothing);
    });

    testWidgets('Attendance supports date-based filtering', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      // Create attendance for today
      final todayAttendance = AttendanceDataFactory.createPresent();
      createdAttendanceEmployees.add(todayAttendance.employeeName);
      await attendancePage.createAttendanceRecord(todayAttendance);
      
      // Filter by today's date
      await attendancePage.filterAttendanceByDate(DateTime.now().toString().split(' ')[0]);
      await attendancePage.verifyAttendanceExists(todayAttendance.employeeName);
    });
  });
}
