const axios = require('axios');
const config = require('../config/test.config');

class AuthHelper {
  constructor() {
    this.tokens = new Map();
    this.baseURL = config.apiBaseUrl;
  }

  /**
   * Get admin authentication token
   */
  async getAdminToken() {
    if (this.tokens.has('admin')) {
      return this.tokens.get('admin');
    }

    const token = await this.login(
      config.testUsers.admin.email,
      config.testUsers.admin.password
    );
    
    this.tokens.set('admin', token);
    return token;
  }

  /**
   * Get property manager authentication token
   */
  async getPropertyManagerToken() {
    if (this.tokens.has('propertyManager')) {
      return this.tokens.get('propertyManager');
    }

    const token = await this.login(
      config.testUsers.propertyManager.email,
      config.testUsers.propertyManager.password
    );
    
    this.tokens.set('propertyManager', token);
    return token;
  }

  /**
   * Get maintenance staff authentication token
   */
  async getMaintenanceStaffToken() {
    if (this.tokens.has('maintenanceStaff')) {
      return this.tokens.get('maintenanceStaff');
    }

    const token = await this.login(
      config.testUsers.maintenanceStaff.email,
      config.testUsers.maintenanceStaff.password
    );
    
    this.tokens.set('maintenanceStaff', token);
    return token;
  }

  /**
   * Get viewer authentication token
   */
  async getViewerToken() {
    if (this.tokens.has('viewer')) {
      return this.tokens.get('viewer');
    }

    const token = await this.login(
      config.testUsers.viewer.email,
      config.testUsers.viewer.password
    );
    
    this.tokens.set('viewer', token);
    return token;
  }

  /**
   * Login with email and password
   */
  async login(email, password) {
    try {
      const response = await axios.post(`${this.baseURL}/auth/login`, {
        email,
        password
      }, {
        timeout: config.timeouts.medium,
        headers: config.api.defaultHeaders
      });

      if (response.data && response.data.token) {
        return response.data.token;
      } else if (response.data && response.data.data && response.data.data.token) {
        return response.data.data.token;
      } else {
        throw new Error('No token found in login response');
      }
    } catch (error) {
      console.error(`Login failed for ${email}:`, error.message);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    }
  }

  /**
   * Login with custom credentials
   */
  async loginWithCredentials(email, password) {
    return await this.login(email, password);
  }

  /**
   * Get authentication headers for API requests
   */
  getAuthHeaders(token) {
    return {
      ...config.api.defaultHeaders,
      'Authorization': `Bearer ${token}`
    };
  }

  /**
   * Make authenticated API request
   */
  async makeAuthenticatedRequest(token, method, endpoint, data = null) {
    const headers = this.getAuthHeaders(token);
    const url = `${this.baseURL}${endpoint}`;

    const requestConfig = {
      method: method.toUpperCase(),
      url,
      headers,
      timeout: config.timeouts.medium,
      validateStatus: config.api.validateStatus
    };

    if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      requestConfig.data = data;
    }

    if (config.logging.logRequests) {
      console.log(`API Request: ${method.toUpperCase()} ${url}`);
      if (data) console.log('Request data:', JSON.stringify(data, null, 2));
    }

    try {
      const response = await axios(requestConfig);
      
      if (config.logging.logResponses) {
        console.log(`API Response: ${response.status}`);
        console.log('Response data:', JSON.stringify(response.data, null, 2));
      }

      return response;
    } catch (error) {
      console.error(`API Request failed: ${method.toUpperCase()} ${url}`);
      console.error('Error:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      throw error;
    }
  }

  /**
   * Verify token is valid
   */
  async verifyToken(token) {
    try {
      const response = await this.makeAuthenticatedRequest(token, 'GET', '/auth/verify');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get user info from token
   */
  async getUserInfo(token) {
    try {
      const response = await this.makeAuthenticatedRequest(token, 'GET', '/auth/me');
      return response.data;
    } catch (error) {
      console.error('Failed to get user info:', error.message);
      throw error;
    }
  }

  /**
   * Logout (invalidate token)
   */
  async logout(token) {
    try {
      await this.makeAuthenticatedRequest(token, 'POST', '/auth/logout');
    } catch (error) {
      // Logout might fail if token is already invalid, which is okay
      console.warn('Logout failed:', error.message);
    }
  }

  /**
   * Clear all cached tokens
   */
  clearTokens() {
    this.tokens.clear();
  }

  /**
   * Refresh token if supported
   */
  async refreshToken(token) {
    try {
      const response = await this.makeAuthenticatedRequest(token, 'POST', '/auth/refresh');
      if (response.data && response.data.token) {
        return response.data.token;
      } else if (response.data && response.data.data && response.data.data.token) {
        return response.data.data.token;
      }
      throw new Error('No token found in refresh response');
    } catch (error) {
      console.error('Token refresh failed:', error.message);
      throw error;
    }
  }

  /**
   * Test authentication with all user types
   */
  async testAllUserAuthentication() {
    const results = {};
    
    try {
      results.admin = await this.getAdminToken();
      console.log('✅ Admin authentication successful');
    } catch (error) {
      results.admin = null;
      console.error('❌ Admin authentication failed:', error.message);
    }

    try {
      results.propertyManager = await this.getPropertyManagerToken();
      console.log('✅ Property Manager authentication successful');
    } catch (error) {
      results.propertyManager = null;
      console.error('❌ Property Manager authentication failed:', error.message);
    }

    try {
      results.maintenanceStaff = await this.getMaintenanceStaffToken();
      console.log('✅ Maintenance Staff authentication successful');
    } catch (error) {
      results.maintenanceStaff = null;
      console.error('❌ Maintenance Staff authentication failed:', error.message);
    }

    try {
      results.viewer = await this.getViewerToken();
      console.log('✅ Viewer authentication successful');
    } catch (error) {
      results.viewer = null;
      console.error('❌ Viewer authentication failed:', error.message);
    }

    return results;
  }

  /**
   * Create test user and return token
   */
  async createTestUserAndGetToken(userData) {
    // First get admin token to create user
    const adminToken = await this.getAdminToken();
    
    // Create user
    const createResponse = await this.makeAuthenticatedRequest(
      adminToken, 
      'POST', 
      '/users', 
      userData
    );
    
    if (createResponse.status !== 201) {
      throw new Error(`Failed to create test user: ${createResponse.status}`);
    }

    // Login as new user to get token
    const userToken = await this.login(userData.email, userData.password);
    
    return {
      user: createResponse.data.data || createResponse.data,
      token: userToken
    };
  }

  /**
   * Delete test user
   */
  async deleteTestUser(userId) {
    const adminToken = await this.getAdminToken();
    
    try {
      await this.makeAuthenticatedRequest(adminToken, 'DELETE', `/users/${userId}`);
    } catch (error) {
      console.warn(`Failed to delete test user ${userId}:`, error.message);
    }
  }
}

module.exports = new AuthHelper();
