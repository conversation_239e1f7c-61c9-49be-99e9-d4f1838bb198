require('dotenv').config();

const config = {
  environments: {
    local: {
      backend: {
        host: process.env.TEST_BACKEND_HOST || 'localhost',
        port: process.env.TEST_BACKEND_PORT || 3000,
        protocol: process.env.TEST_BACKEND_PROTOCOL || 'http',
        apiPath: process.env.TEST_API_PATH || '/api'
      },
      database: {
        host: process.env.TEST_DB_HOST || 'localhost',
        port: process.env.TEST_DB_PORT || 5432,
        database: process.env.TEST_DB_NAME || 'srsr_test',
        username: process.env.TEST_DB_USER || 'postgres',
        password: process.env.TEST_DB_PASSWORD || 'postgres'
      }
    },
    staging: {
      backend: {
        host: process.env.STAGING_BACKEND_HOST || 'staging.srsr.com',
        port: process.env.STAGING_BACKEND_PORT || 443,
        protocol: process.env.STAGING_BACKEND_PROTOCOL || 'https',
        apiPath: process.env.STAGING_API_PATH || '/api'
      }
    },
    production: {
      backend: {
        host: process.env.PROD_BACKEND_HOST || 'api.srsr.com',
        port: process.env.PROD_BACKEND_PORT || 443,
        protocol: process.env.PROD_BACKEND_PROTOCOL || 'https',
        apiPath: process.env.PROD_API_PATH || '/api'
      }
    }
  },
  
  // Current environment
  currentEnv: process.env.TEST_ENV || 'local',
  
  // Test user credentials
  testUsers: {
    admin: {
      email: process.env.TEST_ADMIN_EMAIL || '<EMAIL>',
      password: process.env.TEST_ADMIN_PASSWORD || 'admin123'
    },
    propertyManager: {
      email: process.env.TEST_MANAGER_EMAIL || '<EMAIL>',
      password: process.env.TEST_MANAGER_PASSWORD || 'admin123'
    },
    maintenanceStaff: {
      email: process.env.TEST_MAINTENANCE_EMAIL || '<EMAIL>',
      password: process.env.TEST_MAINTENANCE_PASSWORD || 'admin123'
    },
    securityGuard: {
      email: process.env.TEST_SECURITY_EMAIL || '<EMAIL>',
      password: process.env.TEST_SECURITY_PASSWORD || 'admin123'
    },
    viewer: {
      email: process.env.TEST_VIEWER_EMAIL || '<EMAIL>',
      password: process.env.TEST_VIEWER_PASSWORD || 'admin123'
    }
  },
  
  // Test timeouts
  timeouts: {
    short: 5000,
    medium: 15000,
    long: 30000,
    veryLong: 60000
  },
  
  // Retry configuration
  retries: {
    api: 3,
    database: 2,
    authentication: 2
  },
  
  // Test data configuration
  testData: {
    userPrefix: 'test.user',
    rolePrefix: 'test.role',
    propertyPrefix: 'test.property',
    cleanupAfterTests: true,
    generateRandomData: true
  },
  
  // API configuration
  api: {
    defaultHeaders: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    timeout: 30000,
    validateStatus: (status) => status < 500 // Don't throw on 4xx errors
  },
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: true,
    enableFile: false,
    logRequests: process.env.LOG_REQUESTS === 'true',
    logResponses: process.env.LOG_RESPONSES === 'true'
  },
  
  // Coverage thresholds
  coverage: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};

// Get current environment configuration
config.current = config.environments[config.currentEnv];

// Build API base URL
if (config.current && config.current.backend) {
  const { protocol, host, port, apiPath } = config.current.backend;
  config.apiBaseUrl = `${protocol}://${host}:${port}${apiPath}`;
}

// Validation
if (!config.apiBaseUrl) {
  throw new Error(`Invalid environment configuration for: ${config.currentEnv}`);
}

module.exports = config;
