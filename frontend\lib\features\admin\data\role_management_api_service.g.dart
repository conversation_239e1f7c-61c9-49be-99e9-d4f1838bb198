// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'role_management_api_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Role _$RoleFromJson(Map<String, dynamic> json) => Role(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isSystemRole: json['is_system_role'] as bool,
      isActive: json['is_active'] as bool? ?? true,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => Permission.fromJson(e as Map<String, dynamic>))
          .toList(),
      users: (json['users'] as List<dynamic>?)
          ?.map((e) => RoleUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      userCount: (json['user_count'] as num?)?.toInt(),
      permissionCount: (json['permission_count'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$RoleToJson(Role instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'is_system_role': instance.isSystemRole,
      'is_active': instance.isActive,
      'permissions': instance.permissions,
      'users': instance.users,
      'user_count': instance.userCount,
      'permission_count': instance.permissionCount,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

Permission _$PermissionFromJson(Map<String, dynamic> json) => Permission(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      resource: json['resource'] as String,
      action: json['action'] as String,
      roles: (json['roles'] as List<dynamic>?)
          ?.map((e) => PermissionRole.fromJson(e as Map<String, dynamic>))
          .toList(),
      roleCount: (json['role_count'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$PermissionToJson(Permission instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'resource': instance.resource,
      'action': instance.action,
      'roles': instance.roles,
      'role_count': instance.roleCount,
      'created_at': instance.createdAt.toIso8601String(),
    };

RoleUser _$RoleUserFromJson(Map<String, dynamic> json) => RoleUser(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      isActive: json['is_active'] as bool?,
      assignedAt: json['assigned_at'] == null
          ? null
          : DateTime.parse(json['assigned_at'] as String),
    );

Map<String, dynamic> _$RoleUserToJson(RoleUser instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'full_name': instance.fullName,
      'is_active': instance.isActive,
      'assigned_at': instance.assignedAt?.toIso8601String(),
    };

PermissionRole _$PermissionRoleFromJson(Map<String, dynamic> json) =>
    PermissionRole(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$PermissionRoleToJson(PermissionRole instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
    };

PermissionResponse _$PermissionResponseFromJson(Map<String, dynamic> json) =>
    PermissionResponse(
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => Permission.fromJson(e as Map<String, dynamic>))
          .toList(),
      groupedPermissions:
          (json['grouped_permissions'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>)
                .map((e) => Permission.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      total: (json['total'] as num).toInt(),
      resources:
          (json['resources'] as List<dynamic>).map((e) => e as String).toList(),
      actions:
          (json['actions'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$PermissionResponseToJson(PermissionResponse instance) =>
    <String, dynamic>{
      'permissions': instance.permissions,
      'grouped_permissions': instance.groupedPermissions,
      'total': instance.total,
      'resources': instance.resources,
      'actions': instance.actions,
    };

UserRolesResponse _$UserRolesResponseFromJson(Map<String, dynamic> json) =>
    UserRolesResponse(
      user: UserInfo.fromJson(json['user'] as Map<String, dynamic>),
      roles: (json['roles'] as List<dynamic>)
          .map((e) => ApiUserRole.fromJson(e as Map<String, dynamic>))
          .toList(),
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => UserPermission.fromJson(e as Map<String, dynamic>))
          .toList(),
      roleCount: (json['role_count'] as num).toInt(),
      permissionCount: (json['permission_count'] as num).toInt(),
    );

Map<String, dynamic> _$UserRolesResponseToJson(UserRolesResponse instance) =>
    <String, dynamic>{
      'user': instance.user,
      'roles': instance.roles,
      'permissions': instance.permissions,
      'role_count': instance.roleCount,
      'permission_count': instance.permissionCount,
    };

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) => UserInfo(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      isActive: json['is_active'] as bool,
    );

Map<String, dynamic> _$UserInfoToJson(UserInfo instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'full_name': instance.fullName,
      'is_active': instance.isActive,
    };

ApiUserRole _$ApiUserRoleFromJson(Map<String, dynamic> json) => ApiUserRole(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isSystemRole: json['is_system_role'] as bool,
      isActive: json['is_active'] as bool? ?? true,
      assignedAt: DateTime.parse(json['assigned_at'] as String),
      assignedBy: json['assigned_by'] as String?,
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => Permission.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ApiUserRoleToJson(ApiUserRole instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'is_system_role': instance.isSystemRole,
      'is_active': instance.isActive,
      'assigned_at': instance.assignedAt.toIso8601String(),
      'assigned_by': instance.assignedBy,
      'permissions': instance.permissions,
    };

UserPermission _$UserPermissionFromJson(Map<String, dynamic> json) =>
    UserPermission(
      id: json['id'] as String,
      name: json['name'] as String,
      resource: json['resource'] as String,
      action: json['action'] as String,
      fromRole: json['from_role'] as String,
    );

Map<String, dynamic> _$UserPermissionToJson(UserPermission instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'resource': instance.resource,
      'action': instance.action,
      'from_role': instance.fromRole,
    };

CreateRoleRequest _$CreateRoleRequestFromJson(Map<String, dynamic> json) =>
    CreateRoleRequest(
      name: json['name'] as String,
      description: json['description'] as String?,
      isSystemRole: json['is_system_role'] as bool?,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CreateRoleRequestToJson(CreateRoleRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'is_system_role': instance.isSystemRole,
      'permissions': instance.permissions,
    };

UpdateRoleRequest _$UpdateRoleRequestFromJson(Map<String, dynamic> json) =>
    UpdateRoleRequest(
      name: json['name'] as String?,
      description: json['description'] as String?,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$UpdateRoleRequestToJson(UpdateRoleRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'permissions': instance.permissions,
    };

CreatePermissionRequest _$CreatePermissionRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePermissionRequest(
      name: json['name'] as String,
      description: json['description'] as String?,
      resource: json['resource'] as String,
      action: json['action'] as String,
    );

Map<String, dynamic> _$CreatePermissionRequestToJson(
        CreatePermissionRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'resource': instance.resource,
      'action': instance.action,
    };

AssignRolesRequest _$AssignRolesRequestFromJson(Map<String, dynamic> json) =>
    AssignRolesRequest(
      roleIds:
          (json['role_ids'] as List<dynamic>).map((e) => e as String).toList(),
      replaceExisting: json['replace_existing'] as bool? ?? true,
    );

Map<String, dynamic> _$AssignRolesRequestToJson(AssignRolesRequest instance) =>
    <String, dynamic>{
      'role_ids': instance.roleIds,
      'replace_existing': instance.replaceExisting,
    };

RemoveRoleRequest _$RemoveRoleRequestFromJson(Map<String, dynamic> json) =>
    RemoveRoleRequest(
      roleId: json['role_id'] as String,
    );

Map<String, dynamic> _$RemoveRoleRequestToJson(RemoveRoleRequest instance) =>
    <String, dynamic>{
      'role_id': instance.roleId,
    };

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations

class _RoleManagementApiService implements RoleManagementApiService {
  _RoleManagementApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<ApiResponse<List<Role>>> getRoles({
    bool? includePermissions,
    bool? includeUsers,
    String? systemRoles,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'include_permissions': includePermissions,
      r'include_users': includeUsers,
      r'system_roles': systemRoles,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<Role>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/roles',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<Role>> _value;
    try {
      _value = ApiResponse<List<Role>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<Role>((i) => Role.fromJson(i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<Role>> createRole(CreateRoleRequest request) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<Role>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/roles',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<Role> _value;
    try {
      _value = ApiResponse<Role>.fromJson(
        _result.data!,
        (json) => Role.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<Role>> getRoleById(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<Role>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/roles/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<Role> _value;
    try {
      _value = ApiResponse<Role>.fromJson(
        _result.data!,
        (json) => Role.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<Role>> updateRole(
    String id,
    UpdateRoleRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<Role>>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/roles/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<Role> _value;
    try {
      _value = ApiResponse<Role>.fromJson(
        _result.data!,
        (json) => Role.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> deleteRole(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/roles/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<PermissionResponse>> getPermissions({
    String? resource,
    String? action,
    bool? includeRoles,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'resource': resource,
      r'action': action,
      r'include_roles': includeRoles,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<PermissionResponse>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/permissions',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<PermissionResponse> _value;
    try {
      _value = ApiResponse<PermissionResponse>.fromJson(
        _result.data!,
        (json) => PermissionResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<Permission>> createPermission(
      CreatePermissionRequest request) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<Permission>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/permissions',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<Permission> _value;
    try {
      _value = ApiResponse<Permission>.fromJson(
        _result.data!,
        (json) => Permission.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> deletePermission(String id) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/permissions/${id}',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<UserRolesResponse>> getUserRoles(String userId) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<UserRolesResponse>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/users/${userId}/roles',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<UserRolesResponse> _value;
    try {
      _value = ApiResponse<UserRolesResponse>.fromJson(
        _result.data!,
        (json) => UserRolesResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<UserRolesResponse>> assignUserRoles(
    String userId,
    AssignRolesRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<UserRolesResponse>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/users/${userId}/roles',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<UserRolesResponse> _value;
    try {
      _value = ApiResponse<UserRolesResponse>.fromJson(
        _result.data!,
        (json) => UserRolesResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<VoidResponse>> removeUserRole(
    String userId,
    RemoveRoleRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<ApiResponse<VoidResponse>>(Options(
      method: 'DELETE',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/users/${userId}/roles',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<VoidResponse> _value;
    try {
      _value = ApiResponse<VoidResponse>.fromJson(
        _result.data!,
        (json) => VoidResponse.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
