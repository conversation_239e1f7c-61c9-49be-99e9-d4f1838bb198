const AuthHelper = require('../../src/helpers/auth_helper');
const { RoleFactory, PermissionFactory } = require('../../src/factories/role_factory');
const ScreenFactory = require('../../src/factories/screen_factory');
const WidgetFactory = require('../../src/factories/widget_factory');
const config = require('../../src/config/test.config');

describe('Admin Permission Configuration API Tests', () => {
  let adminToken;
  let testScreens = [];
  let testWidgets = [];
  let testPermissions = [];

  beforeAll(async () => {
    console.log('🔧 Setting up Admin Permission Configuration API tests...');
    
    try {
      adminToken = await AuthHelper.getAdminToken();
      console.log('✅ Admin authentication successful');
    } catch (error) {
      console.error('❌ Failed to authenticate admin user:', error.message);
      throw error;
    }
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up test configurations...');
    
    // Cleanup screens
    for (const screen of testScreens) {
      try {
        await AuthHelper.makeAuthenticatedRequest(
          adminToken,
          'DELETE',
          `/screens/${screen.id}`
        );
        console.log(`✅ Deleted test screen: ${screen.name}`);
      } catch (error) {
        console.warn(`⚠️ Failed to cleanup screen ${screen.name}:`, error.message);
      }
    }
    
    // Cleanup widgets
    for (const widget of testWidgets) {
      try {
        await AuthHelper.makeAuthenticatedRequest(
          adminToken,
          'DELETE',
          `/widgets/${widget.id}`
        );
        console.log(`✅ Deleted test widget: ${widget.name}`);
      } catch (error) {
        console.warn(`⚠️ Failed to cleanup widget ${widget.name}:`, error.message);
      }
    }
    
    // Cleanup custom permissions
    for (const permission of testPermissions) {
      try {
        await AuthHelper.makeAuthenticatedRequest(
          adminToken,
          'DELETE',
          `/permissions/${permission.id}`
        );
        console.log(`✅ Deleted test permission: ${permission.name}`);
      } catch (error) {
        console.warn(`⚠️ Failed to cleanup permission ${permission.name}:`, error.message);
      }
    }
    
    testScreens = [];
    testWidgets = [];
    testPermissions = [];
    console.log('✅ Cleanup completed');
  });

  describe('Screen Permission Configuration', () => {
    test('should configure custom screen with permissions', async () => {
      const screenData = ScreenFactory.createReportsScreen();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        screenData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.name).toBe(screenData.name);
      expect(response.data.data.title).toBe(screenData.title);
      expect(response.data.data.route).toBe(screenData.route);
      expect(response.data.data.requiredPermissions).toEqual(expect.arrayContaining(screenData.requiredPermissions));
      expect(response.data.data.allowedRoles).toEqual(expect.arrayContaining(screenData.allowedRoles));

      testScreens.push(response.data.data);
    });

    test('should configure analytics screen with role restrictions', async () => {
      const screenData = ScreenFactory.createAnalyticsScreen();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        screenData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.allowedRoles).toEqual(expect.arrayContaining(['admin', 'property_manager']));

      testScreens.push(response.data.data);
    });

    test('should reject screen with invalid data', async () => {
      const invalidScreenData = {
        name: '', // Invalid: empty name
        title: 'Invalid Screen',
        route: '/invalid',
        requiredPermissions: [],
        allowedRoles: []
      };
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        invalidScreenData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/name/i);
    });

    test('should reject screen with duplicate route', async () => {
      const screenData = ScreenFactory.createCustomScreen();
      
      // Create first screen
      const firstResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        screenData
      );
      expect(firstResponse.status).toBe(201);
      testScreens.push(firstResponse.data.data);

      // Try to create screen with same route
      const duplicateScreenData = {
        ...screenData,
        name: 'Different Name',
        title: 'Different Title'
      };

      const duplicateResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        duplicateScreenData
      );

      expect(duplicateResponse.status).toBe(409);
      expect(duplicateResponse.data.success).toBe(false);
      expect(duplicateResponse.data.error).toMatch(/route.*already exists|duplicate/i);
    });

    test('should update screen permissions', async () => {
      // Create screen first
      const screenData = ScreenFactory.createCustomScreen();
      const createResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        screenData
      );
      const screenId = createResponse.data.data.id;
      testScreens.push(createResponse.data.data);

      // Update screen permissions
      const updateData = {
        requiredPermissions: [...screenData.requiredPermissions, 'reports.export'],
        allowedRoles: [...screenData.allowedRoles, 'reporting_specialist']
      };

      const updateResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'PUT',
        `/screens/${screenId}`,
        updateData
      );

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.data.success).toBe(true);
      expect(updateResponse.data.data.requiredPermissions).toEqual(expect.arrayContaining(updateData.requiredPermissions));
      expect(updateResponse.data.data.allowedRoles).toEqual(expect.arrayContaining(updateData.allowedRoles));
    });

    test('should get all configured screens', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/screens'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      expect(response.data.data.length).toBeGreaterThan(0);
    });

    test('should filter screens by role', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/screens?role=admin'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      
      // All returned screens should allow admin role
      response.data.data.forEach(screen => {
        expect(screen.allowedRoles).toContain('admin');
      });
    });
  });

  describe('Widget Permission Configuration', () => {
    test('should configure chart widget with permissions', async () => {
      const widgetData = WidgetFactory.createChartWidget();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/widgets',
        widgetData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.name).toBe(widgetData.name);
      expect(response.data.data.type).toBe(widgetData.type);
      expect(response.data.data.requiredPermissions).toEqual(expect.arrayContaining(widgetData.requiredPermissions));
      expect(response.data.data.allowedRoles).toEqual(expect.arrayContaining(widgetData.allowedRoles));

      testWidgets.push(response.data.data);
    });

    test('should configure table widget with role restrictions', async () => {
      const widgetData = WidgetFactory.createTableWidget();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/widgets',
        widgetData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.allowedRoles).toEqual(expect.arrayContaining(['admin', 'property_manager', 'maintenance_staff']));

      testWidgets.push(response.data.data);
    });

    test('should configure stat card widget', async () => {
      const widgetData = WidgetFactory.createStatCardWidget();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/widgets',
        widgetData
      );

      expect(response.status).toBe(201);
      expect(response.data.data.type).toBe('statCard');

      testWidgets.push(response.data.data);
    });

    test('should reject widget with invalid configuration', async () => {
      const invalidWidgetData = {
        name: '', // Invalid: empty name
        type: 'chart',
        screen: 'dashboard',
        requiredPermissions: [],
        allowedRoles: []
      };
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/widgets',
        invalidWidgetData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/name/i);
    });

    test('should update widget permissions', async () => {
      // Create widget first
      const widgetData = WidgetFactory.createCustomWidget();
      const createResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/widgets',
        widgetData
      );
      const widgetId = createResponse.data.data.id;
      testWidgets.push(createResponse.data.data);

      // Update widget permissions
      const updateData = {
        requiredPermissions: ['maintenance.read', 'maintenance.create'],
        allowedRoles: ['admin', 'maintenance_staff'],
        isVisible: false
      };

      const updateResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'PUT',
        `/widgets/${widgetId}`,
        updateData
      );

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.data.success).toBe(true);
      expect(updateResponse.data.data.requiredPermissions).toEqual(expect.arrayContaining(updateData.requiredPermissions));
      expect(updateResponse.data.data.isVisible).toBe(false);
    });

    test('should get widgets by screen', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/widgets?screen=dashboard'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      
      // All returned widgets should be for dashboard screen
      response.data.data.forEach(widget => {
        expect(widget.screen).toBe('dashboard');
      });
    });
  });

  describe('Permission Management', () => {
    test('should create custom permission', async () => {
      const permissionData = PermissionFactory.createCustomPermission();
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/permissions',
        permissionData
      );

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.name).toBe(permissionData.name);
      expect(response.data.data.description).toBe(permissionData.description);
      expect(response.data.data.category).toBe(permissionData.category);

      testPermissions.push(response.data.data);
    });

    test('should get all permissions', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/permissions'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      expect(response.data.data.length).toBeGreaterThan(0);
      
      // Should include standard permissions
      const permissionNames = response.data.data.map(perm => perm.name);
      expect(permissionNames).toContain('properties.read');
      expect(permissionNames).toContain('maintenance.read');
      expect(permissionNames).toContain('dashboard.view');
    });

    test('should get permissions by category', async () => {
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        '/permissions?category=properties'
      );

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(Array.isArray(response.data.data)).toBe(true);
      
      // All returned permissions should be in properties category
      response.data.data.forEach(permission => {
        expect(permission.category).toBe('properties');
      });
    });

    test('should validate permission references in configurations', async () => {
      const screenData = ScreenFactory.createCustomScreen();
      screenData.requiredPermissions = ['invalid.permission', 'nonexistent.action'];
      
      const response = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        screenData
      );

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/invalid permission|permission not found/i);
    });
  });

  describe('Permission Enforcement', () => {
    test('should enforce screen configuration permissions', async () => {
      // Get token for non-admin user
      const viewerToken = await AuthHelper.getViewerToken();
      const screenData = ScreenFactory.createCustomScreen();

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken,
        'POST',
        '/screens',
        screenData
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
      expect(response.data.error).toMatch(/permission|unauthorized|forbidden/i);
    });

    test('should enforce widget configuration permissions', async () => {
      const viewerToken = await AuthHelper.getViewerToken();
      const widgetData = WidgetFactory.createCustomWidget();

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken,
        'POST',
        '/widgets',
        widgetData
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
    });

    test('should enforce permission creation permissions', async () => {
      const viewerToken = await AuthHelper.getViewerToken();
      const permissionData = PermissionFactory.createCustomPermission();

      const response = await AuthHelper.makeAuthenticatedRequest(
        viewerToken,
        'POST',
        '/permissions',
        permissionData
      );

      expect(response.status).toBe(403);
      expect(response.data.success).toBe(false);
    });
  });

  describe('Configuration Validation', () => {
    test('should validate screen accessibility for user roles', async () => {
      // Create screen with specific role requirements
      const screenData = ScreenFactory.createCustomScreen();
      screenData.allowedRoles = ['admin', 'property_manager'];
      
      const createResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        screenData
      );
      testScreens.push(createResponse.data.data);

      // Check accessibility for property manager
      const propertyManagerToken = await AuthHelper.getPropertyManagerToken();
      const accessResponse = await AuthHelper.makeAuthenticatedRequest(
        propertyManagerToken,
        'GET',
        `/screens/${createResponse.data.data.id}/access-check`
      );

      expect(accessResponse.status).toBe(200);
      expect(accessResponse.data.data.hasAccess).toBe(true);

      // Check accessibility for viewer (should be denied)
      const viewerToken = await AuthHelper.getViewerToken();
      const viewerAccessResponse = await AuthHelper.makeAuthenticatedRequest(
        viewerToken,
        'GET',
        `/screens/${createResponse.data.data.id}/access-check`
      );

      expect(viewerAccessResponse.status).toBe(200);
      expect(viewerAccessResponse.data.data.hasAccess).toBe(false);
    });

    test('should validate widget visibility for user permissions', async () => {
      // Create widget with specific permission requirements
      const widgetData = WidgetFactory.createCustomWidget();
      widgetData.requiredPermissions = ['maintenance.read'];
      widgetData.allowedRoles = ['admin', 'maintenance_staff'];
      
      const createResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/widgets',
        widgetData
      );
      testWidgets.push(createResponse.data.data);

      // Check visibility for maintenance staff
      const maintenanceToken = await AuthHelper.getMaintenanceStaffToken();
      const visibilityResponse = await AuthHelper.makeAuthenticatedRequest(
        maintenanceToken,
        'GET',
        `/widgets/${createResponse.data.data.id}/visibility-check`
      );

      expect(visibilityResponse.status).toBe(200);
      expect(visibilityResponse.data.data.isVisible).toBe(true);

      // Check visibility for viewer (should be hidden)
      const viewerToken = await AuthHelper.getViewerToken();
      const viewerVisibilityResponse = await AuthHelper.makeAuthenticatedRequest(
        viewerToken,
        'GET',
        `/widgets/${createResponse.data.data.id}/visibility-check`
      );

      expect(viewerVisibilityResponse.status).toBe(200);
      expect(viewerVisibilityResponse.data.data.isVisible).toBe(false);
    });
  });

  describe('Configuration Deletion', () => {
    test('should delete screen configuration', async () => {
      // Create screen to delete
      const screenData = ScreenFactory.createCustomScreen();
      const createResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/screens',
        screenData
      );
      const screenId = createResponse.data.data.id;

      // Delete screen
      const deleteResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'DELETE',
        `/screens/${screenId}`
      );

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.data.success).toBe(true);

      // Verify screen is deleted
      const getResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        `/screens/${screenId}`
      );

      expect(getResponse.status).toBe(404);
    });

    test('should delete widget configuration', async () => {
      // Create widget to delete
      const widgetData = WidgetFactory.createCustomWidget();
      const createResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'POST',
        '/widgets',
        widgetData
      );
      const widgetId = createResponse.data.data.id;

      // Delete widget
      const deleteResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'DELETE',
        `/widgets/${widgetId}`
      );

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.data.success).toBe(true);

      // Verify widget is deleted
      const getResponse = await AuthHelper.makeAuthenticatedRequest(
        adminToken,
        'GET',
        `/widgets/${widgetId}`
      );

      expect(getResponse.status).toBe(404);
    });
  });
});
