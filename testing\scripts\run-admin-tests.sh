#!/bin/bash

# SRSR Property Management - Admin Dashboard Testing Script
# This script runs all admin-focused tests with priority on user management

set -e  # Exit on any error

echo "🚀 Starting SRSR Admin Dashboard Test Suite"
echo "=============================================="

# Configuration
BACKEND_URL=${BACKEND_URL:-"http://localhost:3000"}
TEST_ENV=${TEST_ENV:-"local"}
PARALLEL_TESTS=${PARALLEL_TESTS:-false}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if backend is running
check_backend() {
    print_status "Checking backend server at $BACKEND_URL..."
    
    if curl -f "$BACKEND_URL/api" > /dev/null 2>&1; then
        print_success "Backend server is running"
        return 0
    else
        print_warning "Backend server not responding"
        return 1
    fi
}

# Function to start backend if needed
start_backend() {
    print_status "Starting backend server..."
    
    if [ -d "../backend" ]; then
        cd ../backend
        
        # Check if dependencies are installed
        if [ ! -d "node_modules" ]; then
            print_status "Installing backend dependencies..."
            npm install
        fi
        
        # Start backend in background
        print_status "Starting backend server..."
        npm run dev &
        BACKEND_PID=$!
        
        # Wait for backend to start
        print_status "Waiting for backend to start..."
        sleep 10
        
        cd ../testing
        
        # Verify backend is running
        if check_backend; then
            print_success "Backend started successfully (PID: $BACKEND_PID)"
            return 0
        else
            print_error "Failed to start backend"
            return 1
        fi
    else
        print_error "Backend directory not found"
        return 1
    fi
}

# Function to setup test environment
setup_test_environment() {
    print_status "Setting up test environment..."
    
    # Navigate to API tests directory
    cd api_tests
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        print_status "Installing API test dependencies..."
        npm install
    fi
    
    # Setup test data
    print_status "Setting up test data..."
    if [ -f "scripts/setup-test-environment.js" ]; then
        node scripts/setup-test-environment.js
    else
        print_warning "Test environment setup script not found"
    fi
    
    cd ..
}

# Function to run Flutter integration tests
run_flutter_tests() {
    print_status "Running Flutter integration tests..."
    
    cd flutter_tests
    
    # Check if dependencies are installed
    if [ ! -d ".dart_tool" ]; then
        print_status "Installing Flutter dependencies..."
        flutter pub get
    fi
    
    # Run Flutter analysis
    print_status "Running Flutter analysis..."
    flutter analyze
    
    # Run admin integration tests
    print_status "Running admin integration tests..."
    flutter test integration_test/admin/ --reporter=json > ../reports/flutter-admin-results.json
    FLUTTER_EXIT_CODE=$?
    
    if [ $FLUTTER_EXIT_CODE -eq 0 ]; then
        print_success "Flutter admin tests passed"
    else
        print_error "Flutter admin tests failed"
    fi
    
    cd ..
    return $FLUTTER_EXIT_CODE
}

# Function to run API tests
run_api_tests() {
    print_status "Running API tests..."
    
    cd api_tests
    
    local api_exit_code=0
    
    if [ "$PARALLEL_TESTS" = "true" ]; then
        print_status "Running API tests in parallel..."
        npm run test:admin -- --maxWorkers=4
        api_exit_code=$?
    else
        print_status "Running API tests sequentially..."
        
        # Run admin tests in order of priority
        print_status "1. Running User Management API tests..."
        npm run test:user-management
        local user_mgmt_exit=$?
        
        print_status "2. Running Role Management API tests..."
        npm run test:role-management
        local role_mgmt_exit=$?
        
        print_status "3. Running Permission Configuration API tests..."
        npm run test:permission-config
        local perm_config_exit=$?
        
        # Calculate overall exit code
        if [ $user_mgmt_exit -ne 0 ] || [ $role_mgmt_exit -ne 0 ] || [ $perm_config_exit -ne 0 ]; then
            api_exit_code=1
        fi
    fi
    
    if [ $api_exit_code -eq 0 ]; then
        print_success "API admin tests passed"
    else
        print_error "API admin tests failed"
    fi
    
    cd ..
    return $api_exit_code
}

# Function to generate test reports
generate_reports() {
    print_status "Generating test reports..."
    
    # Create reports directory if it doesn't exist
    mkdir -p reports/combined
    
    # Generate API test reports
    cd api_tests
    npm run report
    cd ..
    
    # Copy reports to combined directory
    if [ -d "api_tests/reports" ]; then
        cp -r api_tests/reports/* reports/
    fi
    
    if [ -d "flutter_tests/coverage" ]; then
        cp -r flutter_tests/coverage reports/flutter-coverage
    fi
    
    print_success "Test reports generated in reports/ directory"
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up..."
    
    # Kill backend if we started it
    if [ ! -z "$BACKEND_PID" ]; then
        print_status "Stopping backend server (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
        wait $BACKEND_PID 2>/dev/null || true
        print_success "Backend server stopped"
    fi
    
    # Cleanup test data
    cd api_tests
    if [ -f "scripts/cleanup-test-data.js" ]; then
        print_status "Cleaning up test data..."
        node scripts/cleanup-test-data.js
    fi
    cd ..
}

# Main execution
main() {
    local overall_exit_code=0
    
    print_status "Environment: $TEST_ENV"
    print_status "Backend URL: $BACKEND_URL"
    print_status "Parallel Tests: $PARALLEL_TESTS"
    echo ""
    
    # Check if backend is running, start if needed
    if ! check_backend; then
        if ! start_backend; then
            print_error "Cannot proceed without backend server"
            exit 1
        fi
    fi
    
    # Setup test environment
    setup_test_environment
    
    # Run tests
    print_status "Starting admin dashboard tests..."
    echo ""
    
    # Run API tests first (they're faster and catch basic issues)
    run_api_tests
    local api_result=$?
    
    # Run Flutter integration tests
    run_flutter_tests
    local flutter_result=$?
    
    # Generate reports
    generate_reports
    
    # Calculate overall result
    if [ $api_result -ne 0 ] || [ $flutter_result -ne 0 ]; then
        overall_exit_code=1
    fi
    
    # Print summary
    echo ""
    echo "=============================================="
    echo "🧪 ADMIN DASHBOARD TEST SUMMARY"
    echo "=============================================="
    
    if [ $api_result -eq 0 ]; then
        print_success "API Tests: PASSED"
    else
        print_error "API Tests: FAILED"
    fi
    
    if [ $flutter_result -eq 0 ]; then
        print_success "Flutter Tests: PASSED"
    else
        print_error "Flutter Tests: FAILED"
    fi
    
    echo ""
    
    if [ $overall_exit_code -eq 0 ]; then
        print_success "🎉 All admin dashboard tests PASSED!"
    else
        print_error "❌ Some admin dashboard tests FAILED"
        print_status "Check reports/ directory for detailed results"
    fi
    
    echo "=============================================="
    
    return $overall_exit_code
}

# Trap to ensure cleanup runs
trap cleanup EXIT

# Run main function
main "$@"
exit $?
