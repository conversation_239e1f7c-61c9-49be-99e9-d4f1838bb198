# 🧪 Dedicated Testing Project Setup Guide

## 📋 Overview

This guide shows how to create a separate `/testing` project parallel to `/frontend` and `/backend` for comprehensive test automation without disturbing the original projects.

## 🏗️ Project Structure

```
SrsrMan/
├── frontend/           # Flutter mobile app
├── backend/            # NextJS API server
├── testing/            # 🆕 Dedicated testing project
│   ├── flutter_tests/  # Flutter integration tests
│   ├── api_tests/      # API automation tests
│   ├── e2e_tests/      # End-to-end scenarios
│   ├── performance/    # Performance testing
│   ├── security/       # Security testing
│   ├── reports/        # Test reports and artifacts
│   ├── scripts/        # Automation scripts
│   ├── config/         # Test configurations
│   └── docs/           # Testing documentation
```

---

## 🚀 Testing Project Setup

### **1. Create Testing Project Structure**

```bash
# Navigate to project root (same level as frontend/backend)
cd SrsrMan

# Create testing project structure
mkdir testing
cd testing

# Create main directories
mkdir -p flutter_tests/{integration,widget,unit,page_objects,helpers,factories}
mkdir -p api_tests/{admin,auth,properties,maintenance,attendance,fuel}
mkdir -p e2e_tests/{user_workflows,role_scenarios,permission_tests}
mkdir -p performance/{load_tests,stress_tests,benchmarks}
mkdir -p security/{auth_tests,permission_tests,vulnerability_tests}
mkdir -p reports/{html,json,coverage,screenshots}
mkdir -p scripts/{setup,cleanup,data_management}
mkdir -p config/{environments,test_data,credentials}
mkdir -p docs/{guides,specifications,results}
```

### **2. Initialize Flutter Testing Project**

```bash
# Create Flutter test project
cd flutter_tests
flutter create . --template=app --project-name=srsr_testing
```

### **3. Initialize Node.js API Testing Project**

```bash
# Create API testing project
cd ../api_tests
npm init -y
```

---

## 📱 Flutter Testing Project Configuration

### **Flutter Test Project Structure:**
```
testing/flutter_tests/
├── lib/
│   ├── app/                    # Test app wrapper
│   ├── helpers/                # Test helper utilities
│   ├── page_objects/           # Page object models
│   ├── factories/              # Test data factories
│   └── config/                 # Test configurations
├── test/
│   ├── unit/                   # Unit tests
│   ├── widget/                 # Widget tests
│   └── helpers/                # Test utilities
├── integration_test/
│   ├── admin/                  # Admin workflow tests
│   ├── roles/                  # Role-based tests
│   ├── permissions/            # Permission tests
│   ├── screens/                # Screen navigation tests
│   └── workflows/              # End-to-end workflows
├── test_driver/
│   ├── app.dart               # Test driver app
│   └── integration_test.dart  # Driver configuration
└── pubspec.yaml               # Dependencies
```

### **pubspec.yaml for Testing Project:**
```yaml
name: srsr_testing
description: Automated testing suite for SRSR Property Management
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # HTTP & API Testing
  dio: ^5.4.3+1
  http: ^1.1.0

  # State Management (for test app)
  flutter_riverpod: ^2.5.1

  # Utilities
  intl: ^0.20.2
  uuid: ^4.4.0
  logger: ^2.3.0

  # Test Data Generation
  faker: ^2.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Integration Testing
  integration_test:
    sdk: flutter

  # Driver Testing
  flutter_driver:
    sdk: flutter

  # API Testing
  test: ^1.24.0
  mockito: ^5.4.4

  # Test Utilities
  golden_toolkit: ^0.15.0
  patrol: ^3.0.0

  # Code Generation
  build_runner: ^2.4.9
  json_serializable: ^6.8.0

  # Linting
  flutter_lints: ^6.0.0

flutter:
  uses-material-design: true
```

---

## 🌐 API Testing Project Configuration

### **API Testing Structure:**
```
testing/api_tests/
├── src/
│   ├── helpers/                # API helper utilities
│   ├── fixtures/               # Test data fixtures
│   ├── config/                 # Test configurations
│   └── utils/                  # Utility functions
├── tests/
│   ├── admin/                  # Admin API tests
│   │   ├── user_management.test.js
│   │   ├── role_management.test.js
│   │   └── permission_config.test.js
│   ├── auth/                   # Authentication tests
│   │   ├── login.test.js
│   │   ├── registration.test.js
│   │   └── token_validation.test.js
│   ├── properties/             # Property API tests
│   ├── maintenance/            # Maintenance API tests
│   ├── attendance/             # Attendance API tests
│   └── fuel/                   # Fuel monitoring tests
├── reports/                    # Test reports
├── package.json
└── jest.config.js
```

### **package.json for API Testing:**
```json
{
  "name": "srsr-api-testing",
  "version": "1.0.0",
  "description": "API automation testing for SRSR Property Management",
  "main": "index.js",
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:admin": "jest tests/admin",
    "test:auth": "jest tests/auth",
    "test:properties": "jest tests/properties",
    "test:maintenance": "jest tests/maintenance",
    "test:attendance": "jest tests/attendance",
    "test:fuel": "jest tests/fuel",
    "test:all": "jest --runInBand",
    "report": "jest --coverage --coverageReporters=html",
    "setup": "node scripts/setup-test-environment.js",
    "cleanup": "node scripts/cleanup-test-data.js"
  },
  "dependencies": {
    "axios": "^1.6.0",
    "dotenv": "^16.3.1",
    "faker": "^6.6.6",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "jest": "^29.7.0",
    "supertest": "^6.3.3",
    "@types/jest": "^29.5.8",
    "jest-html-reporter": "^3.10.2",
    "jest-junit": "^16.0.0"
  }
}
```

---

## 🔧 Configuration Files

### **Test Environment Configuration:**
```javascript
// testing/config/test.config.js
module.exports = {
  environments: {
    local: {
      frontend: {
        host: 'localhost',
        port: 8080,
        protocol: 'http'
      },
      backend: {
        host: 'localhost',
        port: 3000,
        protocol: 'http',
        apiPath: '/api'
      },
      database: {
        host: 'localhost',
        port: 5432,
        database: 'srsr_test',
        username: 'postgres',
        password: 'postgres'
      }
    },
    staging: {
      frontend: {
        host: 'staging.srsr.com',
        port: 443,
        protocol: 'https'
      },
      backend: {
        host: 'api-staging.srsr.com',
        port: 443,
        protocol: 'https',
        apiPath: '/api'
      }
    },
    production: {
      frontend: {
        host: 'app.srsr.com',
        port: 443,
        protocol: 'https'
      },
      backend: {
        host: 'api.srsr.com',
        port: 443,
        protocol: 'https',
        apiPath: '/api'
      }
    }
  },

  testData: {
    users: {
      admin: {
        email: '<EMAIL>',
        password: 'TestAdmin123!'
      },
      manager: {
        email: '<EMAIL>',
        password: 'TestManager123!'
      },
      maintenance: {
        email: '<EMAIL>',
        password: 'TestMaintenance123!'
      }
    }
  },

  timeouts: {
    short: 5000,
    medium: 15000,
    long: 30000,
    veryLong: 60000
  },

  retries: {
    api: 3,
    ui: 2,
    integration: 1
  }
};
```

### **Jest Configuration:**
```javascript
// testing/api_tests/jest.config.js
module.exports = {
  testEnvironment: 'node',
  roots: ['<rootDir>/tests'],
  testMatch: ['**/*.test.js'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.config.js'
  ],
  coverageDirectory: 'reports/coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/src/config/jest.setup.js'],
  reporters: [
    'default',
    ['jest-html-reporter', {
      pageTitle: 'SRSR API Test Report',
      outputPath: 'reports/html/api-test-report.html',
      includeFailureMsg: true,
      includeSuiteFailure: true
    }],
    ['jest-junit', {
      outputDirectory: 'reports/junit',
      outputName: 'api-test-results.xml'
    }]
  ],
  testTimeout: 30000,
  maxWorkers: 4
};
```

---

## 📋 Automation Scripts

### **Master Test Runner Script:**
```bash
#!/bin/bash
# testing/scripts/run-all-tests.sh

echo "🚀 Starting SRSR Property Management Test Suite"
echo "================================================"

# Set environment
ENVIRONMENT=${1:-local}
echo "Environment: $ENVIRONMENT"

# Check if backend is running
echo "🔍 Checking backend server..."
if ! curl -f http://localhost:3000/api > /dev/null 2>&1; then
    echo "❌ Backend server not running. Starting backend..."
    cd ../backend
    npm run dev &
    BACKEND_PID=$!
    sleep 10
    cd ../testing
else
    echo "✅ Backend server is running"
fi

# Run API tests
echo "🌐 Running API tests..."
cd api_tests
npm test
API_EXIT_CODE=$?

# Run Flutter integration tests
echo "📱 Running Flutter integration tests..."
cd ../flutter_tests
flutter test integration_test/
FLUTTER_EXIT_CODE=$?

# Run E2E tests
echo "🔄 Running E2E workflow tests..."
flutter drive --target=test_driver/app.dart
E2E_EXIT_CODE=$?

# Generate combined report
echo "📊 Generating test reports..."
cd ../scripts
node generate-combined-report.js

# Cleanup
if [ ! -z "$BACKEND_PID" ]; then
    echo "🧹 Stopping backend server..."
    kill $BACKEND_PID
fi

# Exit with appropriate code
if [ $API_EXIT_CODE -eq 0 ] && [ $FLUTTER_EXIT_CODE -eq 0 ] && [ $E2E_EXIT_CODE -eq 0 ]; then
    echo "🎉 All tests passed!"
    exit 0
else
    echo "❌ Some tests failed"
    exit 1
fi
```

### **Test Data Setup Script:**
```javascript
// testing/scripts/setup-test-data.js
const axios = require('axios');
const config = require('../config/test.config.js');

async function setupTestData() {
  console.log('🔧 Setting up test data...');

  const baseURL = `${config.environments.local.backend.protocol}://${config.environments.local.backend.host}:${config.environments.local.backend.port}${config.environments.local.backend.apiPath}`;

  try {
    // Login as admin
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    const token = loginResponse.data.token;
    const headers = { Authorization: `Bearer ${token}` };

    // Create test users
    const testUsers = [
      {
        fullName: 'Test Property Manager',
        email: '<EMAIL>',
        password: 'TestManager123!',
        roles: ['property_manager']
      },
      {
        fullName: 'Test Maintenance Staff',
        email: '<EMAIL>',
        password: 'TestMaintenance123!',
        roles: ['maintenance_staff']
      },
      {
        fullName: 'Test Viewer',
        email: '<EMAIL>',
        password: 'TestViewer123!',
        roles: ['viewer']
      }
    ];

    for (const user of testUsers) {
      try {
        await axios.post(`${baseURL}/users`, user, { headers });
        console.log(`✅ Created test user: ${user.email}`);
      } catch (error) {
        if (error.response?.status === 409) {
          console.log(`ℹ️ Test user already exists: ${user.email}`);
        } else {
          console.error(`❌ Failed to create user ${user.email}:`, error.message);
        }
      }
    }

    // Create test properties
    const testProperties = [
      {
        name: 'Test Residential Property',
        type: 'residential',
        address: '123 Test Street',
        isActive: true
      },
      {
        name: 'Test Office Property',
        type: 'office',
        address: '456 Business Ave',
        isActive: true
      }
    ];

    for (const property of testProperties) {
      try {
        await axios.post(`${baseURL}/properties`, property, { headers });
        console.log(`✅ Created test property: ${property.name}`);
      } catch (error) {
        if (error.response?.status === 409) {
          console.log(`ℹ️ Test property already exists: ${property.name}`);
        } else {
          console.error(`❌ Failed to create property ${property.name}:`, error.message);
        }
      }
    }

    console.log('🎉 Test data setup completed!');

  } catch (error) {
    console.error('❌ Test data setup failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  setupTestData();
}

module.exports = { setupTestData };
```

---

## 🎯 Benefits of Separate Testing Project

### **✅ Advantages:**

1. **Isolation** - No interference with production code
2. **Independence** - Can test multiple versions of frontend/backend
3. **Scalability** - Easy to add new test types and frameworks
4. **Maintainability** - Dedicated team can manage testing code
5. **Flexibility** - Different testing tools and approaches
6. **CI/CD Integration** - Separate pipeline for testing
7. **Documentation** - Centralized testing documentation
8. **Reporting** - Unified test reporting across all layers

### **🔧 Project Integration:**

1. **Shared Configuration** - Common test data and environments
2. **Cross-Project Testing** - Test frontend + backend integration
3. **Unified Reporting** - Combined test results and coverage
4. **Automated Deployment** - Test different versions automatically
5. **Performance Monitoring** - Track performance across releases

---

## 🚀 Getting Started

### **Quick Setup Commands:**
```bash
# Clone or navigate to project root
cd SrsrMan

# Create testing project
mkdir testing && cd testing

# Setup Flutter tests
git clone <this-guide-repo> .
cd flutter_tests && flutter pub get

# Setup API tests
cd ../api_tests && npm install

# Setup test data
npm run setup

# Run all tests
../scripts/run-all-tests.sh
```

---

## 📁 Sample Implementation Files

### **Flutter Integration Test Example:**
```dart
// testing/flutter_tests/integration_test/admin/user_management_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import '../helpers/test_app.dart';
import '../helpers/auth_helper.dart';
import '../helpers/navigation_helper.dart';
import '../page_objects/user_management_page.dart';
import '../factories/user_data_factory.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('User Management Integration Tests', () {
    late UserManagementPage userManagementPage;

    setUpAll(() async {
      // Setup test environment
      await TestEnvironment.initialize();
    });

    setUp(() async {
      // Start fresh test app
      await TestApp.start();
    });

    tearDown(() async {
      // Cleanup after each test
      await TestApp.cleanup();
    });

    testWidgets('Admin can create new user with roles', (tester) async {
      userManagementPage = UserManagementPage(tester);

      // Step 1: Login as admin
      await AuthHelper.loginAsAdmin(tester);

      // Step 2: Navigate to user management
      await NavigationHelper.navigateToUserManagement(tester);

      // Step 3: Create test user data
      final userData = UserDataFactory.createPropertyManager();

      // Step 4: Create user through UI
      await userManagementPage.createUser(userData);

      // Step 5: Verify user creation
      await userManagementPage.verifyUserExists(userData.email);

      // Step 6: Test new user login
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithCredentials(tester, userData.email, userData.password);

      // Step 7: Verify role permissions
      await NavigationHelper.verifyAccessibleScreens(tester, userData.expectedScreens);

      // Step 8: Cleanup
      await AuthHelper.loginAsAdmin(tester);
      await userManagementPage.deleteUser(userData.email);
    });

    testWidgets('Role permissions are enforced correctly', (tester) async {
      // Test different role scenarios
      final roleScenarios = [
        RoleScenario.propertyManager(),
        RoleScenario.maintenanceStaff(),
        RoleScenario.viewer(),
      ];

      for (final scenario in roleScenarios) {
        await _testRoleScenario(tester, scenario);
      }
    });
  });
}

Future<void> _testRoleScenario(WidgetTester tester, RoleScenario scenario) async {
  // Login with specific role
  await AuthHelper.loginWithRole(tester, scenario.role);

  // Test accessible screens
  for (final screen in scenario.accessibleScreens) {
    await NavigationHelper.navigateToScreen(tester, screen);
    expect(find.byKey(Key('${screen}_screen')), findsOneWidget);
  }

  // Test inaccessible screens
  for (final screen in scenario.inaccessibleScreens) {
    expect(find.byKey(Key('${screen}_menu_item')), findsNothing);
  }

  await AuthHelper.logout(tester);
}
```

### **API Test Example:**
```javascript
// testing/api_tests/tests/admin/user_management.test.js
const axios = require('axios');
const { TestEnvironment } = require('../../src/helpers/test_environment');
const { UserFactory } = require('../../src/factories/user_factory');
const { AuthHelper } = require('../../src/helpers/auth_helper');

describe('User Management API Tests', () => {
  let authToken;
  let testUsers = [];

  beforeAll(async () => {
    await TestEnvironment.setup();
    authToken = await AuthHelper.getAdminToken();
  });

  afterAll(async () => {
    // Cleanup test users
    for (const user of testUsers) {
      try {
        await axios.delete(`${TestEnvironment.apiUrl}/users/${user.id}`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
      } catch (error) {
        console.warn(`Failed to cleanup user ${user.email}:`, error.message);
      }
    }
    await TestEnvironment.cleanup();
  });

  describe('User Creation', () => {
    test('should create user with valid data', async () => {
      const userData = UserFactory.createPropertyManager();

      const response = await axios.post(`${TestEnvironment.apiUrl}/users`, userData, {
        headers: { Authorization: `Bearer ${authToken}` }
      });

      expect(response.status).toBe(201);
      expect(response.data.success).toBe(true);
      expect(response.data.data.email).toBe(userData.email);
      expect(response.data.data.roles).toEqual(expect.arrayContaining(userData.roles));

      testUsers.push(response.data.data);
    });

    test('should reject user with invalid email', async () => {
      const userData = UserFactory.createWithInvalidEmail();

      try {
        await axios.post(`${TestEnvironment.apiUrl}/users`, userData, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        fail('Should have thrown validation error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toContain('email');
      }
    });

    test('should reject duplicate email', async () => {
      const userData = UserFactory.createPropertyManager();

      // Create first user
      const firstResponse = await axios.post(`${TestEnvironment.apiUrl}/users`, userData, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      testUsers.push(firstResponse.data.data);

      // Try to create duplicate
      try {
        await axios.post(`${TestEnvironment.apiUrl}/users`, userData, {
          headers: { Authorization: `Bearer ${authToken}` }
        });
        fail('Should have thrown duplicate error');
      } catch (error) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.error).toContain('already exists');
      }
    });
  });

  describe('Role Assignment', () => {
    test('should assign multiple roles to user', async () => {
      const userData = UserFactory.createBasicUser();

      // Create user
      const createResponse = await axios.post(`${TestEnvironment.apiUrl}/users`, userData, {
        headers: { Authorization: `Bearer ${authToken}` }
      });
      const userId = createResponse.data.data.id;
      testUsers.push(createResponse.data.data);

      // Assign roles
      const roleData = {
        roleIds: ['property_manager_role_id', 'maintenance_staff_role_id'],
        replaceExisting: true
      };

      const assignResponse = await axios.post(`${TestEnvironment.apiUrl}/users/${userId}/roles`, roleData, {
        headers: { Authorization: `Bearer ${authToken}` }
      });

      expect(assignResponse.status).toBe(200);
      expect(assignResponse.data.success).toBe(true);

      // Verify role assignment
      const userResponse = await axios.get(`${TestEnvironment.apiUrl}/users/${userId}`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });

      expect(userResponse.data.data.roles).toHaveLength(2);
      expect(userResponse.data.data.roles).toEqual(expect.arrayContaining(['property_manager', 'maintenance_staff']));
    });
  });

  describe('Permission Enforcement', () => {
    test('should enforce user creation permissions', async () => {
      // Get token for non-admin user
      const viewerToken = await AuthHelper.getViewerToken();
      const userData = UserFactory.createBasicUser();

      try {
        await axios.post(`${TestEnvironment.apiUrl}/users`, userData, {
          headers: { Authorization: `Bearer ${viewerToken}` }
        });
        fail('Should have thrown permission error');
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.error).toContain('permission');
      }
    });
  });
});
```

### **Page Object Model Example:**
```dart
// testing/flutter_tests/lib/page_objects/user_management_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../models/user_data.dart';

class UserManagementPage {
  final WidgetTester tester;

  UserManagementPage(this.tester);

  // Locators
  Finder get addUserFab => find.byKey(const Key('add_user_fab'));
  Finder get usersList => find.byKey(const Key('users_list'));
  Finder get searchField => find.byKey(const Key('search_field'));

  // Form fields
  Finder get fullNameField => find.byKey(const Key('full_name_field'));
  Finder get emailField => find.byKey(const Key('email_field'));
  Finder get passwordField => find.byKey(const Key('password_field'));
  Finder get confirmPasswordField => find.byKey(const Key('confirm_password_field'));
  Finder get createUserButton => find.text('Create User');

  // Actions
  Future<void> createUser(UserData userData) async {
    // Open create user dialog
    await tester.tap(addUserFab);
    await tester.pumpAndSettle();

    // Fill form
    await _fillUserForm(userData);

    // Submit form
    await tester.tap(createUserButton);
    await tester.pumpAndSettle();

    // Wait for success message
    await tester.pump(const Duration(seconds: 2));
  }

  Future<void> _fillUserForm(UserData userData) async {
    await tester.enterText(fullNameField, userData.fullName);
    await tester.enterText(emailField, userData.email);
    await tester.enterText(passwordField, userData.password);
    await tester.enterText(confirmPasswordField, userData.password);

    // Select roles
    for (final role in userData.roles) {
      final roleCheckbox = find.byKey(Key('role_checkbox_$role'));
      await tester.tap(roleCheckbox);
    }

    await tester.pumpAndSettle();
  }

  Future<void> verifyUserExists(String email) async {
    // Search for user
    await tester.enterText(searchField, email);
    await tester.pumpAndSettle();

    // Verify user appears in list
    expect(find.text(email), findsOneWidget);
  }

  Future<void> deleteUser(String email) async {
    // Find user card
    final userCard = find.ancestor(
      of: find.text(email),
      matching: find.byType(Card),
    );

    // Find and tap delete button
    final deleteButton = find.descendant(
      of: userCard,
      matching: find.byIcon(Icons.delete),
    );

    await tester.tap(deleteButton);
    await tester.pumpAndSettle();

    // Confirm deletion
    await tester.tap(find.text('Delete'));
    await tester.pumpAndSettle();

    // Verify user is removed
    expect(find.text(email), findsNothing);
  }

  Future<void> editUser(String email, UserData newData) async {
    // Find user card
    final userCard = find.ancestor(
      of: find.text(email),
      matching: find.byType(Card),
    );

    // Find and tap edit button
    final editButton = find.descendant(
      of: userCard,
      matching: find.byIcon(Icons.edit),
    );

    await tester.tap(editButton);
    await tester.pumpAndSettle();

    // Update form
    await _fillUserForm(newData);

    // Submit changes
    await tester.tap(find.text('Save Changes'));
    await tester.pumpAndSettle();
  }
}
```

### **Test Data Factory Example:**
```dart
// testing/flutter_tests/lib/factories/user_data_factory.dart
import '../models/user_data.dart';

class UserDataFactory {
  static UserData createPropertyManager() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Test Property Manager $timestamp',
      email: 'test.manager.$<EMAIL>',
      password: 'TestManager123!',
      roles: ['property_manager'],
      expectedScreens: ['dashboard', 'properties', 'maintenance', 'attendance'],
      restrictedScreens: ['admin', 'user_management'],
    );
  }

  static UserData createMaintenanceStaff() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Test Maintenance Staff $timestamp',
      email: 'test.maintenance.$<EMAIL>',
      password: 'TestMaintenance123!',
      roles: ['maintenance_staff'],
      expectedScreens: ['dashboard', 'maintenance'],
      restrictedScreens: ['admin', 'user_management', 'properties'],
    );
  }

  static UserData createViewer() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Test Viewer $timestamp',
      email: 'test.viewer.$<EMAIL>',
      password: 'TestViewer123!',
      roles: ['viewer'],
      expectedScreens: ['dashboard'],
      restrictedScreens: ['admin', 'user_management', 'properties', 'maintenance'],
    );
  }

  static UserData createWithInvalidEmail() {
    return UserData(
      fullName: 'Invalid Email User',
      email: 'invalid-email-format',
      password: 'ValidPassword123!',
      roles: ['viewer'],
      expectedScreens: [],
      restrictedScreens: [],
    );
  }

  static UserData createCustomRole(List<String> roles, List<String> expectedScreens) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return UserData(
      fullName: 'Custom Role User $timestamp',
      email: 'custom.role.$<EMAIL>',
      password: 'CustomRole123!',
      roles: roles,
      expectedScreens: expectedScreens,
      restrictedScreens: [],
    );
  }
}
```

---

## 🚀 Quick Start Commands

### **1. Create Testing Project:**
```bash
# Navigate to project root (same level as frontend/backend)
cd SrsrMan

# Create testing directory structure
mkdir -p testing/{flutter_tests,api_tests,e2e_tests,performance,security,reports,scripts,config,docs}

# Initialize Flutter testing project
cd testing/flutter_tests
flutter create . --template=app --project-name=srsr_testing

# Initialize API testing project
cd ../api_tests
npm init -y
npm install jest axios supertest faker dotenv uuid
npm install --save-dev @types/jest jest-html-reporter jest-junit

# Copy configuration files (from this guide)
# ... copy pubspec.yaml, jest.config.js, etc.
```

### **2. Setup Test Environment:**
```bash
# Install dependencies
cd testing/flutter_tests && flutter pub get
cd ../api_tests && npm install

# Setup test data
node ../scripts/setup-test-data.js

# Verify setup
npm test -- --testNamePattern="health check"
```

### **3. Run Tests:**
```bash
# Run all tests
./scripts/run-all-tests.sh

# Run specific test suites
cd api_tests && npm run test:admin
cd flutter_tests && flutter test integration_test/admin/

# Run with coverage
cd api_tests && npm run test:coverage
cd flutter_tests && flutter test --coverage
```

---

## 📊 CI/CD Integration

### **GitHub Actions Workflow:**
```yaml
# .github/workflows/testing-pipeline.yml
name: Dedicated Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM

jobs:
  api-tests:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: testing/api_tests

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Install dependencies
      run: npm install

    - name: Start backend
      run: |
        cd ../../backend
        npm install
        npm run dev &
        sleep 10

    - name: Run API tests
      run: npm test

    - name: Upload API test results
      uses: actions/upload-artifact@v3
      with:
        name: api-test-results
        path: testing/api_tests/reports/

  flutter-tests:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: testing/flutter_tests

    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'

    - name: Install dependencies
      run: flutter pub get

    - name: Run integration tests
      run: flutter test integration_test/

    - name: Upload Flutter test results
      uses: actions/upload-artifact@v3
      with:
        name: flutter-test-results
        path: testing/flutter_tests/test/reports/

  generate-report:
    needs: [api-tests, flutter-tests]
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Download test results
      uses: actions/download-artifact@v3

    - name: Generate combined report
      run: |
        cd testing/scripts
        node generate-combined-report.js

    - name: Upload combined report
      uses: actions/upload-artifact@v3
      with:
        name: combined-test-report
        path: testing/reports/combined/
```

---

*This dedicated testing project provides complete isolation from your production code while maintaining full integration testing capabilities. It's scalable, maintainable, and follows industry best practices for enterprise test automation.*
