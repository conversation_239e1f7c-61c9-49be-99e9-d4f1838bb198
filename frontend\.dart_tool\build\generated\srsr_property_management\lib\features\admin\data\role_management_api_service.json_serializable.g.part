// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Role _$RoleFromJson(Map<String, dynamic> json) => Role(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isSystemRole: json['is_system_role'] as bool,
      isActive: json['is_active'] as bool? ?? true,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => Permission.fromJson(e as Map<String, dynamic>))
          .toList(),
      users: (json['users'] as List<dynamic>?)
          ?.map((e) => RoleUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      userCount: (json['user_count'] as num?)?.toInt(),
      permissionCount: (json['permission_count'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$RoleToJson(Role instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'is_system_role': instance.isSystemRole,
      'is_active': instance.isActive,
      'permissions': instance.permissions,
      'users': instance.users,
      'user_count': instance.userCount,
      'permission_count': instance.permissionCount,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

Permission _$PermissionFromJson(Map<String, dynamic> json) => Permission(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      resource: json['resource'] as String,
      action: json['action'] as String,
      roles: (json['roles'] as List<dynamic>?)
          ?.map((e) => PermissionRole.fromJson(e as Map<String, dynamic>))
          .toList(),
      roleCount: (json['role_count'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$PermissionToJson(Permission instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'resource': instance.resource,
      'action': instance.action,
      'roles': instance.roles,
      'role_count': instance.roleCount,
      'created_at': instance.createdAt.toIso8601String(),
    };

RoleUser _$RoleUserFromJson(Map<String, dynamic> json) => RoleUser(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      isActive: json['is_active'] as bool?,
      assignedAt: json['assigned_at'] == null
          ? null
          : DateTime.parse(json['assigned_at'] as String),
    );

Map<String, dynamic> _$RoleUserToJson(RoleUser instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'full_name': instance.fullName,
      'is_active': instance.isActive,
      'assigned_at': instance.assignedAt?.toIso8601String(),
    };

PermissionRole _$PermissionRoleFromJson(Map<String, dynamic> json) =>
    PermissionRole(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$PermissionRoleToJson(PermissionRole instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
    };

PermissionResponse _$PermissionResponseFromJson(Map<String, dynamic> json) =>
    PermissionResponse(
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => Permission.fromJson(e as Map<String, dynamic>))
          .toList(),
      groupedPermissions:
          (json['grouped_permissions'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>)
                .map((e) => Permission.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      total: (json['total'] as num).toInt(),
      resources:
          (json['resources'] as List<dynamic>).map((e) => e as String).toList(),
      actions:
          (json['actions'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$PermissionResponseToJson(PermissionResponse instance) =>
    <String, dynamic>{
      'permissions': instance.permissions,
      'grouped_permissions': instance.groupedPermissions,
      'total': instance.total,
      'resources': instance.resources,
      'actions': instance.actions,
    };

UserRolesResponse _$UserRolesResponseFromJson(Map<String, dynamic> json) =>
    UserRolesResponse(
      user: UserInfo.fromJson(json['user'] as Map<String, dynamic>),
      roles: (json['roles'] as List<dynamic>)
          .map((e) => ApiUserRole.fromJson(e as Map<String, dynamic>))
          .toList(),
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => UserPermission.fromJson(e as Map<String, dynamic>))
          .toList(),
      roleCount: (json['role_count'] as num).toInt(),
      permissionCount: (json['permission_count'] as num).toInt(),
    );

Map<String, dynamic> _$UserRolesResponseToJson(UserRolesResponse instance) =>
    <String, dynamic>{
      'user': instance.user,
      'roles': instance.roles,
      'permissions': instance.permissions,
      'role_count': instance.roleCount,
      'permission_count': instance.permissionCount,
    };

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) => UserInfo(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      isActive: json['is_active'] as bool,
    );

Map<String, dynamic> _$UserInfoToJson(UserInfo instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'full_name': instance.fullName,
      'is_active': instance.isActive,
    };

ApiUserRole _$ApiUserRoleFromJson(Map<String, dynamic> json) => ApiUserRole(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isSystemRole: json['is_system_role'] as bool,
      isActive: json['is_active'] as bool? ?? true,
      assignedAt: DateTime.parse(json['assigned_at'] as String),
      assignedBy: json['assigned_by'] as String?,
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => Permission.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ApiUserRoleToJson(ApiUserRole instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'is_system_role': instance.isSystemRole,
      'is_active': instance.isActive,
      'assigned_at': instance.assignedAt.toIso8601String(),
      'assigned_by': instance.assignedBy,
      'permissions': instance.permissions,
    };

UserPermission _$UserPermissionFromJson(Map<String, dynamic> json) =>
    UserPermission(
      id: json['id'] as String,
      name: json['name'] as String,
      resource: json['resource'] as String,
      action: json['action'] as String,
      fromRole: json['from_role'] as String,
    );

Map<String, dynamic> _$UserPermissionToJson(UserPermission instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'resource': instance.resource,
      'action': instance.action,
      'from_role': instance.fromRole,
    };

CreateRoleRequest _$CreateRoleRequestFromJson(Map<String, dynamic> json) =>
    CreateRoleRequest(
      name: json['name'] as String,
      description: json['description'] as String?,
      isSystemRole: json['is_system_role'] as bool?,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CreateRoleRequestToJson(CreateRoleRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'is_system_role': instance.isSystemRole,
      'permissions': instance.permissions,
    };

UpdateRoleRequest _$UpdateRoleRequestFromJson(Map<String, dynamic> json) =>
    UpdateRoleRequest(
      name: json['name'] as String?,
      description: json['description'] as String?,
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$UpdateRoleRequestToJson(UpdateRoleRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'permissions': instance.permissions,
    };

CreatePermissionRequest _$CreatePermissionRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePermissionRequest(
      name: json['name'] as String,
      description: json['description'] as String?,
      resource: json['resource'] as String,
      action: json['action'] as String,
    );

Map<String, dynamic> _$CreatePermissionRequestToJson(
        CreatePermissionRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'resource': instance.resource,
      'action': instance.action,
    };

AssignRolesRequest _$AssignRolesRequestFromJson(Map<String, dynamic> json) =>
    AssignRolesRequest(
      roleIds:
          (json['role_ids'] as List<dynamic>).map((e) => e as String).toList(),
      replaceExisting: json['replace_existing'] as bool? ?? true,
    );

Map<String, dynamic> _$AssignRolesRequestToJson(AssignRolesRequest instance) =>
    <String, dynamic>{
      'role_ids': instance.roleIds,
      'replace_existing': instance.replaceExisting,
    };

RemoveRoleRequest _$RemoveRoleRequestFromJson(Map<String, dynamic> json) =>
    RemoveRoleRequest(
      roleId: json['role_id'] as String,
    );

Map<String, dynamic> _$RemoveRoleRequestToJson(RemoveRoleRequest instance) =>
    <String, dynamic>{
      'role_id': instance.roleId,
    };
