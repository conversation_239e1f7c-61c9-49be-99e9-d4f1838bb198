# 🧪 SRSR Property Management - Testing Project

## 📋 Overview

This is a dedicated testing project for SRSR Property Management system, focusing on comprehensive automation testing with priority on admin dashboard features.

## 🏗️ Project Structure

```
testing/
├── flutter_tests/          # Flutter integration & widget tests
│   ├── integration_test/    # Integration tests
│   │   ├── admin/          # Admin dashboard tests (TOP PRIORITY)
│   │   ├── auth/           # Authentication tests
│   │   ├── roles/          # Role-based tests
│   │   └── workflows/      # End-to-end workflows
│   ├── lib/                # Test app and utilities
│   │   ├── helpers/        # Test helper utilities
│   │   ├── page_objects/   # Page object models
│   │   ├── factories/      # Test data factories
│   │   └── models/         # Test data models
│   └── test_driver/        # Driver tests
├── api_tests/              # API automation tests
│   ├── tests/              # Test suites
│   │   ├── admin/          # Admin API tests (TOP PRIORITY)
│   │   ├── auth/           # Authentication API tests
│   │   └── integration/    # Cross-module tests
│   └── src/                # Test utilities and helpers
├── scripts/                # Automation scripts
├── config/                 # Test configurations
└── reports/                # Test reports and artifacts
```

## 🎯 Admin Dashboard Testing Priority

### **Phase 1: Core Admin Features (Week 1)**
1. **User Management** - Create, edit, delete, role assignment
2. **Role Management** - Create roles, assign permissions
3. **Permission Configuration** - Screen and widget permissions
4. **Dashboard Overview** - Admin dashboard widgets and stats

### **Phase 2: Advanced Admin Features (Week 2)**
1. **Screen Management** - Custom screen creation
2. **Widget Management** - Widget configuration and permissions
3. **Real-time Updates** - Live permission changes
4. **Security Testing** - Permission enforcement

## 🚀 Quick Start

### **Prerequisites**
- Flutter SDK 3.16.0+
- Node.js 18+
- Backend server running on localhost:3000

### **Setup**
```bash
# Navigate to testing directory
cd testing

# Setup Flutter tests
cd flutter_tests
flutter pub get

# Setup API tests
cd ../api_tests
npm install

# Setup test data
npm run setup

# Run admin dashboard tests
npm run test:admin
```

## 📊 Test Execution

### **Run Admin Tests Only**
```bash
# API tests for admin features
cd api_tests
npm run test:admin

# Flutter integration tests for admin
cd flutter_tests
flutter test integration_test/admin/

# Combined admin test suite
./scripts/run-admin-tests.sh
```

### **Run All Tests**
```bash
# Complete test suite
./scripts/run-all-tests.sh
```

## 📈 Test Coverage Goals

### **Admin Dashboard Coverage**
- ✅ User Management: 100%
- ✅ Role Management: 100%
- ✅ Permission Config: 100%
- ✅ Dashboard Widgets: 100%
- ✅ Screen Management: 90%
- ✅ Security Testing: 95%

## 🔧 Configuration

### **Test Environments**
- **Local**: localhost:3000 (default)
- **Staging**: staging.srsr.com
- **Production**: api.srsr.com (read-only tests)

### **Test Users**
- **Admin**: <EMAIL>
- **Manager**: <EMAIL>
- **Maintenance**: <EMAIL>
- **Viewer**: <EMAIL>

## 📋 Test Reports

Test reports are generated in:
- **HTML Reports**: `reports/html/`
- **Coverage Reports**: `reports/coverage/`
- **Screenshots**: `reports/screenshots/`
- **JSON Results**: `reports/json/`

## 🎯 Current Focus: Admin Dashboard

This testing project prioritizes admin dashboard functionality to ensure robust user management, role assignment, and permission configuration testing.
