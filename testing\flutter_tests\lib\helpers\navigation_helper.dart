import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';

class NavigationHelper {
  static final _logger = Logger();

  /// Navigate to admin dashboard
  static Future<void> navigateToAdminDashboard(WidgetTester tester) async {
    _logger.i('Navigating to admin dashboard');
    
    await _navigateViaBottomNav(tester, 'admin') ||
    await _navigateViaDrawer(tester, 'Admin') ||
    await _navigateViaAppBar(tester, 'admin');
    
    await tester.pumpAndSettle();
    await _verifyScreenLoaded(tester, 'admin_dashboard');
  }

  /// Navigate to user management
  static Future<void> navigateToUserManagement(WidgetTester tester) async {
    _logger.i('Navigating to user management');
    
    // First navigate to admin dashboard if not already there
    await navigateToAdminDashboard(tester);
    
    // Look for user management button/card
    final userManagementButton = find.byKey(const Key('manage_users_button')) ??
                                find.text('Manage Users') ??
                                find.text('User Management');
    
    if (userManagementButton.evaluate().isNotEmpty) {
      await tester.tap(userManagementButton);
      await tester.pumpAndSettle();
    } else {
      // Try alternative navigation paths
      await _navigateViaDrawer(tester, 'User Management') ||
      await _navigateViaBottomNav(tester, 'users');
    }
    
    await _verifyScreenLoaded(tester, 'user_management');
  }

  /// Navigate to role management
  static Future<void> navigateToRoleManagement(WidgetTester tester) async {
    _logger.i('Navigating to role management');
    
    // First navigate to admin dashboard if not already there
    await navigateToAdminDashboard(tester);
    
    // Look for role management button/card
    final roleManagementButton = find.byKey(const Key('manage_roles_button')) ??
                                find.text('Manage Roles') ??
                                find.text('Role Management');
    
    if (roleManagementButton.evaluate().isNotEmpty) {
      await tester.tap(roleManagementButton);
      await tester.pumpAndSettle();
    } else {
      // Try alternative navigation paths
      await _navigateViaDrawer(tester, 'Role Management');
    }
    
    await _verifyScreenLoaded(tester, 'role_management');
  }

  /// Navigate to permission configuration
  static Future<void> navigateToPermissionConfig(WidgetTester tester) async {
    _logger.i('Navigating to permission configuration');
    
    // First navigate to admin dashboard if not already there
    await navigateToAdminDashboard(tester);
    
    // Look for permission config button/card
    final permissionConfigButton = find.byKey(const Key('permission_config_button')) ??
                                  find.text('Permission Config') ??
                                  find.text('Permissions');
    
    if (permissionConfigButton.evaluate().isNotEmpty) {
      await tester.tap(permissionConfigButton);
      await tester.pumpAndSettle();
    } else {
      // Try alternative navigation paths
      await _navigateViaDrawer(tester, 'Permission Config');
    }
    
    await _verifyScreenLoaded(tester, 'permission_config');
  }

  /// Navigate to screen management
  static Future<void> navigateToScreenManagement(WidgetTester tester) async {
    _logger.i('Navigating to screen management');
    
    await navigateToAdminDashboard(tester);
    
    final screenManagementButton = find.byKey(const Key('screen_management_button')) ??
                                  find.text('Screen Management') ??
                                  find.text('Screens');
    
    if (screenManagementButton.evaluate().isNotEmpty) {
      await tester.tap(screenManagementButton);
      await tester.pumpAndSettle();
    }
    
    await _verifyScreenLoaded(tester, 'screen_management');
  }

  /// Navigate to widget management
  static Future<void> navigateToWidgetManagement(WidgetTester tester) async {
    _logger.i('Navigating to widget management');
    
    await navigateToAdminDashboard(tester);
    
    final widgetManagementButton = find.byKey(const Key('widget_management_button')) ??
                                  find.text('Widget Management') ??
                                  find.text('Widgets');
    
    if (widgetManagementButton.evaluate().isNotEmpty) {
      await tester.tap(widgetManagementButton);
      await tester.pumpAndSettle();
    }
    
    await _verifyScreenLoaded(tester, 'widget_management');
  }

  /// Navigate to properties screen
  static Future<void> navigateToProperties(WidgetTester tester) async {
    _logger.i('Navigating to properties');
    
    await _navigateViaBottomNav(tester, 'properties') ||
    await _navigateViaDrawer(tester, 'Properties');
    
    await _verifyScreenLoaded(tester, 'properties');
  }

  /// Navigate to maintenance screen
  static Future<void> navigateToMaintenance(WidgetTester tester) async {
    _logger.i('Navigating to maintenance');
    
    await _navigateViaBottomNav(tester, 'maintenance') ||
    await _navigateViaDrawer(tester, 'Maintenance');
    
    await _verifyScreenLoaded(tester, 'maintenance');
  }

  /// Navigate to attendance screen
  static Future<void> navigateToAttendance(WidgetTester tester) async {
    _logger.i('Navigating to attendance');
    
    await _navigateViaBottomNav(tester, 'attendance') ||
    await _navigateViaDrawer(tester, 'Attendance');
    
    await _verifyScreenLoaded(tester, 'attendance');
  }

  /// Navigate to fuel monitoring screen
  static Future<void> navigateToFuelMonitoring(WidgetTester tester) async {
    _logger.i('Navigating to fuel monitoring');
    
    await _navigateViaBottomNav(tester, 'fuel') ||
    await _navigateViaDrawer(tester, 'Fuel') ||
    await _navigateViaDrawer(tester, 'Generator Fuel');
    
    await _verifyScreenLoaded(tester, 'fuel');
  }

  /// Navigate to dashboard
  static Future<void> navigateToDashboard(WidgetTester tester) async {
    _logger.i('Navigating to dashboard');
    
    await _navigateViaBottomNav(tester, 'dashboard') ||
    await _navigateViaDrawer(tester, 'Dashboard') ||
    await _navigateViaAppBar(tester, 'dashboard');
    
    await _verifyScreenLoaded(tester, 'dashboard');
  }

  /// Generic navigation to any screen
  static Future<void> navigateToScreen(WidgetTester tester, String screenName) async {
    _logger.i('Navigating to screen: $screenName');
    
    switch (screenName.toLowerCase()) {
      case 'dashboard':
        await navigateToDashboard(tester);
        break;
      case 'properties':
        await navigateToProperties(tester);
        break;
      case 'maintenance':
        await navigateToMaintenance(tester);
        break;
      case 'attendance':
        await navigateToAttendance(tester);
        break;
      case 'fuel':
        await navigateToFuelMonitoring(tester);
        break;
      case 'admin':
        await navigateToAdminDashboard(tester);
        break;
      case 'user_management':
        await navigateToUserManagement(tester);
        break;
      case 'role_management':
        await navigateToRoleManagement(tester);
        break;
      case 'permission_config':
        await navigateToPermissionConfig(tester);
        break;
      case 'screen_management':
        await navigateToScreenManagement(tester);
        break;
      case 'widget_management':
        await navigateToWidgetManagement(tester);
        break;
      default:
        throw Exception('Unknown screen: $screenName');
    }
  }

  /// Verify accessible screens for a user
  static Future<void> verifyAccessibleScreens(
    WidgetTester tester, 
    List<String> expectedScreens,
  ) async {
    _logger.i('Verifying accessible screens: $expectedScreens');
    
    for (final screen in expectedScreens) {
      try {
        await navigateToScreen(tester, screen);
        _logger.d('✅ Screen accessible: $screen');
      } catch (e) {
        _logger.e('❌ Screen not accessible: $screen - $e');
        rethrow;
      }
    }
  }

  /// Verify restricted screens for a user
  static Future<void> verifyRestrictedScreens(
    WidgetTester tester, 
    List<String> restrictedScreens,
  ) async {
    _logger.i('Verifying restricted screens: $restrictedScreens');
    
    for (final screen in restrictedScreens) {
      // Check that navigation items are not present
      final navItems = [
        find.byKey(Key('nav_$screen')),
        find.byKey(Key('${screen}_nav')),
        find.byKey(Key('bottom_nav_$screen')),
      ];
      
      bool found = false;
      for (final navItem in navItems) {
        if (navItem.evaluate().isNotEmpty) {
          found = true;
          break;
        }
      }
      
      if (found) {
        throw Exception('Restricted screen navigation found: $screen');
      }
      
      _logger.d('✅ Screen properly restricted: $screen');
    }
  }

  /// Navigate via bottom navigation
  static Future<bool> _navigateViaBottomNav(WidgetTester tester, String screen) async {
    final bottomNavItems = [
      find.byKey(Key('bottom_nav_$screen')),
      find.byKey(Key('nav_$screen')),
    ];
    
    for (final navItem in bottomNavItems) {
      if (navItem.evaluate().isNotEmpty) {
        await tester.tap(navItem);
        await tester.pumpAndSettle();
        return true;
      }
    }
    
    return false;
  }

  /// Navigate via drawer/hamburger menu
  static Future<bool> _navigateViaDrawer(WidgetTester tester, String itemText) async {
    // Try to open drawer
    final drawerButton = find.byKey(const Key('hamburger_menu')) ??
                        find.byIcon(Icons.menu);
    
    if (drawerButton.evaluate().isNotEmpty) {
      await tester.tap(drawerButton);
      await tester.pumpAndSettle();
      
      // Look for menu item
      final menuItem = find.text(itemText);
      if (menuItem.evaluate().isNotEmpty) {
        await tester.tap(menuItem);
        await tester.pumpAndSettle();
        return true;
      }
    }
    
    return false;
  }

  /// Navigate via app bar actions
  static Future<bool> _navigateViaAppBar(WidgetTester tester, String screen) async {
    final appBarActions = [
      find.byKey(Key('appbar_$screen')),
      find.byKey(Key('${screen}_action')),
    ];
    
    for (final action in appBarActions) {
      if (action.evaluate().isNotEmpty) {
        await tester.tap(action);
        await tester.pumpAndSettle();
        return true;
      }
    }
    
    return false;
  }

  /// Verify screen has loaded
  static Future<void> _verifyScreenLoaded(WidgetTester tester, String screenKey) async {
    await tester.pumpAndSettle();
    
    final screenIndicators = [
      find.byKey(Key('${screenKey}_screen')),
      find.byKey(Key(screenKey)),
      find.byKey(Key('${screenKey}_page')),
    ];
    
    bool screenFound = false;
    for (final indicator in screenIndicators) {
      if (indicator.evaluate().isNotEmpty) {
        screenFound = true;
        break;
      }
    }
    
    if (!screenFound) {
      _logger.w('Screen verification failed for: $screenKey');
      // Don't throw exception, just log warning as screen keys might vary
    } else {
      _logger.d('✅ Screen loaded successfully: $screenKey');
    }
  }

  /// Go back to previous screen
  static Future<void> goBack(WidgetTester tester) async {
    final backButton = find.byKey(const Key('back_button')) ??
                      find.byIcon(Icons.arrow_back) ??
                      find.byTooltip('Back');
    
    if (backButton.evaluate().isNotEmpty) {
      await tester.tap(backButton);
      await tester.pumpAndSettle();
    } else {
      // Try system back
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/platform',
        null,
        (data) {},
      );
      await tester.pumpAndSettle();
    }
  }

  /// Wait for navigation to complete
  static Future<void> waitForNavigation(WidgetTester tester) async {
    await tester.pumpAndSettle();
    await tester.pump(const Duration(milliseconds: 500));
    await tester.pumpAndSettle();
  }
}
