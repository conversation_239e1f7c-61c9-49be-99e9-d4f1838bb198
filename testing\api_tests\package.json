{"name": "srsr-api-testing", "version": "1.0.0", "description": "API automation testing for SRSR Property Management - Admin Dashboard Focus", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:admin": "jest tests/admin --runInBand", "test:auth": "jest tests/auth --runInBand", "test:integration": "jest tests/integration --runInBand", "test:user-management": "jest tests/admin/user_management.test.js", "test:role-management": "jest tests/admin/role_management.test.js", "test:permission-config": "jest tests/admin/permission_config.test.js", "test:screen-management": "jest tests/admin/screen_management.test.js", "test:widget-management": "jest tests/admin/widget_management.test.js", "test:all": "jest --runInBand", "test:parallel": "jest --maxWorkers=4", "report": "jest --coverage --coverageReporters=html", "report:json": "jest --coverage --coverageReporters=json", "setup": "node scripts/setup-test-environment.js", "cleanup": "node scripts/cleanup-test-data.js", "health-check": "node scripts/health-check.js"}, "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "faker": "^6.6.6", "uuid": "^9.0.1", "lodash": "^4.17.21"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-environment-node": "^29.7.0"}, "jest": {"testEnvironment": "node", "roots": ["<rootDir>/tests"], "testMatch": ["**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "!src/**/*.config.js"], "coverageDirectory": "reports/coverage", "coverageReporters": ["text", "lcov", "html"], "setupFilesAfterEnv": ["<rootDir>/src/config/jest.setup.js"], "reporters": ["default", ["jest-html-reporter", {"pageTitle": "SRSR Admin API Test Report", "outputPath": "reports/html/admin-api-test-report.html", "includeFailureMsg": true, "includeSuiteFailure": true}], ["jest-junit", {"outputDirectory": "reports/junit", "outputName": "admin-api-test-results.xml"}]], "testTimeout": 30000, "maxWorkers": 1, "verbose": true}, "keywords": ["testing", "api", "automation", "admin", "property-management"], "author": "SRSR Testing Team", "license": "MIT"}