import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../lib/helpers/auth_helper.dart';
import '../../lib/helpers/navigation_helper.dart';
import '../../lib/page_objects/properties_page.dart';
import '../../lib/factories/user_data_factory.dart';
import '../../lib/models/user_data.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Properties Screen Tests', () {
    late PropertiesPage propertiesPage;
    final List<String> createdPropertyNames = [];

    setUpAll(() async {
      // Setup test environment
    });

    setUp(() async {
      // Start fresh for each test
    });

    tearDown(() async {
      // Cleanup created properties after each test
      if (createdPropertyNames.isNotEmpty) {
        try {
          await AuthHelper.loginAsAdmin(tester);
          await NavigationHelper.navigateToProperties(tester);
          propertiesPage = PropertiesPage(tester);
          
          for (final propertyName in createdPropertyNames) {
            try {
              await propertiesPage.deleteProperty(propertyName);
            } catch (e) {
              print('Failed to cleanup property $propertyName: $e');
            }
          }
          createdPropertyNames.clear();
        } catch (e) {
          print('Failed to cleanup test data: $e');
        }
      }
    });

    testWidgets('Admin can create residential property', (tester) async {
      // Step 1: Login as admin
      await AuthHelper.loginAsAdmin(tester);
      
      // Step 2: Navigate to properties
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Step 3: Create test property data
      final propertyData = PropertyDataFactory.createResidential();
      createdPropertyNames.add(propertyData.name);
      
      // Step 4: Create property through UI
      await propertiesPage.createProperty(propertyData);
      
      // Step 5: Verify property creation
      await propertiesPage.verifyPropertyExists(propertyData.name);
    });

    testWidgets('Admin can create office property', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      final propertyData = PropertyDataFactory.createOffice();
      createdPropertyNames.add(propertyData.name);
      
      await propertiesPage.createProperty(propertyData);
      await propertiesPage.verifyPropertyExists(propertyData.name);
    });

    testWidgets('Admin can create construction site property', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      final propertyData = PropertyDataFactory.createConstruction();
      createdPropertyNames.add(propertyData.name);
      
      await propertiesPage.createProperty(propertyData);
      await propertiesPage.verifyPropertyExists(propertyData.name);
    });

    testWidgets('Property creation validates required fields', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Try to create property with empty form
      await tester.tap(propertiesPage.addPropertyFab);
      await tester.pumpAndSettle();
      
      await tester.tap(propertiesPage.savePropertyButton);
      await tester.pumpAndSettle();
      
      // Verify validation errors
      await propertiesPage.verifyValidationError('Property name is required');
      await propertiesPage.verifyValidationError('Address is required');
    });

    testWidgets('Admin can edit existing property', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Create property first
      final originalPropertyData = PropertyDataFactory.createResidential();
      createdPropertyNames.add(originalPropertyData.name);
      await propertiesPage.createProperty(originalPropertyData);
      
      // Edit property
      final updatedPropertyData = originalPropertyData.copyWith(
        description: 'Updated property description for testing',
        city: 'Updated City',
      );
      
      await propertiesPage.editProperty(originalPropertyData.name, updatedPropertyData);
      await propertiesPage.verifyPropertyExists(updatedPropertyData.name);
    });

    testWidgets('Admin can filter properties by type', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Create properties of different types
      final residentialProperty = PropertyDataFactory.createResidential();
      final officeProperty = PropertyDataFactory.createOffice();
      final constructionProperty = PropertyDataFactory.createConstruction();
      
      createdPropertyNames.addAll([
        residentialProperty.name,
        officeProperty.name,
        constructionProperty.name,
      ]);
      
      await propertiesPage.createProperty(residentialProperty);
      await propertiesPage.createProperty(officeProperty);
      await propertiesPage.createProperty(constructionProperty);
      
      // Test filtering by residential
      await propertiesPage.filterPropertiesByType('residential');
      await propertiesPage.verifyPropertyExists(residentialProperty.name);
      
      // Test filtering by office
      await propertiesPage.filterPropertiesByType('office');
      await propertiesPage.verifyPropertyExists(officeProperty.name);
      
      // Test filtering by construction
      await propertiesPage.filterPropertiesByType('construction');
      await propertiesPage.verifyPropertyExists(constructionProperty.name);
      
      // Test showing all
      await propertiesPage.filterPropertiesByType('all');
      await propertiesPage.verifyPropertyExists(residentialProperty.name);
      await propertiesPage.verifyPropertyExists(officeProperty.name);
      await propertiesPage.verifyPropertyExists(constructionProperty.name);
    });

    testWidgets('Admin can search for properties', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Create test property
      final propertyData = PropertyDataFactory.createResidential();
      createdPropertyNames.add(propertyData.name);
      await propertiesPage.createProperty(propertyData);
      
      // Search for property
      await propertiesPage.searchProperties(propertyData.name);
      await propertiesPage.verifyPropertyExists(propertyData.name);
      
      // Clear search
      await propertiesPage.clearSearch();
    });

    testWidgets('Admin can navigate to property details', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Create test property
      final propertyData = PropertyDataFactory.createOffice();
      createdPropertyNames.add(propertyData.name);
      await propertiesPage.createProperty(propertyData);
      
      // Navigate to property details
      await propertiesPage.navigateToPropertyDetails(propertyData.name);
      
      // Test different tabs
      await propertiesPage.switchToOverviewTab();
      await propertiesPage.switchToServicesTab();
      await propertiesPage.switchToActivityTab();
      await propertiesPage.switchToReportsTab();
    });

    testWidgets('Property details provide navigation to related screens', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Create test property
      final propertyData = PropertyDataFactory.createConstruction();
      createdPropertyNames.add(propertyData.name);
      await propertiesPage.createProperty(propertyData);
      
      // Navigate to property details
      await propertiesPage.navigateToPropertyDetails(propertyData.name);
      
      // Test navigation to maintenance
      await propertiesPage.navigateToMaintenanceFromProperty();
      
      // Go back to property details
      await NavigationHelper.navigateToProperties(tester);
      await propertiesPage.navigateToPropertyDetails(propertyData.name);
      
      // Test navigation to attendance
      await propertiesPage.navigateToAttendanceFromProperty();
      
      // Go back to property details
      await NavigationHelper.navigateToProperties(tester);
      await propertiesPage.navigateToPropertyDetails(propertyData.name);
      
      // Test navigation to fuel monitoring
      await propertiesPage.navigateToFuelFromProperty();
    });

    testWidgets('Property manager can view and edit assigned properties', (tester) async {
      // Login as property manager
      await AuthHelper.loginWithRole(tester, 'property_manager');
      
      // Navigate to properties
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Verify property manager can access properties screen
      await propertiesPage.navigateToProperties();
      
      // Property manager should be able to view properties
      // but may have limited editing capabilities
    });

    testWidgets('Maintenance staff has limited property access', (tester) async {
      // Login as maintenance staff
      await AuthHelper.loginWithRole(tester, 'maintenance_staff');
      
      // Verify maintenance staff has limited access to properties
      // They might only see properties related to their maintenance tasks
      await NavigationHelper.verifyRestrictedScreens(tester, ['properties']);
    });

    testWidgets('Viewer can only view properties without editing', (tester) async {
      // Login as viewer
      await AuthHelper.loginWithRole(tester, 'viewer');
      
      // Verify viewer has read-only access or no access to properties
      await NavigationHelper.verifyRestrictedScreens(tester, ['properties']);
    });

    testWidgets('Properties screen handles empty state correctly', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // If no properties exist, should show appropriate message
      // This test would depend on the actual implementation
      // For now, we'll just verify the screen loads
      await propertiesPage.navigateToProperties();
    });

    testWidgets('Properties screen supports refresh functionality', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Test refresh functionality
      await propertiesPage.refreshProperties();
    });

    testWidgets('Property creation supports bulk operations', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Create multiple properties
      final properties = PropertyDataFactory.createBulkProperties(3);
      
      for (final property in properties) {
        createdPropertyNames.add(property.name);
        await propertiesPage.createProperty(property);
        await propertiesPage.verifyPropertyExists(property.name);
      }
    });

    testWidgets('Property deletion requires confirmation', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      // Create property to delete
      final propertyData = PropertyDataFactory.createResidential();
      await propertiesPage.createProperty(propertyData);
      
      // Delete property (no need to add to cleanup list since it's being deleted)
      await propertiesPage.deleteProperty(propertyData.name);
      
      // Verify property is deleted
      expect(find.text(propertyData.name), findsNothing);
    });
  });
}
