import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../lib/helpers/auth_helper.dart';
import '../../lib/helpers/navigation_helper.dart';
import '../../lib/page_objects/role_management_page.dart';
import '../../lib/page_objects/user_management_page.dart';
import '../../lib/factories/role_data_factory.dart';
import '../../lib/factories/user_data_factory.dart';
import '../../lib/models/user_data.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Admin Role Management Tests', () {
    late RoleManagementPage roleManagementPage;
    late UserManagementPage userManagementPage;
    final List<String> createdRoleNames = [];
    final List<String> createdUserEmails = [];

    setUpAll(() async {
      // Setup test environment
    });

    setUp(() async {
      // Start fresh for each test
    });

    tearDown(() async {
      // Cleanup created roles and users after each test
      if (createdRoleNames.isNotEmpty || createdUserEmails.isNotEmpty) {
        try {
          await AuthHelper.loginAsAdmin(tester);
          
          // Cleanup users first (they might depend on roles)
          if (createdUserEmails.isNotEmpty) {
            await NavigationHelper.navigateToUserManagement(tester);
            userManagementPage = UserManagementPage(tester);
            
            for (final email in createdUserEmails) {
              try {
                await userManagementPage.deleteUser(email);
              } catch (e) {
                print('Failed to cleanup user $email: $e');
              }
            }
            createdUserEmails.clear();
          }
          
          // Cleanup roles
          if (createdRoleNames.isNotEmpty) {
            await NavigationHelper.navigateToRoleManagement(tester);
            roleManagementPage = RoleManagementPage(tester);
            
            for (final roleName in createdRoleNames) {
              try {
                await roleManagementPage.deleteRole(roleName);
              } catch (e) {
                print('Failed to cleanup role $roleName: $e');
              }
            }
            createdRoleNames.clear();
          }
        } catch (e) {
          print('Failed to cleanup test data: $e');
        }
      }
    });

    testWidgets('Admin can create custom role with permissions', (tester) async {
      // Step 1: Login as admin
      await AuthHelper.loginAsAdmin(tester);
      
      // Step 2: Navigate to role management
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Step 3: Create test role data
      final roleData = RoleDataFactory.createSiteCoordinator();
      createdRoleNames.add(roleData.name);
      
      // Step 4: Create role through UI
      await roleManagementPage.createRole(roleData);
      
      // Step 5: Verify role creation
      await roleManagementPage.verifyRoleExists(roleData.name);
      
      // Step 6: Verify role has correct permissions
      await roleManagementPage.verifyRoleHasPermissions(roleData.name, roleData.permissions);
    });

    testWidgets('Admin can create facilities manager role', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      final roleData = RoleDataFactory.createFacilitiesManager();
      createdRoleNames.add(roleData.name);
      
      await roleManagementPage.createRole(roleData);
      await roleManagementPage.verifyRoleExists(roleData.name);
      await roleManagementPage.verifyRoleHasPermissions(roleData.name, roleData.permissions);
    });

    testWidgets('Admin can create security supervisor role', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      final roleData = RoleDataFactory.createSecuritySupervisor();
      createdRoleNames.add(roleData.name);
      
      await roleManagementPage.createRole(roleData);
      await roleManagementPage.verifyRoleExists(roleData.name);
      await roleManagementPage.verifyRoleHasPermissions(roleData.name, roleData.permissions);
    });

    testWidgets('Role creation validates required fields', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Try to create role with empty form
      await tester.tap(roleManagementPage.createRoleFab);
      await tester.pumpAndSettle();
      
      await tester.tap(roleManagementPage.createRoleButton);
      await tester.pumpAndSettle();
      
      // Verify validation errors
      await roleManagementPage.verifyValidationError('Role name is required');
      await roleManagementPage.verifyValidationError('Description is required');
    });

    testWidgets('Role creation validates unique role name', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Create first role
      final roleData = RoleDataFactory.createCustomRole();
      createdRoleNames.add(roleData.name);
      await roleManagementPage.createRole(roleData);
      
      // Try to create duplicate role
      final duplicateRoleData = RoleDataFactory.createDuplicate(roleData.name);
      
      await tester.tap(roleManagementPage.createRoleFab);
      await tester.pumpAndSettle();
      
      await tester.enterText(roleManagementPage.roleNameField, duplicateRoleData.name);
      await tester.enterText(roleManagementPage.roleDescriptionField, duplicateRoleData.description);
      
      await tester.tap(roleManagementPage.createRoleButton);
      await tester.pumpAndSettle();
      
      await roleManagementPage.verifyValidationError('Role name already exists');
    });

    testWidgets('Admin can edit existing role', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Create role first
      final originalRoleData = RoleDataFactory.createCustomRole();
      createdRoleNames.add(originalRoleData.name);
      await roleManagementPage.createRole(originalRoleData);
      
      // Edit role
      final updatedRoleData = originalRoleData.copyWith(
        description: 'Updated role description',
        permissions: [...originalRoleData.permissions, 'reports.export'],
      );
      
      await roleManagementPage.editRole(originalRoleData.name, updatedRoleData);
      await roleManagementPage.verifyRoleExists(updatedRoleData.name);
      await roleManagementPage.verifyRoleHasPermissions(updatedRoleData.name, updatedRoleData.permissions);
    });

    testWidgets('Admin can manage role permissions', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Create role with minimal permissions
      final roleData = RoleDataFactory.createMinimalRole();
      createdRoleNames.add(roleData.name);
      await roleManagementPage.createRole(roleData);
      
      // Add more permissions
      final newPermissions = [
        'properties.read',
        'maintenance.read',
        'maintenance.create',
        'reports.generate',
      ];
      
      await roleManagementPage.manageRolePermissions(roleData.name, newPermissions);
      await roleManagementPage.verifyRoleHasPermissions(roleData.name, newPermissions);
    });

    testWidgets('Admin can delete custom role', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Create role first
      final roleData = RoleDataFactory.createCustomRole();
      await roleManagementPage.createRole(roleData);
      
      // Delete role
      await roleManagementPage.deleteRole(roleData.name);
      
      // Verify role is deleted (no need to add to cleanup list)
      expect(find.text(roleData.name), findsNothing);
    });

    testWidgets('Admin can search for roles', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Create test role
      final roleData = RoleDataFactory.createCustomRole();
      createdRoleNames.add(roleData.name);
      await roleManagementPage.createRole(roleData);
      
      // Search for role
      await roleManagementPage.searchRoles(roleData.name);
      await roleManagementPage.verifyRoleExists(roleData.name);
      
      // Clear search
      await roleManagementPage.clearSearch();
    });

    testWidgets('Admin can view all permissions', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Switch to permissions tab
      await roleManagementPage.viewAllPermissions();
      
      // Verify standard permissions exist
      final standardPermissions = [
        'properties.read',
        'maintenance.read',
        'dashboard.view',
        'reports.generate',
      ];
      
      for (final permission in standardPermissions) {
        await roleManagementPage.verifyPermissionExists(permission);
      }
    });

    testWidgets('Created role can be assigned to user', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Create custom role
      final roleData = RoleDataFactory.createReportingRole();
      createdRoleNames.add(roleData.name);
      await roleManagementPage.createRole(roleData);
      
      // Navigate to user management
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      // Create user with custom role
      final userData = UserDataFactory.createCustomRole(
        roles: [roleData.name],
        expectedScreens: ['dashboard', 'properties', 'maintenance'],
      );
      createdUserEmails.add(userData.email);
      
      await userManagementPage.createUser(userData);
      await userManagementPage.verifyUserExists(userData.email);
      
      // Test user login with custom role
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      
      // Verify user has expected permissions based on role
      await NavigationHelper.verifyAccessibleScreens(tester, userData.expectedScreens);
    });

    testWidgets('Role with escalation permissions works correctly', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Create escalation role
      final roleData = RoleDataFactory.createEscalationRole();
      createdRoleNames.add(roleData.name);
      await roleManagementPage.createRole(roleData);
      
      // Create user with escalation role
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final userData = UserDataFactory.createCustomRole(
        roles: [roleData.name],
        expectedScreens: ['dashboard', 'maintenance'],
      );
      createdUserEmails.add(userData.email);
      
      await userManagementPage.createUser(userData);
      
      // Test user login and verify escalation permissions
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      
      // Navigate to maintenance to verify escalation capabilities
      await NavigationHelper.navigateToMaintenance(tester);
      
      // Verify escalation-related UI elements are visible
      // (This would depend on the actual maintenance screen implementation)
    });

    testWidgets('Role permissions are enforced in real-time', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Create role with limited permissions
      final roleData = RoleDataFactory.createForPermissionTesting();
      createdRoleNames.add(roleData.name);
      await roleManagementPage.createRole(roleData);
      
      // Create user with this role
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final userData = UserDataFactory.createCustomRole(
        roles: [roleData.name],
        expectedScreens: ['dashboard', 'properties', 'maintenance'],
      );
      createdUserEmails.add(userData.email);
      
      await userManagementPage.createUser(userData);
      
      // Login as user and verify initial permissions
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      
      // Verify limited access
      await NavigationHelper.verifyAccessibleScreens(tester, ['dashboard', 'properties', 'maintenance']);
      await NavigationHelper.verifyRestrictedScreens(tester, ['admin', 'user_management']);
      
      // TODO: In a real implementation, we would test real-time permission updates
      // by having admin modify role permissions while user is logged in
      // and verify that UI updates immediately without requiring re-login
    });

    testWidgets('Non-admin user cannot access role management', (tester) async {
      // Login as property manager
      await AuthHelper.loginWithRole(tester, 'property_manager');
      
      // Verify role management is not accessible
      await NavigationHelper.verifyRestrictedScreens(tester, ['role_management']);
      
      // Try direct navigation (should fail gracefully)
      try {
        await NavigationHelper.navigateToRoleManagement(tester);
        fail('Should not be able to navigate to role management');
      } catch (e) {
        // Expected to fail
      }
    });

    testWidgets('System roles cannot be deleted', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      // Try to delete a system role (like 'admin')
      final systemRoles = ['admin', 'property_manager', 'maintenance_staff', 'viewer'];
      
      for (final systemRole in systemRoles) {
        // Verify delete button is not present or disabled for system roles
        final deleteButton = roleManagementPage.deleteRoleButton(systemRole);
        expect(deleteButton, findsNothing, 
               reason: 'Delete button should not be present for system role: $systemRole');
      }
    });
  });
}
