const { faker } = require('@faker-js/faker');
const { v4: uuidv4 } = require('uuid');
const config = require('../config/test.config');

class UserFactory {
  /**
   * Create admin user data
   */
  static createAdmin() {
    const timestamp = Date.now();

    return {
      fullName: `Test Admin ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.admin.${timestamp}@srsr.com`,
      password: 'TestAdmin123!',
      username: `testadmin${timestamp}`,
      phone: faker.phone.number(),
      roles: ['admin'],
      isActive: true
    };
  }

  /**
   * Create property manager user data
   */
  static createPropertyManager() {
    const timestamp = Date.now();

    return {
      fullName: `Test Property Manager ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.manager.${timestamp}@srsr.com`,
      password: '<PERSON><PERSON>anager123!',
      username: `testmanager${timestamp}`,
      phone: faker.phone.number(),
      roles: ['property_manager'],
      isActive: true
    };
  }

  /**
   * Create maintenance staff user data
   */
  static createMaintenanceStaff() {
    const timestamp = Date.now();

    return {
      fullName: `Test Maintenance Staff ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.maintenance.${timestamp}@srsr.com`,
      password: 'TestMaintenance123!',
      username: `testmaintenance${timestamp}`,
      phone: faker.phone.number(),
      roles: ['maintenance_staff'],
      isActive: true
    };
  }

  /**
   * Create security guard user data
   */
  static createSecurityGuard() {
    const timestamp = Date.now();

    return {
      fullName: `Test Security Guard ${faker.name.firstName()}`,
      email: `${config.testData.userPrefix}.security.${timestamp}@srsr.com`,
      password: 'TestSecurity123!',
      username: `testsecurity${timestamp}`,
      phone: faker.phone.phoneNumber(),
      roles: ['security_guard'],
      isActive: true
    };
  }

  /**
   * Create viewer user data
   */
  static createViewer() {
    const timestamp = Date.now();

    return {
      fullName: `Test Viewer ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.viewer.${timestamp}@srsr.com`,
      password: 'TestViewer123!',
      username: `testviewer${timestamp}`,
      phone: faker.phone.number(),
      roles: ['viewer'],
      isActive: true
    };
  }

  /**
   * Create basic user data
   */
  static createBasicUser() {
    const timestamp = Date.now();

    return {
      fullName: `Test User ${faker.name.firstName()}`,
      email: `${config.testData.userPrefix}.basic.${timestamp}@srsr.com`,
      password: 'TestUser123!',
      username: `testuser${timestamp}`,
      phone: faker.phone.phoneNumber(),
      roles: ['viewer'],
      isActive: true
    };
  }

  /**
   * Create user with invalid email
   */
  static createWithInvalidEmail() {
    return {
      fullName: 'Invalid Email User',
      email: 'invalid-email-format',
      password: 'ValidPassword123!',
      roles: ['viewer'],
      isActive: true
    };
  }

  /**
   * Create user with weak password
   */
  static createWithWeakPassword() {
    const timestamp = Date.now();

    return {
      fullName: 'Weak Password User',
      email: `${config.testData.userPrefix}.weak.${timestamp}@srsr.com`,
      password: '123',
      roles: ['viewer'],
      isActive: true
    };
  }

  /**
   * Create user with missing required fields
   */
  static createIncomplete() {
    return {
      // Missing fullName, email, password
      roles: ['viewer'],
      isActive: true
    };
  }

  /**
   * Create user with custom roles
   */
  static createWithCustomRoles(roles) {
    const timestamp = Date.now();

    return {
      fullName: `Custom Role User ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.custom.${timestamp}@srsr.com`,
      password: 'CustomRole123!',
      username: `customrole${timestamp}`,
      phone: faker.phone.number(),
      roles: roles,
      isActive: true
    };
  }

  /**
   * Create user for role assignment testing
   */
  static createForRoleAssignment() {
    const timestamp = Date.now();

    return {
      fullName: 'Role Assignment Test User',
      email: `${config.testData.userPrefix}.roletest.${timestamp}@srsr.com`,
      password: 'RoleTest123!',
      username: `roletest${timestamp}`,
      phone: faker.phone.number(),
      roles: ['viewer'], // Start with minimal role
      isActive: true
    };
  }

  /**
   * Create user for permission testing
   */
  static createForPermissionTesting() {
    const timestamp = Date.now();

    return {
      fullName: 'Permission Test User',
      email: `${config.testData.userPrefix}.permtest.${timestamp}@srsr.com`,
      password: 'PermTest123!',
      username: `permtest${timestamp}`,
      phone: faker.phone.number(),
      roles: ['property_manager'],
      isActive: true
    };
  }

  /**
   * Create multiple users for bulk operations
   */
  static createBulkUsers(count) {
    const users = [];

    for (let i = 0; i < count; i++) {
      const timestamp = Date.now() + i;

      users.push({
        fullName: `Bulk User ${i + 1} ${faker.person.firstName()}`,
        email: `${config.testData.userPrefix}.bulk.${timestamp}@srsr.com`,
        password: 'BulkUser123!',
        username: `bulkuser${timestamp}`,
        phone: faker.phone.number(),
        roles: ['viewer'],
        isActive: true
      });
    }

    return users;
  }

  /**
   * Create user with duplicate email
   */
  static createDuplicate(existingEmail) {
    return {
      fullName: 'Duplicate Email User',
      email: existingEmail,
      password: 'DuplicateTest123!',
      roles: ['viewer'],
      isActive: true
    };
  }

  /**
   * Create user update data
   */
  static createUpdateData() {
    return {
      fullName: `Updated User ${faker.person.firstName()}`,
      phone: faker.phone.number(),
      isActive: true
    };
  }

  /**
   * Create user with all optional fields
   */
  static createComplete() {
    const timestamp = Date.now();

    return {
      fullName: `Complete User ${faker.person.firstName()}`,
      email: `${config.testData.userPrefix}.complete.${timestamp}@srsr.com`,
      password: 'CompleteUser123!',
      username: `completeuser${timestamp}`,
      phone: faker.phone.number(),
      roles: ['property_manager'],
      isActive: true,
      address: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      zipCode: faker.location.zipCode(),
      country: faker.location.country(),
      dateOfBirth: faker.date.past({ years: 30, refDate: new Date('2000-01-01') }),
      emergencyContact: {
        name: faker.person.fullName(),
        phone: faker.phone.number(),
        relationship: 'Emergency Contact'
      }
    };
  }

  /**
   * Create user for specific test scenario
   */
  static createForScenario(scenario) {
    const timestamp = Date.now();

    const baseUser = {
      fullName: `${scenario} Test User`,
      email: `${config.testData.userPrefix}.${scenario.toLowerCase()}.${timestamp}@srsr.com`,
      password: 'ScenarioTest123!',
      username: `${scenario.toLowerCase()}${timestamp}`,
      phone: faker.phone.number(),
      isActive: true
    };

    switch (scenario.toLowerCase()) {
      case 'admin_creation':
        return { ...baseUser, roles: ['admin'] };
      case 'role_assignment':
        return { ...baseUser, roles: ['viewer'] };
      case 'permission_testing':
        return { ...baseUser, roles: ['property_manager'] };
      case 'bulk_operations':
        return { ...baseUser, roles: ['viewer'] };
      case 'validation_testing':
        return { ...baseUser, roles: ['viewer'] };
      default:
        return { ...baseUser, roles: ['viewer'] };
    }
  }

  /**
   * Generate random user data
   */
  static generateRandom() {
    const timestamp = Date.now();
    const roles = ['admin', 'property_manager', 'maintenance_staff', 'security_guard', 'viewer'];
    const randomRole = roles[Math.floor(Math.random() * roles.length)];

    return {
      fullName: faker.person.fullName(),
      email: `${config.testData.userPrefix}.random.${timestamp}@srsr.com`,
      password: faker.internet.password({ length: 12, pattern: /[A-Za-z0-9!@#$%^&*]/ }),
      username: `random${timestamp}`,
      phone: faker.phone.number(),
      roles: [randomRole],
      isActive: faker.datatype.boolean()
    };
  }
}

module.exports = UserFactory;
