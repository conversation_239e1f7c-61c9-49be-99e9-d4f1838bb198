import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../features/auth/presentation/providers/auth_providers.dart';
import '../../../features/admin/data/role_management_api_service.dart' as admin_api;

import '../../network/dio_client.dart';
import '../models/user_role.dart' as core_models;
import '../models/user_permission.dart' as core_models;

// Permission service provider
final permissionServiceProvider = Provider<admin_api.RoleManagementApiService>((ref) {
  final dio = ref.watch(dioClientProvider);
  return admin_api.RoleManagementApiService(dio);
});

// Current user permissions provider
final userPermissionsProvider = FutureProvider<List<core_models.UserPermission>>((ref) async {
  final authState = ref.watch(authStateProvider);

  if (!authState.isAuthenticated || authState.user == null) {
    return [];
  }

  try {
    final permissionService = ref.read(permissionServiceProvider);
    final response = await permissionService.getUserRoles(authState.user!.id);

    if (response.success && response.data != null) {
      // Convert admin UserPermission to core UserPermission
      return response.data!.permissions.map((adminPerm) =>
        core_models.UserPermission(
          id: adminPerm.id,
          name: adminPerm.name,
          resource: adminPerm.resource,
          action: adminPerm.action,
          fromRole: adminPerm.fromRole,
        )
      ).toList();
    }
    return [];
  } catch (e) {
    // Fallback to static permissions from user role
    return [];
  }
});

// Current user roles provider
final userRolesProvider = FutureProvider<List<admin_api.ApiUserRole>>((ref) async {
  final authState = ref.watch(authStateProvider);

  if (!authState.isAuthenticated || authState.user == null) {
    return [];
  }

  try {
    final permissionService = ref.read(permissionServiceProvider);
    final response = await permissionService.getUserRoles(authState.user!.id);

    if (response.success && response.data != null) {
      return response.data!.roles;
    }
    return [];
  } catch (e) {
    return [];
  }
});

// Permission checker provider
final permissionCheckerProvider = Provider<PermissionChecker>((ref) {
  return PermissionChecker(ref);
});

// Permission checker class
class PermissionChecker {
  final Ref _ref;

  PermissionChecker(this._ref);

  // Check if user has specific permission
  Future<bool> hasPermission(String permission) async {
    final permissions = await _ref.read(userPermissionsProvider.future);
    return permissions.any((p) => p.name == permission);
  }

  // Check if user has permission for resource and action
  Future<bool> hasResourcePermission(String resource, String action) async {
    final permissions = await _ref.read(userPermissionsProvider.future);
    return permissions.any((p) => p.resource == resource && p.action == action);
  }

  // Check if user has any of the specified permissions
  Future<bool> hasAnyPermission(List<String> permissions) async {
    final userPermissions = await _ref.read(userPermissionsProvider.future);
    return permissions.any((permission) =>
      userPermissions.any((p) => p.name == permission));
  }

  // Check if user has all of the specified permissions
  Future<bool> hasAllPermissions(List<String> permissions) async {
    final userPermissions = await _ref.read(userPermissionsProvider.future);
    return permissions.every((permission) =>
      userPermissions.any((p) => p.name == permission));
  }

  // Check if user has specific role
  Future<bool> hasRole(String roleName) async {
    final roles = await _ref.read(userRolesProvider.future);
    return roles.any((role) => role.name == roleName);
  }

  // Check if user has any of the specified roles
  Future<bool> hasAnyRole(List<String> roleNames) async {
    final roles = await _ref.read(userRolesProvider.future);
    return roleNames.any((roleName) =>
      roles.any((role) => role.name == roleName));
  }

  // Get user's permission for a specific resource
  Future<List<String>> getResourceActions(String resource) async {
    final permissions = await _ref.read(userPermissionsProvider.future);
    return permissions
        .where((p) => p.resource == resource)
        .map((p) => p.action)
        .toList();
  }

  // Get all resources user has access to
  Future<List<String>> getAccessibleResources() async {
    final permissions = await _ref.read(userPermissionsProvider.future);
    return permissions.map((p) => p.resource).toSet().toList();
  }
}

// Synchronous permission checker for static permissions
final staticPermissionCheckerProvider = Provider<StaticPermissionChecker>((ref) {
  final authState = ref.watch(authStateProvider);
  return StaticPermissionChecker(authState.user?.primaryRole ?? 'viewer');
});

class StaticPermissionChecker {
  final String userRole;

  StaticPermissionChecker(this.userRole);

  bool hasPermission(String permission) {
    // Map user role to UserRole enum and check permissions
    try {
      final role = core_models.UserRole.values.firstWhere(
        (r) => r.name.toLowerCase() == userRole.toLowerCase(),
        orElse: () => core_models.UserRole.viewer,
      );
      return role.hasPermission(permission);
    } catch (e) {
      return false;
    }
  }

  bool hasAnyPermission(List<String> permissions) {
    return permissions.any((permission) => hasPermission(permission));
  }

  bool hasAllPermissions(List<String> permissions) {
    return permissions.every((permission) => hasPermission(permission));
  }

  bool canAccessScreen(String screenPath) {
    try {
      final role = core_models.UserRole.values.firstWhere(
        (r) => r.name.toLowerCase() == userRole.toLowerCase(),
        orElse: () => core_models.UserRole.viewer,
      );
      return role.canAccessScreen(screenPath);
    } catch (e) {
      return false;
    }
  }
}

// Provider for checking if user can access a specific screen
final screenAccessProvider = Provider.family<bool, String>((ref, screenPath) {
  final checker = ref.watch(staticPermissionCheckerProvider);
  return checker.canAccessScreen(screenPath);
});

// Provider for checking if user has a specific permission (static)
final hasPermissionProvider = Provider.family<bool, String>((ref, permission) {
  final checker = ref.watch(staticPermissionCheckerProvider);
  return checker.hasPermission(permission);
});

// Provider for checking if user has any of the specified permissions (static)
final hasAnyPermissionProvider = Provider.family<bool, List<String>>((ref, permissions) {
  final checker = ref.watch(staticPermissionCheckerProvider);
  return checker.hasAnyPermission(permissions);
});

// Provider for checking if user has all of the specified permissions (static)
final hasAllPermissionsProvider = Provider.family<bool, List<String>>((ref, permissions) {
  final checker = ref.watch(staticPermissionCheckerProvider);
  return checker.hasAllPermissions(permissions);
});

// Cached permission provider for intelligent UI rendering
final cachedPermissionProvider = FutureProvider.family<bool, String>((ref, permissionKey) async {
  final permissionChecker = ref.read(permissionCheckerProvider);

  // Parse the permission key to extract requirements
  final requirements = _parsePermissionKey(permissionKey);

  bool hasAccess = true;

  // Check role-based access
  if (requirements.roles.isNotEmpty) {
    if (requirements.requireAll) {
      hasAccess = await permissionChecker.hasAllPermissions(requirements.roles);
    } else {
      hasAccess = await permissionChecker.hasAnyRole(requirements.roles);
    }
  }

  // Check permission-based access
  if (requirements.permissions.isNotEmpty) {
    if (requirements.requireAll) {
      final permissionAccess = await permissionChecker.hasAllPermissions(requirements.permissions);
      hasAccess = hasAccess && permissionAccess;
    } else {
      final permissionAccess = await permissionChecker.hasAnyPermission(requirements.permissions);
      hasAccess = hasAccess || permissionAccess;
    }
  }

  // Check resource-action based access
  if (requirements.resource.isNotEmpty && requirements.action.isNotEmpty) {
    final resourceAccess = await permissionChecker.hasResourcePermission(requirements.resource, requirements.action);
    hasAccess = hasAccess && resourceAccess;
  }

  return hasAccess;
});

// Permission requirements model
class PermissionRequirements {
  final List<String> roles;
  final List<String> permissions;
  final String resource;
  final String action;
  final bool requireAll;

  const PermissionRequirements({
    this.roles = const [],
    this.permissions = const [],
    this.resource = '',
    this.action = '',
    this.requireAll = false,
  });
}

// Parse permission key into requirements
PermissionRequirements _parsePermissionKey(String permissionKey) {
  final parts = permissionKey.split('|');

  List<String> roles = [];
  List<String> permissions = [];
  String resource = '';
  String action = '';
  bool requireAll = false;

  for (final part in parts) {
    if (part.startsWith('roles:')) {
      final rolesPart = part.substring(6);
      if (rolesPart.isNotEmpty) {
        roles = rolesPart.split(',');
      }
    } else if (part.startsWith('permissions:')) {
      final permissionsPart = part.substring(12);
      if (permissionsPart.isNotEmpty) {
        permissions = permissionsPart.split(',');
      }
    } else if (part.startsWith('resource:')) {
      final resourceParts = part.substring(9).split(':');
      if (resourceParts.length >= 2) {
        resource = resourceParts[0];
        action = resourceParts[1];
      }
    } else if (part.startsWith('requireAll:')) {
      requireAll = part.substring(11) == 'true';
    }
  }

  return PermissionRequirements(
    roles: roles,
    permissions: permissions,
    resource: resource,
    action: action,
    requireAll: requireAll,
  );
}

// Intelligent permission pre-loader
final permissionPreloaderProvider = Provider<PermissionPreloader>((ref) {
  return PermissionPreloader(ref);
});

class PermissionPreloader {
  final Ref _ref;
  final Map<String, bool> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Duration _cacheDuration = const Duration(minutes: 5);

  PermissionPreloader(this._ref);

  // Preload common permissions for faster UI rendering
  Future<void> preloadCommonPermissions() async {
    final commonPermissions = [
      'properties.read',
      'properties.create',
      'properties.update',
      'properties.delete',
      'maintenance.read',
      'maintenance.create',
      'maintenance.update',
      'users.read',
      'users.manage',
      'roles.manage',
      'attendance.read',
      'fuel.read',
    ];

    final permissionChecker = _ref.read(permissionCheckerProvider);

    for (final permission in commonPermissions) {
      try {
        final hasPermission = await permissionChecker.hasPermission(permission);
        _cache[permission] = hasPermission;
        _cacheTimestamps[permission] = DateTime.now();
      } catch (e) {
        // Ignore errors during preloading
      }
    }
  }

  // Get cached permission or fetch if not cached
  Future<bool> getPermission(String permission) async {
    final now = DateTime.now();

    // Check if we have a valid cached result
    if (_cache.containsKey(permission) && _cacheTimestamps.containsKey(permission)) {
      final cacheTime = _cacheTimestamps[permission]!;
      if (now.difference(cacheTime) < _cacheDuration) {
        return _cache[permission]!;
      }
    }

    // Fetch fresh permission
    try {
      final permissionChecker = _ref.read(permissionCheckerProvider);
      final hasPermission = await permissionChecker.hasPermission(permission);

      _cache[permission] = hasPermission;
      _cacheTimestamps[permission] = now;

      return hasPermission;
    } catch (e) {
      // On error, return false for security
      return false;
    }
  }

  // Clear cache (useful when user roles change)
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  // Get multiple permissions efficiently
  Future<Map<String, bool>> getMultiplePermissions(List<String> permissions) async {
    final results = <String, bool>{};

    for (final permission in permissions) {
      results[permission] = await getPermission(permission);
    }

    return results;
  }
}
