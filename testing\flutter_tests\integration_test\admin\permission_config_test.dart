import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../lib/helpers/auth_helper.dart';
import '../../lib/helpers/navigation_helper.dart';
import '../../lib/page_objects/permission_config_page.dart';
import '../../lib/page_objects/user_management_page.dart';
import '../../lib/factories/role_data_factory.dart';
import '../../lib/factories/user_data_factory.dart';
import '../../lib/models/user_data.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Admin Permission Configuration Tests', () {
    late PermissionConfigPage permissionConfigPage;
    late UserManagementPage userManagementPage;
    final List<String> createdScreenNames = [];
    final List<String> createdWidgetNames = [];
    final List<String> createdUserEmails = [];

    setUpAll(() async {
      // Setup test environment
    });

    setUp(() async {
      // Start fresh for each test
    });

    tearDown(() async {
      // Cleanup created configurations after each test
      if (createdScreenNames.isNotEmpty || createdWidgetNames.isNotEmpty || createdUserEmails.isNotEmpty) {
        try {
          await AuthHelper.loginAsAdmin(tester);
          
          // Cleanup users first
          if (createdUserEmails.isNotEmpty) {
            await NavigationHelper.navigateToUserManagement(tester);
            userManagementPage = UserManagementPage(tester);
            
            for (final email in createdUserEmails) {
              try {
                await userManagementPage.deleteUser(email);
              } catch (e) {
                print('Failed to cleanup user $email: $e');
              }
            }
            createdUserEmails.clear();
          }
          
          // Cleanup permission configurations
          if (createdScreenNames.isNotEmpty || createdWidgetNames.isNotEmpty) {
            await NavigationHelper.navigateToPermissionConfig(tester);
            permissionConfigPage = PermissionConfigPage(tester);
            
            // Cleanup screens
            for (final screenName in createdScreenNames) {
              try {
                await permissionConfigPage.deleteScreenConfig(screenName);
              } catch (e) {
                print('Failed to cleanup screen $screenName: $e');
              }
            }
            createdScreenNames.clear();
            
            // Cleanup widgets
            for (final widgetName in createdWidgetNames) {
              try {
                // Widget cleanup would be implemented similarly
                print('Widget cleanup not implemented yet: $widgetName');
              } catch (e) {
                print('Failed to cleanup widget $widgetName: $e');
              }
            }
            createdWidgetNames.clear();
          }
        } catch (e) {
          print('Failed to cleanup test data: $e');
        }
      }
    });

    testWidgets('Admin can configure custom screen permissions', (tester) async {
      // Step 1: Login as admin
      await AuthHelper.loginAsAdmin(tester);
      
      // Step 2: Navigate to permission configuration
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Step 3: Create test screen data
      final screenData = ScreenDataFactory.createReportsScreen();
      createdScreenNames.add(screenData.name);
      
      // Step 4: Configure screen permissions
      await permissionConfigPage.configureScreenPermissions(screenData);
      
      // Step 5: Verify screen configuration
      await permissionConfigPage.verifyScreenExists(screenData.name);
    });

    testWidgets('Admin can configure analytics screen permissions', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      final screenData = ScreenDataFactory.createAnalyticsScreen();
      createdScreenNames.add(screenData.name);
      
      await permissionConfigPage.configureScreenPermissions(screenData);
      await permissionConfigPage.verifyScreenExists(screenData.name);
    });

    testWidgets('Admin can configure custom widget permissions', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      final widgetData = WidgetDataFactory.createChartWidget();
      createdWidgetNames.add(widgetData.name);
      
      await permissionConfigPage.configureWidgetPermissions(widgetData);
      await permissionConfigPage.verifyWidgetExists(widgetData.name);
    });

    testWidgets('Admin can configure table widget permissions', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      final widgetData = WidgetDataFactory.createTableWidget();
      createdWidgetNames.add(widgetData.name);
      
      await permissionConfigPage.configureWidgetPermissions(widgetData);
      await permissionConfigPage.verifyWidgetExists(widgetData.name);
    });

    testWidgets('Screen configuration validates required fields', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Try to create screen with empty form
      await permissionConfigPage.switchToScreenPermissionsTab();
      await tester.tap(permissionConfigPage.addScreenButton);
      await tester.pumpAndSettle();
      
      await tester.tap(permissionConfigPage.saveConfigButton);
      await tester.pumpAndSettle();
      
      // Verify validation errors
      await permissionConfigPage.verifyValidationError('Screen name is required');
      await permissionConfigPage.verifyValidationError('Screen title is required');
      await permissionConfigPage.verifyValidationError('Route is required');
    });

    testWidgets('Widget configuration validates required fields', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Try to create widget with empty form
      await permissionConfigPage.switchToWidgetPermissionsTab();
      await tester.tap(permissionConfigPage.addWidgetButton);
      await tester.pumpAndSettle();
      
      await tester.tap(permissionConfigPage.saveConfigButton);
      await tester.pumpAndSettle();
      
      // Verify validation errors
      await permissionConfigPage.verifyValidationError('Widget name is required');
      await permissionConfigPage.verifyValidationError('Widget title is required');
      await permissionConfigPage.verifyValidationError('Widget type is required');
    });

    testWidgets('Admin can edit existing screen permissions', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Create screen first
      final originalScreenData = ScreenDataFactory.createCustomScreen();
      createdScreenNames.add(originalScreenData.name);
      await permissionConfigPage.configureScreenPermissions(originalScreenData);
      
      // Edit screen
      final updatedScreenData = originalScreenData.copyWith(
        title: 'Updated Custom Screen',
        description: 'Updated description for testing',
        requiredPermissions: [...originalScreenData.requiredPermissions, 'reports.export'],
      );
      
      await permissionConfigPage.editScreenPermissions(originalScreenData.name, updatedScreenData);
      await permissionConfigPage.verifyScreenExists(updatedScreenData.name);
    });

    testWidgets('Configured screen permissions are enforced for users', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Create custom screen with specific permissions
      final screenData = ScreenDataFactory.createCustomScreen(
        requiredPermissions: ['reports.generate', 'reports.export'],
        allowedRoles: ['admin', 'property_manager'],
      );
      createdScreenNames.add(screenData.name);
      await permissionConfigPage.configureScreenPermissions(screenData);
      
      // Create user with appropriate role
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final userData = UserDataFactory.createPropertyManager();
      createdUserEmails.add(userData.email);
      await userManagementPage.createUser(userData);
      
      // Test user login and verify screen access
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      
      // Verify user can access the configured screen
      // (This would depend on the actual navigation implementation)
      // For now, we'll verify the user has the expected permissions
      await NavigationHelper.verifyAccessibleScreens(tester, userData.expectedScreens);
    });

    testWidgets('Widget permissions control visibility correctly', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Create widget with specific role restrictions
      final widgetData = WidgetDataFactory.createCustomWidget(
        requiredPermissions: ['maintenance.read'],
        allowedRoles: ['admin', 'maintenance_staff'],
      );
      createdWidgetNames.add(widgetData.name);
      await permissionConfigPage.configureWidgetPermissions(widgetData);
      
      // Create maintenance staff user
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final maintenanceUser = UserDataFactory.createMaintenanceStaff();
      createdUserEmails.add(maintenanceUser.email);
      await userManagementPage.createUser(maintenanceUser);
      
      // Test user login and verify widget visibility
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, maintenanceUser);
      
      // Navigate to dashboard to check widget visibility
      await NavigationHelper.navigateToDashboard(tester);
      
      // Verify widget is visible for maintenance staff
      // (This would depend on the actual widget implementation)
      // For now, we'll verify the user has the expected widget permissions
      expect(maintenanceUser.expectedWidgets['maintenance_summary_widget'], isTrue);
    });

    testWidgets('Permission configuration supports role-based access', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Create screen accessible only to admin
      final adminOnlyScreen = ScreenDataFactory.createCustomScreen(
        name: 'admin_only_screen',
        title: 'Admin Only Screen',
        requiredPermissions: ['users.create', 'roles.create'],
        allowedRoles: ['admin'],
      );
      createdScreenNames.add(adminOnlyScreen.name);
      await permissionConfigPage.configureScreenPermissions(adminOnlyScreen);
      
      // Create property manager user
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final managerUser = UserDataFactory.createPropertyManager();
      createdUserEmails.add(managerUser.email);
      await userManagementPage.createUser(managerUser);
      
      // Test that property manager cannot access admin-only screen
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, managerUser);
      
      // Verify admin-only screen is not accessible
      await NavigationHelper.verifyRestrictedScreens(tester, [adminOnlyScreen.name]);
    });

    testWidgets('Permission configuration supports multiple permission requirements', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Create screen requiring multiple permissions
      final multiPermissionScreen = ScreenDataFactory.createCustomScreen(
        name: 'multi_permission_screen',
        title: 'Multi Permission Screen',
        requiredPermissions: ['properties.read', 'maintenance.read', 'reports.generate'],
        allowedRoles: ['admin', 'property_manager'],
      );
      createdScreenNames.add(multiPermissionScreen.name);
      
      // Set require all permissions
      await permissionConfigPage.switchToScreenPermissionsTab();
      await tester.tap(permissionConfigPage.addScreenButton);
      await tester.pumpAndSettle();
      
      await permissionConfigPage.setRequireAllPermissions(true);
      
      // Continue with screen configuration
      await permissionConfigPage._fillScreenConfigForm(multiPermissionScreen);
      await permissionConfigPage._assignScreenPermissions(multiPermissionScreen.requiredPermissions);
      await permissionConfigPage._assignScreenRoles(multiPermissionScreen.allowedRoles);
      await permissionConfigPage._submitScreenConfig();
      await permissionConfigPage._verifyScreenConfigured(multiPermissionScreen.name);
    });

    testWidgets('Real-time permission updates affect user access immediately', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Create screen with initial permissions
      final screenData = ScreenDataFactory.createCustomScreen(
        requiredPermissions: ['properties.read'],
        allowedRoles: ['admin', 'property_manager'],
      );
      createdScreenNames.add(screenData.name);
      await permissionConfigPage.configureScreenPermissions(screenData);
      
      // Create user
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final userData = UserDataFactory.createPropertyManager();
      createdUserEmails.add(userData.email);
      await userManagementPage.createUser(userData);
      
      // Test initial access
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      
      // Verify initial access
      await NavigationHelper.verifyAccessibleScreens(tester, userData.expectedScreens);
      
      // TODO: In a real implementation, we would test real-time permission updates
      // by having admin modify screen permissions while user is logged in
      // and verify that UI updates immediately without requiring re-login
    });

    testWidgets('Non-admin user cannot access permission configuration', (tester) async {
      // Login as property manager
      await AuthHelper.loginWithRole(tester, 'property_manager');
      
      // Verify permission configuration is not accessible
      await NavigationHelper.verifyRestrictedScreens(tester, ['permission_config']);
      
      // Try direct navigation (should fail gracefully)
      try {
        await NavigationHelper.navigateToPermissionConfig(tester);
        fail('Should not be able to navigate to permission configuration');
      } catch (e) {
        // Expected to fail
      }
    });

    testWidgets('Permission configuration supports complex widget layouts', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      // Create multiple widgets with different permissions
      final widgets = [
        WidgetDataFactory.createCustomWidget(
          name: 'admin_stats_widget',
          requiredPermissions: ['users.read', 'roles.read'],
          allowedRoles: ['admin'],
        ),
        WidgetDataFactory.createCustomWidget(
          name: 'property_stats_widget',
          requiredPermissions: ['properties.read'],
          allowedRoles: ['admin', 'property_manager'],
        ),
        WidgetDataFactory.createCustomWidget(
          name: 'maintenance_stats_widget',
          requiredPermissions: ['maintenance.read'],
          allowedRoles: ['admin', 'property_manager', 'maintenance_staff'],
        ),
      ];
      
      for (final widget in widgets) {
        createdWidgetNames.add(widget.name);
        await permissionConfigPage.configureWidgetPermissions(widget);
        await permissionConfigPage.verifyWidgetExists(widget.name);
      }
    });
  });
}
