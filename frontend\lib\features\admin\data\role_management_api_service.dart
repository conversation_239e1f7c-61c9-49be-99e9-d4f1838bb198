import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../shared/models/api_response.dart';

part 'role_management_api_service.g.dart';

@RestApi()
abstract class RoleManagementApiService {
  factory RoleManagementApiService(Dio dio) = _RoleManagementApiService;

  // Role Management
  @GET('/api/roles')
  Future<ApiResponse<List<Role>>> getRoles({
    @Query('include_permissions') bool? includePermissions,
    @Query('include_users') bool? includeUsers,
    @Query('system_roles') String? systemRoles,
  });

  @POST('/api/roles')
  Future<ApiResponse<Role>> createRole(@Body() CreateRoleRequest request);

  @GET('/api/roles/{id}')
  Future<ApiResponse<Role>> getRoleById(@Path('id') String id);

  @PUT('/api/roles/{id}')
  Future<ApiResponse<Role>> updateRole(
    @Path('id') String id,
    @Body() UpdateRoleRequest request,
  );

  @DELETE('/api/roles/{id}')
  Future<ApiResponse<VoidResponse>> deleteRole(@Path('id') String id);

  // Permission Management
  @GET('/api/permissions')
  Future<ApiResponse<PermissionResponse>> getPermissions({
    @Query('resource') String? resource,
    @Query('action') String? action,
    @Query('include_roles') bool? includeRoles,
  });

  @POST('/api/permissions')
  Future<ApiResponse<Permission>> createPermission(@Body() CreatePermissionRequest request);

  @DELETE('/api/permissions/{id}')
  Future<ApiResponse<VoidResponse>> deletePermission(@Path('id') String id);

  // User Role Assignment
  @GET('/api/users/{userId}/roles')
  Future<ApiResponse<UserRolesResponse>> getUserRoles(@Path('userId') String userId);

  @POST('/api/users/{userId}/roles')
  Future<ApiResponse<UserRolesResponse>> assignUserRoles(
    @Path('userId') String userId,
    @Body() AssignRolesRequest request,
  );

  @DELETE('/api/users/{userId}/roles')
  Future<ApiResponse<VoidResponse>> removeUserRole(
    @Path('userId') String userId,
    @Body() RemoveRoleRequest request,
  );
}

// Models
@JsonSerializable()
class Role {
  final String id;
  final String name;
  final String? description;
  @JsonKey(name: 'is_system_role')
  final bool isSystemRole;
  @JsonKey(name: 'is_active')
  final bool isActive;
  final List<Permission>? permissions;
  final List<RoleUser>? users;
  @JsonKey(name: 'user_count')
  final int? userCount;
  @JsonKey(name: 'permission_count')
  final int? permissionCount;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  const Role({
    required this.id,
    required this.name,
    this.description,
    required this.isSystemRole,
    this.isActive = true,
    this.permissions,
    this.users,
    this.userCount,
    this.permissionCount,
    required this.createdAt,
    this.updatedAt,
  });

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);
  Map<String, dynamic> toJson() => _$RoleToJson(this);
}

@JsonSerializable()
class Permission {
  final String id;
  final String name;
  final String? description;
  final String resource;
  final String action;
  final List<PermissionRole>? roles;
  @JsonKey(name: 'role_count')
  final int? roleCount;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  const Permission({
    required this.id,
    required this.name,
    this.description,
    required this.resource,
    required this.action,
    this.roles,
    this.roleCount,
    required this.createdAt,
  });

  factory Permission.fromJson(Map<String, dynamic> json) => _$PermissionFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionToJson(this);

  String get displayName => name.split('.').map((part) =>
    part[0].toUpperCase() + part.substring(1)).join(' ');
}

@JsonSerializable()
class RoleUser {
  final String id;
  final String email;
  @JsonKey(name: 'full_name')
  final String fullName;
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @JsonKey(name: 'assigned_at')
  final DateTime? assignedAt;

  const RoleUser({
    required this.id,
    required this.email,
    required this.fullName,
    this.isActive,
    this.assignedAt,
  });

  factory RoleUser.fromJson(Map<String, dynamic> json) => _$RoleUserFromJson(json);
  Map<String, dynamic> toJson() => _$RoleUserToJson(this);
}

@JsonSerializable()
class PermissionRole {
  final String id;
  final String name;
  final String? description;

  const PermissionRole({
    required this.id,
    required this.name,
    this.description,
  });

  factory PermissionRole.fromJson(Map<String, dynamic> json) => _$PermissionRoleFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionRoleToJson(this);
}

@JsonSerializable()
class PermissionResponse {
  final List<Permission> permissions;
  @JsonKey(name: 'grouped_permissions')
  final Map<String, List<Permission>> groupedPermissions;
  final int total;
  final List<String> resources;
  final List<String> actions;

  const PermissionResponse({
    required this.permissions,
    required this.groupedPermissions,
    required this.total,
    required this.resources,
    required this.actions,
  });

  factory PermissionResponse.fromJson(Map<String, dynamic> json) => _$PermissionResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionResponseToJson(this);
}

@JsonSerializable()
class UserRolesResponse {
  final UserInfo user;
  final List<ApiUserRole> roles;
  final List<UserPermission> permissions;
  @JsonKey(name: 'role_count')
  final int roleCount;
  @JsonKey(name: 'permission_count')
  final int permissionCount;

  const UserRolesResponse({
    required this.user,
    required this.roles,
    required this.permissions,
    required this.roleCount,
    required this.permissionCount,
  });

  factory UserRolesResponse.fromJson(Map<String, dynamic> json) => _$UserRolesResponseFromJson(json);
  Map<String, dynamic> toJson() => _$UserRolesResponseToJson(this);
}

@JsonSerializable()
class UserInfo {
  final String id;
  final String email;
  @JsonKey(name: 'full_name')
  final String fullName;
  @JsonKey(name: 'is_active')
  final bool isActive;

  const UserInfo({
    required this.id,
    required this.email,
    required this.fullName,
    required this.isActive,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);
  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}

@JsonSerializable()
class ApiUserRole {
  final String id;
  final String name;
  final String? description;
  @JsonKey(name: 'is_system_role')
  final bool isSystemRole;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'assigned_at')
  final DateTime assignedAt;
  @JsonKey(name: 'assigned_by')
  final String? assignedBy;
  final List<Permission> permissions;

  const ApiUserRole({
    required this.id,
    required this.name,
    this.description,
    required this.isSystemRole,
    this.isActive = true,
    required this.assignedAt,
    this.assignedBy,
    required this.permissions,
  });

  factory ApiUserRole.fromJson(Map<String, dynamic> json) => _$ApiUserRoleFromJson(json);
  Map<String, dynamic> toJson() => _$ApiUserRoleToJson(this);
}

@JsonSerializable()
class UserPermission {
  final String id;
  final String name;
  final String resource;
  final String action;
  @JsonKey(name: 'from_role')
  final String fromRole;

  const UserPermission({
    required this.id,
    required this.name,
    required this.resource,
    required this.action,
    required this.fromRole,
  });

  factory UserPermission.fromJson(Map<String, dynamic> json) => _$UserPermissionFromJson(json);
  Map<String, dynamic> toJson() => _$UserPermissionToJson(this);
}

// Request Models
@JsonSerializable()
class CreateRoleRequest {
  final String name;
  final String? description;
  @JsonKey(name: 'is_system_role')
  final bool? isSystemRole;
  final List<String>? permissions;

  const CreateRoleRequest({
    required this.name,
    this.description,
    this.isSystemRole,
    this.permissions,
  });

  Map<String, dynamic> toJson() => _$CreateRoleRequestToJson(this);
}

@JsonSerializable()
class UpdateRoleRequest {
  final String? name;
  final String? description;
  final List<String>? permissions;

  const UpdateRoleRequest({
    this.name,
    this.description,
    this.permissions,
  });

  Map<String, dynamic> toJson() => _$UpdateRoleRequestToJson(this);
}

@JsonSerializable()
class CreatePermissionRequest {
  final String name;
  final String? description;
  final String resource;
  final String action;

  const CreatePermissionRequest({
    required this.name,
    this.description,
    required this.resource,
    required this.action,
  });

  Map<String, dynamic> toJson() => _$CreatePermissionRequestToJson(this);
}

@JsonSerializable()
class AssignRolesRequest {
  @JsonKey(name: 'role_ids')
  final List<String> roleIds;
  @JsonKey(name: 'replace_existing')
  final bool replaceExisting;

  const AssignRolesRequest({
    required this.roleIds,
    this.replaceExisting = true,
  });

  Map<String, dynamic> toJson() => _$AssignRolesRequestToJson(this);
}

@JsonSerializable()
class RemoveRoleRequest {
  @JsonKey(name: 'role_id')
  final String roleId;

  const RemoveRoleRequest({
    required this.roleId,
  });

  Map<String, dynamic> toJson() => _$RemoveRoleRequestToJson(this);
}
