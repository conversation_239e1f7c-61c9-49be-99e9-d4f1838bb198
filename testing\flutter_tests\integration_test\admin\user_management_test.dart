import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../lib/helpers/auth_helper.dart';
import '../../lib/helpers/navigation_helper.dart';
import '../../lib/page_objects/user_management_page.dart';
import '../../lib/factories/user_data_factory.dart';
import '../../lib/models/user_data.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Admin User Management Tests', () {
    late UserManagementPage userManagementPage;
    final List<String> createdUserEmails = [];

    setUpAll(() async {
      // Setup test environment
      // This would initialize any required test configurations
    });

    setUp(() async {
      // Start fresh for each test
      // Note: In real implementation, this would start the actual app
      // For now, we'll assume the app is already running
    });

    tearDown(() async {
      // Cleanup created users after each test
      if (createdUserEmails.isNotEmpty) {
        try {
          await AuthHelper.loginAsAdmin(tester);
          await NavigationHelper.navigateToUserManagement(tester);
          userManagementPage = UserManagementPage(tester);
          
          for (final email in createdUserEmails) {
            try {
              await userManagementPage.deleteUser(email);
            } catch (e) {
              // User might already be deleted or not exist
              print('Failed to cleanup user $email: $e');
            }
          }
          createdUserEmails.clear();
        } catch (e) {
          print('Failed to cleanup users: $e');
        }
      }
    });

    testWidgets('Admin can create new property manager user', (tester) async {
      // Step 1: Login as admin
      await AuthHelper.loginAsAdmin(tester);
      
      // Step 2: Navigate to user management
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      // Step 3: Create test user data
      final userData = UserDataFactory.createPropertyManager();
      createdUserEmails.add(userData.email);
      
      // Step 4: Create user through UI
      await userManagementPage.createUser(userData);
      
      // Step 5: Verify user creation
      await userManagementPage.verifyUserExists(userData.email);
      
      // Step 6: Test new user login
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      
      // Step 7: Verify role permissions
      await NavigationHelper.verifyAccessibleScreens(tester, userData.expectedScreens);
      await NavigationHelper.verifyRestrictedScreens(tester, userData.restrictedScreens);
    });

    testWidgets('Admin can create new maintenance staff user', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final userData = UserDataFactory.createMaintenanceStaff();
      createdUserEmails.add(userData.email);
      
      await userManagementPage.createUser(userData);
      await userManagementPage.verifyUserExists(userData.email);
      
      // Test login and permissions
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      await NavigationHelper.verifyAccessibleScreens(tester, userData.expectedScreens);
      await NavigationHelper.verifyRestrictedScreens(tester, userData.restrictedScreens);
    });

    testWidgets('Admin can create new viewer user', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final userData = UserDataFactory.createViewer();
      createdUserEmails.add(userData.email);
      
      await userManagementPage.createUser(userData);
      await userManagementPage.verifyUserExists(userData.email);
      
      // Test login and permissions
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      await NavigationHelper.verifyAccessibleScreens(tester, userData.expectedScreens);
      await NavigationHelper.verifyRestrictedScreens(tester, userData.restrictedScreens);
    });

    testWidgets('User creation validates required fields', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      // Try to create user with empty form
      await tester.tap(userManagementPage.addUserFab);
      await tester.pumpAndSettle();
      
      await tester.tap(userManagementPage.createUserButton);
      await tester.pumpAndSettle();
      
      // Verify validation errors
      await userManagementPage.verifyValidationError('Full name is required');
      await userManagementPage.verifyValidationError('Email is required');
      await userManagementPage.verifyValidationError('Password is required');
    });

    testWidgets('User creation validates email format', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final invalidUserData = UserDataFactory.createWithInvalidEmail();
      
      await tester.tap(userManagementPage.addUserFab);
      await tester.pumpAndSettle();
      
      await tester.enterText(userManagementPage.fullNameField, invalidUserData.fullName);
      await tester.enterText(userManagementPage.emailField, invalidUserData.email);
      await tester.enterText(userManagementPage.passwordField, invalidUserData.password);
      await tester.enterText(userManagementPage.confirmPasswordField, invalidUserData.password);
      
      await tester.tap(userManagementPage.createUserButton);
      await tester.pumpAndSettle();
      
      await userManagementPage.verifyValidationError('Please enter a valid email');
    });

    testWidgets('User creation validates password strength', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final weakPasswordData = UserDataFactory.createWithWeakPassword();
      
      await tester.tap(userManagementPage.addUserFab);
      await tester.pumpAndSettle();
      
      await tester.enterText(userManagementPage.fullNameField, weakPasswordData.fullName);
      await tester.enterText(userManagementPage.emailField, weakPasswordData.email);
      await tester.enterText(userManagementPage.passwordField, weakPasswordData.password);
      await tester.enterText(userManagementPage.confirmPasswordField, weakPasswordData.password);
      
      await tester.tap(userManagementPage.createUserButton);
      await tester.pumpAndSettle();
      
      await userManagementPage.verifyValidationError('Password must be at least 8 characters');
    });

    testWidgets('User creation validates password confirmation', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      await tester.tap(userManagementPage.addUserFab);
      await tester.pumpAndSettle();
      
      await tester.enterText(userManagementPage.fullNameField, 'Test User');
      await tester.enterText(userManagementPage.emailField, '<EMAIL>');
      await tester.enterText(userManagementPage.passwordField, 'ValidPassword123!');
      await tester.enterText(userManagementPage.confirmPasswordField, 'DifferentPassword123!');
      
      await tester.tap(userManagementPage.createUserButton);
      await tester.pumpAndSettle();
      
      await userManagementPage.verifyValidationError('Passwords do not match');
    });

    testWidgets('Admin can edit existing user', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      // Create user first
      final originalUserData = UserDataFactory.createPropertyManager();
      createdUserEmails.add(originalUserData.email);
      await userManagementPage.createUser(originalUserData);
      
      // Edit user
      final updatedUserData = originalUserData.copyWith(
        fullName: 'Updated Property Manager',
        phone: '+1987654321',
      );
      
      await userManagementPage.editUser(originalUserData.email, updatedUserData);
      await userManagementPage.verifyUserExists(updatedUserData.email);
    });

    testWidgets('Admin can delete user', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      // Create user first
      final userData = UserDataFactory.createViewer();
      await userManagementPage.createUser(userData);
      
      // Delete user
      await userManagementPage.deleteUser(userData.email);
      
      // Verify user is deleted (no need to add to cleanup list)
      expect(find.text(userData.email), findsNothing);
    });

    testWidgets('Admin can search for users', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      // Create test user
      final userData = UserDataFactory.createPropertyManager();
      createdUserEmails.add(userData.email);
      await userManagementPage.createUser(userData);
      
      // Search for user
      await userManagementPage.searchUsers(userData.email);
      await userManagementPage.verifyUserExists(userData.email);
      
      // Clear search
      await userManagementPage.clearSearch();
    });

    testWidgets('Admin can assign multiple roles to user', (tester) async {
      await AuthHelper.loginAsAdmin(tester);
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      // Create user with multiple roles
      final userData = UserDataFactory.createCustomRole(
        roles: ['property_manager', 'maintenance_staff'],
        expectedScreens: ['dashboard', 'properties', 'maintenance'],
      );
      createdUserEmails.add(userData.email);
      
      await userManagementPage.createUser(userData);
      await userManagementPage.verifyUserExists(userData.email);
      
      // Test login and verify combined permissions
      await AuthHelper.logout(tester);
      await AuthHelper.loginWithUserData(tester, userData);
      await NavigationHelper.verifyAccessibleScreens(tester, userData.expectedScreens);
    });

    testWidgets('Non-admin user cannot access user management', (tester) async {
      // Login as property manager
      await AuthHelper.loginWithRole(tester, 'property_manager');
      
      // Verify user management is not accessible
      await NavigationHelper.verifyRestrictedScreens(tester, ['user_management']);
      
      // Try direct navigation (should fail gracefully)
      try {
        await NavigationHelper.navigateToUserManagement(tester);
        fail('Should not be able to navigate to user management');
      } catch (e) {
        // Expected to fail
      }
    });
  });
}
