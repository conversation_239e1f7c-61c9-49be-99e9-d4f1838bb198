import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../lib/helpers/auth_helper.dart';
import '../lib/helpers/navigation_helper.dart';
import '../lib/page_objects/dashboard_page.dart';
import '../lib/page_objects/properties_page.dart';
import '../lib/page_objects/maintenance_page.dart';
import '../lib/page_objects/attendance_page.dart';
import '../lib/page_objects/user_management_page.dart';
import '../lib/page_objects/role_management_page.dart';
import '../lib/page_objects/permission_config_page.dart';
import '../lib/factories/user_data_factory.dart';
import '../lib/models/user_data.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Complete SRSR App Screen Tests', () {
    late DashboardPage dashboardPage;
    late PropertiesPage propertiesPage;
    late MaintenancePage maintenancePage;
    late AttendancePage attendancePage;
    late UserManagementPage userManagementPage;
    late RoleManagementPage roleManagementPage;
    late PermissionConfigPage permissionConfigPage;

    setUpAll(() async {
      // Setup test environment
      print('🚀 Starting comprehensive SRSR app testing...');
    });

    setUp(() async {
      // Start fresh for each test
    });

    testWidgets('Complete admin workflow across all screens', (tester) async {
      print('🔧 Testing complete admin workflow...');
      
      // Step 1: Login as admin
      await AuthHelper.loginAsAdmin(tester);
      
      // Step 2: Test Dashboard
      print('📊 Testing Dashboard...');
      await NavigationHelper.navigateToDashboard(tester);
      dashboardPage = DashboardPage(tester);
      await dashboardPage.verifyDashboardLoaded();
      await dashboardPage.verifyAdminWidgetsVisible();
      
      // Step 3: Test Properties Management
      print('🏢 Testing Properties Management...');
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      final propertyData = PropertyDataFactory.createResidential();
      await propertiesPage.createProperty(propertyData);
      await propertiesPage.verifyPropertyExists(propertyData.name);
      
      // Step 4: Test Maintenance Management
      print('🔧 Testing Maintenance Management...');
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      final issueData = MaintenanceIssueDataFactory.createHighPriority();
      await maintenancePage.createMaintenanceIssue(issueData);
      await maintenancePage.verifyIssueExists(issueData.title);
      
      // Step 5: Test Attendance Management
      print('👥 Testing Attendance Management...');
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      final attendanceData = AttendanceDataFactory.createPresent();
      await attendancePage.createAttendanceRecord(attendanceData);
      await attendancePage.verifyAttendanceExists(attendanceData.employeeName);
      
      // Step 6: Test User Management
      print('👤 Testing User Management...');
      await NavigationHelper.navigateToUserManagement(tester);
      userManagementPage = UserManagementPage(tester);
      
      final userData = UserDataFactory.createPropertyManager();
      await userManagementPage.createUser(userData);
      await userManagementPage.verifyUserExists(userData.email);
      
      // Step 7: Test Role Management
      print('🎭 Testing Role Management...');
      await NavigationHelper.navigateToRoleManagement(tester);
      roleManagementPage = RoleManagementPage(tester);
      
      final roleData = RoleDataFactory.createSiteCoordinator();
      await roleManagementPage.createRole(roleData);
      await roleManagementPage.verifyRoleExists(roleData.name);
      
      // Step 8: Test Permission Configuration
      print('🔐 Testing Permission Configuration...');
      await NavigationHelper.navigateToPermissionConfig(tester);
      permissionConfigPage = PermissionConfigPage(tester);
      
      final screenData = ScreenDataFactory.createReportsScreen();
      await permissionConfigPage.configureScreenPermissions(screenData);
      await permissionConfigPage.verifyScreenExists(screenData.name);
      
      // Cleanup
      await propertiesPage.deleteProperty(propertyData.name);
      await maintenancePage.deleteIssue(issueData.title);
      await attendancePage.deleteAttendanceRecord(attendanceData.employeeName);
      await userManagementPage.deleteUser(userData.email);
      await roleManagementPage.deleteRole(roleData.name);
      await permissionConfigPage.deleteScreenConfig(screenData.name);
      
      print('✅ Complete admin workflow test passed!');
    });

    testWidgets('Property manager workflow across relevant screens', (tester) async {
      print('🏢 Testing property manager workflow...');
      
      // Step 1: Login as property manager
      await AuthHelper.loginWithRole(tester, 'property_manager');
      
      // Step 2: Test Dashboard access
      await NavigationHelper.navigateToDashboard(tester);
      dashboardPage = DashboardPage(tester);
      await dashboardPage.verifyDashboardLoaded();
      
      // Step 3: Test Properties access
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      await propertiesPage.navigateToProperties();
      
      // Step 4: Test Maintenance access
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      await maintenancePage.navigateToMaintenance();
      
      // Step 5: Test Attendance access
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      await attendancePage.navigateToAttendance();
      
      // Step 6: Verify restricted access to admin screens
      await NavigationHelper.verifyRestrictedScreens(tester, [
        'user_management',
        'role_management',
        'permission_config'
      ]);
      
      print('✅ Property manager workflow test passed!');
    });

    testWidgets('Maintenance staff workflow across relevant screens', (tester) async {
      print('🔧 Testing maintenance staff workflow...');
      
      // Step 1: Login as maintenance staff
      await AuthHelper.loginWithRole(tester, 'maintenance_staff');
      
      // Step 2: Test Dashboard access
      await NavigationHelper.navigateToDashboard(tester);
      dashboardPage = DashboardPage(tester);
      await dashboardPage.verifyDashboardLoaded();
      
      // Step 3: Test Maintenance access (primary screen)
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      await maintenancePage.navigateToMaintenance();
      
      // Step 4: Create and manage maintenance issue
      final issueData = MaintenanceIssueDataFactory.createMediumPriority();
      await maintenancePage.createMaintenanceIssue(issueData);
      await maintenancePage.updateIssueStatus(issueData.title, 'In Progress');
      await maintenancePage.addCommentToIssue(issueData.title, 'Working on this issue');
      
      // Step 5: Verify restricted access to other screens
      await NavigationHelper.verifyRestrictedScreens(tester, [
        'properties',
        'attendance',
        'user_management',
        'role_management',
        'permission_config'
      ]);
      
      // Cleanup
      await maintenancePage.deleteIssue(issueData.title);
      
      print('✅ Maintenance staff workflow test passed!');
    });

    testWidgets('Viewer workflow with read-only access', (tester) async {
      print('👁️ Testing viewer workflow...');
      
      // Step 1: Login as viewer
      await AuthHelper.loginWithRole(tester, 'viewer');
      
      // Step 2: Test Dashboard access (should be limited)
      await NavigationHelper.navigateToDashboard(tester);
      dashboardPage = DashboardPage(tester);
      await dashboardPage.verifyDashboardLoaded();
      
      // Step 3: Verify restricted access to most screens
      await NavigationHelper.verifyRestrictedScreens(tester, [
        'properties',
        'maintenance',
        'attendance',
        'user_management',
        'role_management',
        'permission_config'
      ]);
      
      print('✅ Viewer workflow test passed!');
    });

    testWidgets('Cross-screen navigation and data consistency', (tester) async {
      print('🔄 Testing cross-screen navigation and data consistency...');
      
      await AuthHelper.loginAsAdmin(tester);
      
      // Step 1: Create property
      await NavigationHelper.navigateToProperties(tester);
      propertiesPage = PropertiesPage(tester);
      
      final propertyData = PropertyDataFactory.createOffice();
      await propertiesPage.createProperty(propertyData);
      
      // Step 2: Create maintenance issue for the property
      await NavigationHelper.navigateToMaintenance(tester);
      maintenancePage = MaintenancePage(tester);
      
      final issueData = MaintenanceIssueDataFactory.createHighPriority();
      final updatedIssueData = issueData.copyWith(propertyName: propertyData.name);
      await maintenancePage.createMaintenanceIssue(updatedIssueData);
      
      // Step 3: Create attendance record for the property
      await NavigationHelper.navigateToAttendance(tester);
      attendancePage = AttendancePage(tester);
      
      final attendanceData = AttendanceDataFactory.createPresent();
      final updatedAttendanceData = attendanceData.copyWith(propertyName: propertyData.name);
      await attendancePage.createAttendanceRecord(updatedAttendanceData);
      
      // Step 4: Navigate back to property details and verify related data
      await NavigationHelper.navigateToProperties(tester);
      await propertiesPage.navigateToPropertyDetails(propertyData.name);
      
      // Step 5: Test navigation from property to related screens
      await propertiesPage.navigateToMaintenanceFromProperty();
      await propertiesPage.navigateToAttendanceFromProperty();
      
      // Cleanup
      await NavigationHelper.navigateToProperties(tester);
      await propertiesPage.deleteProperty(propertyData.name);
      
      await NavigationHelper.navigateToMaintenance(tester);
      await maintenancePage.deleteIssue(updatedIssueData.title);
      
      await NavigationHelper.navigateToAttendance(tester);
      await attendancePage.deleteAttendanceRecord(updatedAttendanceData.employeeName);
      
      print('✅ Cross-screen navigation test passed!');
    });

    testWidgets('Role-based UI rendering across all screens', (tester) async {
      print('🎭 Testing role-based UI rendering...');
      
      final roles = ['admin', 'property_manager', 'maintenance_staff', 'viewer'];
      
      for (final role in roles) {
        print('Testing role: $role');
        
        await AuthHelper.loginWithRole(tester, role);
        
        // Test Dashboard
        await NavigationHelper.navigateToDashboard(tester);
        dashboardPage = DashboardPage(tester);
        await dashboardPage.verifyDashboardLoaded();
        
        // Test role-specific widget visibility
        if (role == 'admin') {
          await dashboardPage.verifyAdminWidgetsVisible();
        } else if (role == 'property_manager') {
          await dashboardPage.verifyPropertyManagerWidgetsVisible();
        } else if (role == 'maintenance_staff') {
          await dashboardPage.verifyMaintenanceWidgetsVisible();
        } else {
          await dashboardPage.verifyViewerWidgetsVisible();
        }
        
        // Test screen accessibility based on role
        final accessibleScreens = _getAccessibleScreensForRole(role);
        await NavigationHelper.verifyAccessibleScreens(tester, accessibleScreens);
        
        final restrictedScreens = _getRestrictedScreensForRole(role);
        await NavigationHelper.verifyRestrictedScreens(tester, restrictedScreens);
        
        await AuthHelper.logout(tester);
      }
      
      print('✅ Role-based UI rendering test passed!');
    });

    testWidgets('Performance and responsiveness across screens', (tester) async {
      print('⚡ Testing performance and responsiveness...');
      
      await AuthHelper.loginAsAdmin(tester);
      
      final screens = [
        'dashboard',
        'properties',
        'maintenance',
        'attendance',
        'user_management',
        'role_management',
        'permission_config'
      ];
      
      for (final screen in screens) {
        print('Testing performance for: $screen');
        
        final stopwatch = Stopwatch()..start();
        
        switch (screen) {
          case 'dashboard':
            await NavigationHelper.navigateToDashboard(tester);
            break;
          case 'properties':
            await NavigationHelper.navigateToProperties(tester);
            break;
          case 'maintenance':
            await NavigationHelper.navigateToMaintenance(tester);
            break;
          case 'attendance':
            await NavigationHelper.navigateToAttendance(tester);
            break;
          case 'user_management':
            await NavigationHelper.navigateToUserManagement(tester);
            break;
          case 'role_management':
            await NavigationHelper.navigateToRoleManagement(tester);
            break;
          case 'permission_config':
            await NavigationHelper.navigateToPermissionConfig(tester);
            break;
        }
        
        stopwatch.stop();
        final loadTime = stopwatch.elapsedMilliseconds;
        
        print('$screen loaded in ${loadTime}ms');
        
        // Assert reasonable load time (adjust threshold as needed)
        expect(loadTime, lessThan(5000), reason: '$screen should load within 5 seconds');
      }
      
      print('✅ Performance test passed!');
    });
  });

  /// Get accessible screens for a given role
  List<String> _getAccessibleScreensForRole(String role) {
    switch (role) {
      case 'admin':
        return [
          'dashboard',
          'properties',
          'maintenance',
          'attendance',
          'user_management',
          'role_management',
          'permission_config'
        ];
      case 'property_manager':
        return ['dashboard', 'properties', 'maintenance', 'attendance'];
      case 'maintenance_staff':
        return ['dashboard', 'maintenance'];
      case 'viewer':
        return ['dashboard'];
      default:
        return ['dashboard'];
    }
  }

  /// Get restricted screens for a given role
  List<String> _getRestrictedScreensForRole(String role) {
    final allScreens = [
      'dashboard',
      'properties',
      'maintenance',
      'attendance',
      'user_management',
      'role_management',
      'permission_config'
    ];
    
    final accessibleScreens = _getAccessibleScreensForRole(role);
    return allScreens.where((screen) => !accessibleScreens.contains(screen)).toList();
  }
}
