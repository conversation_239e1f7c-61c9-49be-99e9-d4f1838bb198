import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../models/user_data.dart';

class RoleManagementPage {
  final WidgetTester tester;
  static final _logger = Logger();

  RoleManagementPage(this.tester);

  // Main screen locators
  Finder get screen => find.byKey(const Key('role_management_screen'));
  Finder get rolesTab => find.text('Roles');
  Finder get permissionsTab => find.text('Permissions');
  Finder get createRoleFab => find.byKey(const Key('create_role_fab'));
  Finder get rolesList => find.byKey(const Key('roles_list'));
  Finder get permissionsList => find.byKey(const Key('permissions_list'));
  Finder get searchField => find.byKey(const Key('search_field'));
  Finder get filterButton => find.byKey(const Key('filter_button'));

  // Create role dialog locators
  Finder get createRoleDialog => find.byKey(const Key('create_role_dialog'));
  Finder get roleNameField => find.byKey(const Key('role_name_field'));
  Finder get roleDescriptionField => find.byKey(const Key('role_description_field'));
  Finder get isSystemRoleCheckbox => find.byKey(const Key('is_system_role_checkbox'));
  Finder get createRoleButton => find.text('Create Role');
  Finder get cancelButton => find.text('Cancel');

  // Permission selection locators
  Finder get permissionSelectionSection => find.byKey(const Key('permission_selection_section'));
  Finder permissionCheckbox(String permission) => find.byKey(Key('permission_checkbox_$permission'));
  Finder permissionCategory(String category) => find.byKey(Key('permission_category_$category'));

  // Role actions locators
  Finder editRoleButton(String roleName) => find.byKey(Key('edit_role_$roleName'));
  Finder deleteRoleButton(String roleName) => find.byKey(Key('delete_role_$roleName'));
  Finder managePermissionsButton(String roleName) => find.byKey(Key('manage_permissions_$roleName'));
  Finder assignUsersButton(String roleName) => find.byKey(Key('assign_users_$roleName'));

  // Permission management locators
  Finder get permissionAssignmentDialog => find.byKey(const Key('permission_assignment_dialog'));
  Finder get updatePermissionsButton => find.text('Update Permissions');
  Finder get selectAllPermissionsButton => find.text('Select All');
  Finder get clearAllPermissionsButton => find.text('Clear All');

  // Verification locators
  Finder get successMessage => find.byKey(const Key('success_message'));
  Finder get errorMessage => find.byKey(const Key('error_message'));
  Finder roleCard(String roleName) => find.byKey(Key('role_card_$roleName'));

  /// Navigate to role management screen
  Future<void> navigateToRoleManagement() async {
    _logger.i('Navigating to role management screen');
    
    final adminMenu = find.byKey(const Key('admin_menu'));
    if (adminMenu.evaluate().isNotEmpty) {
      await tester.tap(adminMenu);
      await tester.pumpAndSettle();
    }
    
    final roleManagementItem = find.text('Role Management');
    if (roleManagementItem.evaluate().isNotEmpty) {
      await tester.tap(roleManagementItem);
      await tester.pumpAndSettle();
    }
    
    await _verifyScreenLoaded();
  }

  /// Switch to roles tab
  Future<void> switchToRolesTab() async {
    _logger.d('Switching to roles tab');
    
    if (rolesTab.evaluate().isNotEmpty) {
      await tester.tap(rolesTab);
      await tester.pumpAndSettle();
    }
  }

  /// Switch to permissions tab
  Future<void> switchToPermissionsTab() async {
    _logger.d('Switching to permissions tab');
    
    if (permissionsTab.evaluate().isNotEmpty) {
      await tester.tap(permissionsTab);
      await tester.pumpAndSettle();
    }
  }

  /// Create a new role
  Future<void> createRole(RoleData roleData) async {
    _logger.i('Creating role: ${roleData.name}');
    
    await switchToRolesTab();
    await _openCreateRoleDialog();
    await _fillRoleForm(roleData);
    await _selectPermissions(roleData.permissions);
    await _submitRoleForm();
    await _verifyRoleCreated(roleData.name);
  }

  /// Open create role dialog
  Future<void> _openCreateRoleDialog() async {
    _logger.d('Opening create role dialog');
    
    expect(createRoleFab, findsOneWidget, reason: 'Create role FAB should be present');
    await tester.tap(createRoleFab);
    await tester.pumpAndSettle();
    
    expect(createRoleDialog, findsOneWidget, reason: 'Create role dialog should open');
  }

  /// Fill role creation form
  Future<void> _fillRoleForm(RoleData roleData) async {
    _logger.d('Filling role form for: ${roleData.name}');
    
    // Fill required fields
    await tester.enterText(roleNameField, roleData.name);
    await tester.enterText(roleDescriptionField, roleData.description);
    
    // Set system role checkbox if needed
    if (roleData.isSystemRole) {
      final checkbox = isSystemRoleCheckbox;
      if (checkbox.evaluate().isNotEmpty) {
        await tester.tap(checkbox);
      }
    }
    
    await tester.pumpAndSettle();
  }

  /// Select permissions for role
  Future<void> _selectPermissions(List<String> permissions) async {
    _logger.d('Selecting permissions: $permissions');
    
    for (final permission in permissions) {
      final checkbox = permissionCheckbox(permission);
      if (checkbox.evaluate().isNotEmpty) {
        await tester.tap(checkbox);
        await tester.pumpAndSettle();
      } else {
        _logger.w('Permission checkbox not found: $permission');
      }
    }
  }

  /// Submit role creation form
  Future<void> _submitRoleForm() async {
    _logger.d('Submitting role form');
    
    expect(createRoleButton, findsOneWidget, reason: 'Create role button should be present');
    await tester.tap(createRoleButton);
    await tester.pumpAndSettle();
    
    // Wait for form processing
    await tester.pump(const Duration(seconds: 2));
    await tester.pumpAndSettle();
  }

  /// Verify role was created successfully
  Future<void> _verifyRoleCreated(String roleName) async {
    _logger.d('Verifying role created: $roleName');
    
    // Check for success message
    expect(successMessage, findsOneWidget, reason: 'Success message should appear');
    
    // Verify role appears in list
    await verifyRoleExists(roleName);
  }

  /// Verify role exists in the list
  Future<void> verifyRoleExists(String roleName) async {
    _logger.d('Verifying role exists: $roleName');
    
    await switchToRolesTab();
    
    // Search for role if search field is available
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, roleName);
      await tester.pumpAndSettle();
    }
    
    // Scroll to find role if needed
    await _scrollToFindRole(roleName);
    
    expect(find.text(roleName), findsOneWidget, reason: 'Role $roleName should be visible in list');
  }

  /// Scroll to find role in list
  Future<void> _scrollToFindRole(String roleName) async {
    final roleText = find.text(roleName);
    
    if (roleText.evaluate().isEmpty && rolesList.evaluate().isNotEmpty) {
      await tester.dragUntilVisible(
        roleText,
        rolesList,
        const Offset(0, -300),
        maxIteration: 10,
      );
    }
  }

  /// Edit existing role
  Future<void> editRole(String roleName, RoleData newData) async {
    _logger.i('Editing role: $roleName');
    
    await _findAndTapEditButton(roleName);
    await _fillRoleForm(newData);
    await _selectPermissions(newData.permissions);
    await _submitEditForm();
    await _verifyRoleUpdated(newData.name);
  }

  /// Find and tap edit button for role
  Future<void> _findAndTapEditButton(String roleName) async {
    await _scrollToFindRole(roleName);
    
    final editButton = editRoleButton(roleName);
    expect(editButton, findsOneWidget, reason: 'Edit button should be present for role $roleName');
    
    await tester.tap(editButton);
    await tester.pumpAndSettle();
  }

  /// Submit edit form
  Future<void> _submitEditForm() async {
    final saveButton = find.text('Save Changes');
    expect(saveButton, findsOneWidget, reason: 'Save changes button should be present');
    
    await tester.tap(saveButton);
    await tester.pumpAndSettle();
  }

  /// Verify role was updated
  Future<void> _verifyRoleUpdated(String roleName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after update');
    await verifyRoleExists(roleName);
  }

  /// Delete role
  Future<void> deleteRole(String roleName) async {
    _logger.i('Deleting role: $roleName');
    
    await _findAndTapDeleteButton(roleName);
    await _confirmDeletion();
    await _verifyRoleDeleted(roleName);
  }

  /// Find and tap delete button for role
  Future<void> _findAndTapDeleteButton(String roleName) async {
    await _scrollToFindRole(roleName);
    
    final deleteButton = deleteRoleButton(roleName);
    expect(deleteButton, findsOneWidget, reason: 'Delete button should be present for role $roleName');
    
    await tester.tap(deleteButton);
    await tester.pumpAndSettle();
  }

  /// Confirm deletion in dialog
  Future<void> _confirmDeletion() async {
    final confirmButton = find.text('Delete');
    expect(confirmButton, findsOneWidget, reason: 'Confirm delete button should be present');
    
    await tester.tap(confirmButton);
    await tester.pumpAndSettle();
  }

  /// Verify role was deleted
  Future<void> _verifyRoleDeleted(String roleName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after deletion');
    expect(find.text(roleName), findsNothing, reason: 'Role $roleName should no longer be visible');
  }

  /// Manage permissions for role
  Future<void> manageRolePermissions(String roleName, List<String> permissions) async {
    _logger.i('Managing permissions for role: $roleName');
    
    await _findAndTapManagePermissionsButton(roleName);
    await _updateRolePermissions(permissions);
    await _confirmPermissionUpdate();
    await _verifyPermissionsUpdated(roleName);
  }

  /// Find and tap manage permissions button
  Future<void> _findAndTapManagePermissionsButton(String roleName) async {
    await _scrollToFindRole(roleName);
    
    final manageButton = managePermissionsButton(roleName);
    expect(manageButton, findsOneWidget, reason: 'Manage permissions button should be present');
    
    await tester.tap(manageButton);
    await tester.pumpAndSettle();
    
    expect(permissionAssignmentDialog, findsOneWidget, reason: 'Permission assignment dialog should open');
  }

  /// Update role permissions
  Future<void> _updateRolePermissions(List<String> permissions) async {
    // Clear all existing permissions first
    if (clearAllPermissionsButton.evaluate().isNotEmpty) {
      await tester.tap(clearAllPermissionsButton);
      await tester.pumpAndSettle();
    }
    
    // Select new permissions
    await _selectPermissions(permissions);
  }

  /// Confirm permission update
  Future<void> _confirmPermissionUpdate() async {
    expect(updatePermissionsButton, findsOneWidget, reason: 'Update permissions button should be present');
    
    await tester.tap(updatePermissionsButton);
    await tester.pumpAndSettle();
  }

  /// Verify permissions were updated
  Future<void> _verifyPermissionsUpdated(String roleName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after permission update');
  }

  /// Search for roles
  Future<void> searchRoles(String query) async {
    _logger.d('Searching for roles: $query');
    
    await switchToRolesTab();
    
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, query);
      await tester.pumpAndSettle();
    }
  }

  /// Clear search
  Future<void> clearSearch() async {
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, '');
      await tester.pumpAndSettle();
    }
  }

  /// Filter roles by type
  Future<void> filterRolesByType(String type) async {
    _logger.d('Filtering roles by type: $type');
    
    if (filterButton.evaluate().isNotEmpty) {
      await tester.tap(filterButton);
      await tester.pumpAndSettle();
      
      final filterOption = find.text(type);
      if (filterOption.evaluate().isNotEmpty) {
        await tester.tap(filterOption);
        await tester.pumpAndSettle();
      }
    }
  }

  /// View all permissions
  Future<void> viewAllPermissions() async {
    _logger.d('Viewing all permissions');
    
    await switchToPermissionsTab();
    
    expect(permissionsList, findsOneWidget, reason: 'Permissions list should be visible');
  }

  /// Verify permission exists
  Future<void> verifyPermissionExists(String permissionName) async {
    await switchToPermissionsTab();
    
    expect(find.text(permissionName), findsOneWidget, 
           reason: 'Permission $permissionName should be visible');
  }

  /// Verify screen is loaded
  Future<void> _verifyScreenLoaded() async {
    expect(screen, findsOneWidget, reason: 'Role management screen should be loaded');
  }

  /// Get role count from UI
  Future<int> getRoleCount() async {
    await switchToRolesTab();
    
    final countText = find.byKey(const Key('role_count'));
    if (countText.evaluate().isNotEmpty) {
      // Parse count from text
      // Implementation would depend on UI format
    }
    return 0; // Placeholder
  }

  /// Verify form validation errors
  Future<void> verifyValidationError(String expectedError) async {
    expect(find.text(expectedError), findsOneWidget, 
           reason: 'Validation error should be displayed: $expectedError');
  }

  /// Cancel role creation
  Future<void> cancelRoleCreation() async {
    if (cancelButton.evaluate().isNotEmpty) {
      await tester.tap(cancelButton);
      await tester.pumpAndSettle();
    }
  }

  /// Select all permissions
  Future<void> selectAllPermissions() async {
    if (selectAllPermissionsButton.evaluate().isNotEmpty) {
      await tester.tap(selectAllPermissionsButton);
      await tester.pumpAndSettle();
    }
  }

  /// Clear all permissions
  Future<void> clearAllPermissions() async {
    if (clearAllPermissionsButton.evaluate().isNotEmpty) {
      await tester.tap(clearAllPermissionsButton);
      await tester.pumpAndSettle();
    }
  }

  /// Verify role has specific permissions
  Future<void> verifyRoleHasPermissions(String roleName, List<String> expectedPermissions) async {
    await _findAndTapManagePermissionsButton(roleName);
    
    for (final permission in expectedPermissions) {
      final checkbox = permissionCheckbox(permission);
      expect(checkbox, findsOneWidget, reason: 'Permission $permission should be selected');
      
      // Verify checkbox is checked
      final checkboxWidget = tester.widget<Checkbox>(checkbox);
      expect(checkboxWidget.value, isTrue, reason: 'Permission $permission should be checked');
    }
    
    // Close dialog
    await tester.tap(cancelButton);
    await tester.pumpAndSettle();
  }
}
