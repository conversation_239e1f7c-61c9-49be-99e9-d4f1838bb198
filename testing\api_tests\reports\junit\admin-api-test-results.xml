<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="26" failures="24" errors="0" time="10.317">
  <testsuite name="Admin User Management API Tests" errors="0" failures="24" skipped="0" timestamp="2025-06-01T04:42:11" time="9.91" tests="26">
    <testcase classname="Admin User Management API Tests User Creation should create admin user with valid data" name="Admin User Management API Tests User Creation should create admin user with valid data" time="0.07">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:17:26)
    at Object.createAdmin (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:41:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should create property manager user with valid data" name="Admin User Management API Tests User Creation should create property manager user with valid data" time="0.018">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:61:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should create maintenance staff user with valid data" name="Admin User Management API Tests User Creation should create maintenance staff user with valid data" time="0.024">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createMaintenanceStaff] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:51:26)
    at Object.createMaintenanceStaff (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:79:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should create viewer user with valid data" name="Admin User Management API Tests User Creation should create viewer user with valid data" time="0.017">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createViewer] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:85:26)
    at Object.createViewer (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:97:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should reject user with invalid email" name="Admin User Management API Tests User Creation should reject user with invalid email" time="2.973">
      <failure>Error: expect(received).toMatch(expected)

Expected pattern: /email/i
Received string:  &quot;Validation failed&quot;
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:126:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should reject user with weak password" name="Admin User Management API Tests User Creation should reject user with weak password" time="0.043">
      <failure>Error: expect(received).toMatch(expected)

Expected pattern: /password/i
Received string:  &quot;Validation failed&quot;
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:141:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should reject user with missing required fields" name="Admin User Management API Tests User Creation should reject user with missing required fields" time="0.04">
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should reject duplicate email" name="Admin User Management API Tests User Creation should reject duplicate email" time="0.009">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:160:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Creation should create user with multiple roles" name="Admin User Management API Tests User Creation should create user with multiple roles" time="0.009">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createWithCustomRoles] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:158:26)
    at Object.createWithCustomRoles (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:186:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should get all users" name="Admin User Management API Tests User Retrieval should get all users" time="0">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should get user by ID" name="Admin User Management API Tests User Retrieval should get user by ID" time="0">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should return 404 for non-existent user" name="Admin User Management API Tests User Retrieval should return 404 for non-existent user" time="0">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should filter users by role" name="Admin User Management API Tests User Retrieval should filter users by role" time="0">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Retrieval should search users by email" name="Admin User Management API Tests User Retrieval should search users by email" time="0.001">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Updates should update user details" name="Admin User Management API Tests User Updates should update user details" time="0.007">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:295:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Updates should activate/deactivate user" name="Admin User Management API Tests User Updates should activate/deactivate user" time="0.012">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:295:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Updates should reject update with invalid data" name="Admin User Management API Tests User Updates should reject update with invalid data" time="0.012">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:295:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Role Assignment should assign single role to user" name="Admin User Management API Tests Role Assignment should assign single role to user" time="0.001">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createForRoleAssignment] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:175:26)
    at Object.createForRoleAssignment (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:368:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Role Assignment should assign multiple roles to user" name="Admin User Management API Tests Role Assignment should assign multiple roles to user" time="0.001">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createForRoleAssignment] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:175:26)
    at Object.createForRoleAssignment (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:368:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Role Assignment should add role without replacing existing" name="Admin User Management API Tests Role Assignment should add role without replacing existing" time="0.001">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createForRoleAssignment] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:175:26)
    at Object.createForRoleAssignment (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:368:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Role Assignment should remove role from user" name="Admin User Management API Tests Role Assignment should remove role from user" time="0.001">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createForRoleAssignment] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:175:26)
    at Object.createForRoleAssignment (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:368:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Permission Enforcement should enforce user creation permissions" name="Admin User Management API Tests Permission Enforcement should enforce user creation permissions" time="0.164">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:494:27)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Permission Enforcement should enforce user update permissions" name="Admin User Management API Tests Permission Enforcement should enforce user update permissions" time="0.063">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:510:27)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests Permission Enforcement should enforce user deletion permissions" name="Admin User Management API Tests Permission Enforcement should enforce user deletion permissions" time="0.053">
      <failure>AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:525:27)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Deletion should delete user" name="Admin User Management API Tests User Deletion should delete user" time="0.006">
      <failure>TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createViewer] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:85:26)
    at Object.createViewer (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:541:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin User Management API Tests User Deletion should return 404 when deleting non-existent user" name="Admin User Management API Tests User Deletion should return 404 when deleting non-existent user" time="1.155">
    </testcase>
  </testsuite>
</testsuites>