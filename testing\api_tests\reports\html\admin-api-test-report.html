<html><head><meta charset="utf-8"/><title>SRSR Admin API Test Report</title><style type="text/css">html,
body {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1rem;
  margin: 0;
  padding: 0;
  color: #333;
}
body {
  padding: 2rem 1rem;
  font-size: 0.85rem;
}
.jesthtml-content {
  margin: 0 auto;
  max-width: 70rem;
}
header {
  display: flex;
  align-items: center;
}
#title {
  margin: 0;
  flex-grow: 1;
}
#logo {
  height: 4rem;
}
#timestamp {
  color: #777;
  margin-top: 0.5rem;
}

/** SUMMARY */
#summary {
  color: #333;
  margin: 2rem 0;
  display: flex;
  font-family: monospace;
  font-size: 1rem;
}
#summary > div {
  margin-right: 2rem;
  background: #eee;
  padding: 1rem;
  min-width: 15rem;
}
#summary > div:last-child {
  margin-right: 0;
}
@media only screen and (max-width: 720px) {
  #summary {
    flex-direction: column;
  }
  #summary > div {
    margin-right: 0;
    margin-top: 2rem;
  }
  #summary > div:first-child {
    margin-top: 0;
  }
}

.summary-total {
  font-weight: bold;
  margin-bottom: 0.5rem;
}
.summary-passed {
  color: #4f8a10;
  border-left: 0.4rem solid #4f8a10;
  padding-left: 0.5rem;
}
.summary-failed,
.summary-obsolete-snapshots {
  color: #d8000c;
  border-left: 0.4rem solid #d8000c;
  padding-left: 0.5rem;
}
.summary-pending {
  color: #9f6000;
  border-left: 0.4rem solid #9f6000;
  padding-left: 0.5rem;
}
.summary-empty {
  color: #999;
  border-left: 0.4rem solid #999;
}

.test-result {
  padding: 1rem;
  margin-bottom: 0.25rem;
}
.test-result:last-child {
  border: 0;
}
.test-result.passed {
  background-color: #dff2bf;
  color: #4f8a10;
}
.test-result.failed {
  background-color: #ffbaba;
  color: #d8000c;
}
.test-result.pending {
  background-color: #ffdf61;
  color: #9f6000;
}

.test-info {
  display: flex;
  justify-content: space-between;
}
.test-suitename {
  width: 20%;
  text-align: left;
  font-weight: bold;
  word-break: break-word;
}
.test-title {
  width: 40%;
  text-align: left;
  font-style: italic;
}
.test-status {
  width: 20%;
  text-align: right;
}
.test-duration {
  width: 10%;
  text-align: right;
  font-size: 0.75rem;
}

.failureMessages {
  padding: 0 1rem;
  margin-top: 1rem;
  border-top: 1px dashed #d8000c;
}
.failureMessages.suiteFailure {
  border-top: none;
}
.failureMsg {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

.suite-container {
  margin-bottom: 2rem;
}
.suite-container > input[type="checkbox"] {
  position: absolute;
  left: -100vw;
}
.suite-container label {
  display: block;
}
.suite-container .suite-tests {
  overflow-y: hidden;
  height: 0;
}
.suite-container > input[type="checkbox"]:checked ~ .suite-tests {
  height: auto;
  overflow: visible;
}
.suite-info {
  padding: 1rem;
  background-color: #eee;
  color: #777;
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}
.suite-info:hover {
  background-color: #ddd;
  cursor: pointer;
}
.suite-info .suite-path {
  word-break: break-all;
  flex-grow: 1;
  font-family: monospace;
  font-size: 1rem;
}
.suite-info .suite-time {
  margin-left: 0.5rem;
  padding: 0.2rem 0.3rem;
  font-size: 0.75rem;
}
.suite-info .suite-time.warn {
  background-color: #d8000c;
  color: #fff;
}
.suite-info:before {
  content: "\2303";
  display: inline-block;
  margin-right: 0.5rem;
  transform: rotate(0deg);
}
.suite-container > input[type="checkbox"]:checked ~ label .suite-info:before {
  transform: rotate(180deg);
}

/* CONSOLE LOGS */
.suite-consolelog {
  margin-bottom: 0.25rem;
  padding: 1rem;
  background-color: #efefef;
}
.suite-consolelog-header {
  font-weight: bold;
}
.suite-consolelog-item {
  padding: 0.5rem;
}
.suite-consolelog-item pre {
  margin: 0.5rem 0;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
.suite-consolelog-item-origin {
  color: #777;
  font-weight: bold;
}
.suite-consolelog-item-message {
  color: #000;
  font-size: 1rem;
  padding: 0 0.5rem;
}

/* OBSOLETE SNAPSHOTS */
.suite-obsolete-snapshots {
  margin-bottom: 0.25rem;
  padding: 1rem;
  background-color: #ffbaba;
  color: #d8000c;
}
.suite-obsolete-snapshots-header {
  font-weight: bold;
}
.suite-obsolete-snapshots-item {
  padding: 0.5rem;
}
.suite-obsolete-snapshots-item pre {
  margin: 0.5rem 0;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
.suite-obsolete-snapshots-item-message {
  color: #000;
  font-size: 1rem;
  padding: 0 0.5rem;
}
</style></head><body><div class="jesthtml-content"><header><h1 id="title">SRSR Admin API Test Report</h1></header><div id="metadata-container"><div id="timestamp">Started: 2025-06-01 10:12:11</div><div id="summary"><div id="suite-summary"><div class="summary-total">Suites (1)</div><div class="summary-passed  summary-empty">0 passed</div><div class="summary-failed ">1 failed</div><div class="summary-pending  summary-empty">0 pending</div></div><div id="test-summary"><div class="summary-total">Tests (26)</div><div class="summary-passed ">2 passed</div><div class="summary-failed ">24 failed</div><div class="summary-pending  summary-empty">0 pending</div></div></div></div><div id="suite-1" class="suite-container"><input id="collapsible-0" type="checkbox" class="toggle" checked="checked"/><label for="collapsible-0"><div class="suite-info"><div class="suite-path">D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js</div><div class="suite-time warn">9.91s</div></div></label><div class="suite-tests"><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create admin user with valid data</div><div class="test-status">failed</div><div class="test-duration">0.07s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:17:26)
    at Object.createAdmin (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:41:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create property manager user with valid data</div><div class="test-status">failed</div><div class="test-duration">0.018s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:61:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create maintenance staff user with valid data</div><div class="test-status">failed</div><div class="test-duration">0.024s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createMaintenanceStaff] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:51:26)
    at Object.createMaintenanceStaff (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:79:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create viewer user with valid data</div><div class="test-status">failed</div><div class="test-duration">0.017s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createViewer] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:85:26)
    at Object.createViewer (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:97:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should reject user with invalid email</div><div class="test-status">failed</div><div class="test-duration">2.973s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toMatch(expected)

Expected pattern: /email/i
Received string:  "Validation failed"
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:126:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should reject user with weak password</div><div class="test-status">failed</div><div class="test-duration">0.043s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toMatch(expected)

Expected pattern: /password/i
Received string:  "Validation failed"
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:141:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should reject user with missing required fields</div><div class="test-status">passed</div><div class="test-duration">0.04s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should reject duplicate email</div><div class="test-status">failed</div><div class="test-duration">0.009s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:160:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Creation</div><div class="test-title">should create user with multiple roles</div><div class="test-status">failed</div><div class="test-duration">0.009s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createWithCustomRoles] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:158:26)
    at Object.createWithCustomRoles (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:186:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should get all users</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should get user by ID</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should return 404 for non-existent user</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should filter users by role</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Retrieval</div><div class="test-title">should search users by email</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:209:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:95:7)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Updates</div><div class="test-title">should update user details</div><div class="test-status">failed</div><div class="test-duration">0.007s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:295:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Updates</div><div class="test-title">should activate/deactivate user</div><div class="test-status">failed</div><div class="test-duration">0.012s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:295:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Updates</div><div class="test-title">should reject update with invalid data</div><div class="test-status">failed</div><div class="test-duration">0.012s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createPropertyManager] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:34:26)
    at Object.createPropertyManager (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:295:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Role Assignment</div><div class="test-title">should assign single role to user</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createForRoleAssignment] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:175:26)
    at Object.createForRoleAssignment (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:368:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Role Assignment</div><div class="test-title">should assign multiple roles to user</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createForRoleAssignment] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:175:26)
    at Object.createForRoleAssignment (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:368:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Role Assignment</div><div class="test-title">should add role without replacing existing</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createForRoleAssignment] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:175:26)
    at Object.createForRoleAssignment (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:368:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Role Assignment</div><div class="test-title">should remove role from user</div><div class="test-status">failed</div><div class="test-duration">0.001s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createForRoleAssignment] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:175:26)
    at Object.createForRoleAssignment (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:368:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusHook (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:246:5)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce user creation permissions</div><div class="test-status">failed</div><div class="test-duration">0.164s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:494:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce user update permissions</div><div class="test-status">failed</div><div class="test-duration">0.063s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:510:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce user deletion permissions</div><div class="test-status">failed</div><div class="test-duration">0.053s</div></div><div class="failureMessages"> <pre class="failureMsg">AxiosError: Request failed with status code 401
    at settle (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\settle.js:19:12)
    at IncomingMessage.handleStreamEnd (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\adapters\http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AuthHelper.login (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:83:24)
    at AuthHelper.getViewerToken (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\helpers\auth_helper.js:69:19)
    at Object.&lt;anonymous&gt; (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:525:27)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Deletion</div><div class="test-title">should delete user</div><div class="test-status">failed</div><div class="test-duration">0.006s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: faker.phone.phoneNumber is not a function
    at Function.phoneNumber [as createViewer] (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\src\factories\user_factory.js:85:26)
    at Object.createViewer (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\user_management.test.js:541:36)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin User Management API Tests &gt; User Deletion</div><div class="test-title">should return 404 when deleting non-existent user</div><div class="test-status">passed</div><div class="test-duration">1.155s</div></div></div></div></div></div></body></html>