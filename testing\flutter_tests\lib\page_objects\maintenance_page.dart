import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../models/user_data.dart';

class MaintenancePage {
  final WidgetTester tester;
  static final _logger = Logger();

  MaintenancePage(this.tester);

  // Main screen locators
  Finder get screen => find.byKey(const Key('maintenance_screen'));
  Finder get issuesList => find.byKey(const Key('issues_list'));
  Finder get addIssueFab => find.byKey(const Key('add_issue_fab'));
  Finder get searchField => find.byKey(const Key('search_field'));
  Finder get refreshButton => find.byKey(const Key('refresh_button'));
  
  // Filter locators
  Finder get allFilterChip => find.byKey(const Key('filter_all'));
  Finder get pendingFilterChip => find.byKey(const Key('filter_pending'));
  Finder get inProgressFilterChip => find.byKey(const Key('filter_in_progress'));
  Finder get completedFilterChip => find.byKey(const Key('filter_completed'));
  Finder get urgentFilterChip => find.byKey(const Key('filter_urgent'));
  
  // Issue card locators
  Finder issueCard(String issueTitle) => find.byKey(Key('issue_card_$issueTitle'));
  Finder editIssueButton(String issueTitle) => find.byKey(Key('edit_issue_$issueTitle'));
  Finder deleteIssueButton(String issueTitle) => find.byKey(Key('delete_issue_$issueTitle'));
  Finder assignIssueButton(String issueTitle) => find.byKey(Key('assign_issue_$issueTitle'));
  Finder escalateIssueButton(String issueTitle) => find.byKey(Key('escalate_issue_$issueTitle'));
  
  // Add issue dialog locators
  Finder get addIssueDialog => find.byKey(const Key('add_issue_dialog'));
  Finder get issueTitleField => find.byKey(const Key('issue_title_field'));
  Finder get issueDescriptionField => find.byKey(const Key('issue_description_field'));
  Finder get issuePriorityDropdown => find.byKey(const Key('issue_priority_dropdown'));
  Finder get issueCategoryDropdown => find.byKey(const Key('issue_category_dropdown'));
  Finder get issuePropertyDropdown => find.byKey(const Key('issue_property_dropdown'));
  Finder get issueLocationField => find.byKey(const Key('issue_location_field'));
  Finder get saveIssueButton => find.text('Save Issue');
  Finder get cancelButton => find.text('Cancel');
  
  // Issue details locators
  Finder get issueDetailsScreen => find.byKey(const Key('issue_details_screen'));
  Finder get issueStatusDropdown => find.byKey(const Key('issue_status_dropdown'));
  Finder get addCommentButton => find.byKey(const Key('add_comment_button'));
  Finder get commentField => find.byKey(const Key('comment_field'));
  Finder get attachImageButton => find.byKey(const Key('attach_image_button'));
  
  // Assignment dialog locators
  Finder get assignmentDialog => find.byKey(const Key('assignment_dialog'));
  Finder get assigneeDropdown => find.byKey(const Key('assignee_dropdown'));
  Finder get assignButton => find.text('Assign');
  
  // Escalation dialog locators
  Finder get escalationDialog => find.byKey(const Key('escalation_dialog'));
  Finder get escalationReasonField => find.byKey(const Key('escalation_reason_field'));
  Finder get escalateButton => find.text('Escalate');
  
  // Verification locators
  Finder get successMessage => find.byKey(const Key('success_message'));
  Finder get errorMessage => find.byKey(const Key('error_message'));
  Finder get noIssuesMessage => find.text('No maintenance issues found');

  /// Navigate to maintenance screen
  Future<void> navigateToMaintenance() async {
    _logger.i('Navigating to maintenance screen');
    
    final maintenanceNavItem = find.text('Maintenance');
    if (maintenanceNavItem.evaluate().isNotEmpty) {
      await tester.tap(maintenanceNavItem);
      await tester.pumpAndSettle();
    }
    
    await _verifyScreenLoaded();
  }

  /// Create a new maintenance issue
  Future<void> createMaintenanceIssue(MaintenanceIssueData issueData) async {
    _logger.i('Creating maintenance issue: ${issueData.title}');
    
    await _openAddIssueDialog();
    await _fillIssueForm(issueData);
    await _submitIssueForm();
    await _verifyIssueCreated(issueData.title);
  }

  /// Open add issue dialog
  Future<void> _openAddIssueDialog() async {
    _logger.d('Opening add issue dialog');
    
    expect(addIssueFab, findsOneWidget, reason: 'Add issue FAB should be present');
    await tester.tap(addIssueFab);
    await tester.pumpAndSettle();
    
    expect(addIssueDialog, findsOneWidget, reason: 'Add issue dialog should open');
  }

  /// Fill issue creation form
  Future<void> _fillIssueForm(MaintenanceIssueData issueData) async {
    _logger.d('Filling issue form for: ${issueData.title}');
    
    await tester.enterText(issueTitleField, issueData.title);
    await tester.enterText(issueDescriptionField, issueData.description);
    await tester.enterText(issueLocationField, issueData.location);
    
    // Select priority
    await tester.tap(issuePriorityDropdown);
    await tester.pumpAndSettle();
    final priorityOption = find.text(issueData.priority);
    if (priorityOption.evaluate().isNotEmpty) {
      await tester.tap(priorityOption);
      await tester.pumpAndSettle();
    }
    
    // Select category
    await tester.tap(issueCategoryDropdown);
    await tester.pumpAndSettle();
    final categoryOption = find.text(issueData.category);
    if (categoryOption.evaluate().isNotEmpty) {
      await tester.tap(categoryOption);
      await tester.pumpAndSettle();
    }
    
    // Select property
    await tester.tap(issuePropertyDropdown);
    await tester.pumpAndSettle();
    final propertyOption = find.text(issueData.propertyName);
    if (propertyOption.evaluate().isNotEmpty) {
      await tester.tap(propertyOption);
      await tester.pumpAndSettle();
    }
    
    await tester.pumpAndSettle();
  }

  /// Submit issue creation form
  Future<void> _submitIssueForm() async {
    _logger.d('Submitting issue form');
    
    expect(saveIssueButton, findsOneWidget, reason: 'Save issue button should be present');
    await tester.tap(saveIssueButton);
    await tester.pumpAndSettle();
    
    // Wait for form processing
    await tester.pump(const Duration(seconds: 2));
    await tester.pumpAndSettle();
  }

  /// Verify issue was created successfully
  Future<void> _verifyIssueCreated(String issueTitle) async {
    _logger.d('Verifying issue created: $issueTitle');
    
    // Check for success message
    expect(successMessage, findsOneWidget, reason: 'Success message should appear');
    
    // Verify issue appears in list
    await verifyIssueExists(issueTitle);
  }

  /// Verify issue exists in the list
  Future<void> verifyIssueExists(String issueTitle) async {
    _logger.d('Verifying issue exists: $issueTitle');
    
    // Search for issue if search field is available
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, issueTitle);
      await tester.pumpAndSettle();
    }
    
    // Scroll to find issue if needed
    await _scrollToFindIssue(issueTitle);
    
    expect(find.text(issueTitle), findsOneWidget, reason: 'Issue $issueTitle should be visible in list');
  }

  /// Scroll to find issue in list
  Future<void> _scrollToFindIssue(String issueTitle) async {
    final issueText = find.text(issueTitle);
    
    if (issueText.evaluate().isEmpty && issuesList.evaluate().isNotEmpty) {
      await tester.dragUntilVisible(
        issueText,
        issuesList,
        const Offset(0, -300),
        maxIteration: 10,
      );
    }
  }

  /// Filter issues by status
  Future<void> filterIssuesByStatus(String status) async {
    _logger.d('Filtering issues by status: $status');
    
    Finder filterChip;
    switch (status.toLowerCase()) {
      case 'pending':
        filterChip = pendingFilterChip;
        break;
      case 'in_progress':
        filterChip = inProgressFilterChip;
        break;
      case 'completed':
        filterChip = completedFilterChip;
        break;
      case 'urgent':
        filterChip = urgentFilterChip;
        break;
      default:
        filterChip = allFilterChip;
    }
    
    if (filterChip.evaluate().isNotEmpty) {
      await tester.tap(filterChip);
      await tester.pumpAndSettle();
    }
  }

  /// Search for issues
  Future<void> searchIssues(String query) async {
    _logger.d('Searching for issues: $query');
    
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, query);
      await tester.pumpAndSettle();
    }
  }

  /// Clear search
  Future<void> clearSearch() async {
    if (searchField.evaluate().isNotEmpty) {
      await tester.enterText(searchField, '');
      await tester.pumpAndSettle();
    }
  }

  /// Navigate to issue details
  Future<void> navigateToIssueDetails(String issueTitle) async {
    _logger.i('Navigating to issue details: $issueTitle');
    
    await _scrollToFindIssue(issueTitle);
    
    final issueCardFinder = issueCard(issueTitle);
    expect(issueCardFinder, findsOneWidget, reason: 'Issue card should be present');
    
    await tester.tap(issueCardFinder);
    await tester.pumpAndSettle();
    
    expect(issueDetailsScreen, findsOneWidget, reason: 'Issue details screen should load');
  }

  /// Update issue status
  Future<void> updateIssueStatus(String issueTitle, String newStatus) async {
    _logger.i('Updating issue status: $issueTitle to $newStatus');
    
    await navigateToIssueDetails(issueTitle);
    
    await tester.tap(issueStatusDropdown);
    await tester.pumpAndSettle();
    
    final statusOption = find.text(newStatus);
    if (statusOption.evaluate().isNotEmpty) {
      await tester.tap(statusOption);
      await tester.pumpAndSettle();
    }
    
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after status update');
  }

  /// Assign issue to user
  Future<void> assignIssue(String issueTitle, String assigneeName) async {
    _logger.i('Assigning issue: $issueTitle to $assigneeName');
    
    await _scrollToFindIssue(issueTitle);
    
    final assignButton = assignIssueButton(issueTitle);
    expect(assignButton, findsOneWidget, reason: 'Assign button should be present');
    
    await tester.tap(assignButton);
    await tester.pumpAndSettle();
    
    expect(assignmentDialog, findsOneWidget, reason: 'Assignment dialog should open');
    
    await tester.tap(assigneeDropdown);
    await tester.pumpAndSettle();
    
    final assigneeOption = find.text(assigneeName);
    if (assigneeOption.evaluate().isNotEmpty) {
      await tester.tap(assigneeOption);
      await tester.pumpAndSettle();
    }
    
    await tester.tap(assignButton);
    await tester.pumpAndSettle();
    
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after assignment');
  }

  /// Escalate issue
  Future<void> escalateIssue(String issueTitle, String reason) async {
    _logger.i('Escalating issue: $issueTitle');
    
    await _scrollToFindIssue(issueTitle);
    
    final escalateButton = escalateIssueButton(issueTitle);
    expect(escalateButton, findsOneWidget, reason: 'Escalate button should be present');
    
    await tester.tap(escalateButton);
    await tester.pumpAndSettle();
    
    expect(escalationDialog, findsOneWidget, reason: 'Escalation dialog should open');
    
    await tester.enterText(escalationReasonField, reason);
    await tester.pumpAndSettle();
    
    await tester.tap(escalateButton);
    await tester.pumpAndSettle();
    
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after escalation');
  }

  /// Add comment to issue
  Future<void> addCommentToIssue(String issueTitle, String comment) async {
    _logger.i('Adding comment to issue: $issueTitle');
    
    await navigateToIssueDetails(issueTitle);
    
    await tester.tap(addCommentButton);
    await tester.pumpAndSettle();
    
    await tester.enterText(commentField, comment);
    await tester.pumpAndSettle();
    
    final submitCommentButton = find.text('Add Comment');
    if (submitCommentButton.evaluate().isNotEmpty) {
      await tester.tap(submitCommentButton);
      await tester.pumpAndSettle();
    }
    
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after adding comment');
  }

  /// Edit existing issue
  Future<void> editIssue(String issueTitle, MaintenanceIssueData newData) async {
    _logger.i('Editing issue: $issueTitle');
    
    await _findAndTapEditButton(issueTitle);
    await _fillIssueForm(newData);
    await _submitEditForm();
    await _verifyIssueUpdated(newData.title);
  }

  /// Find and tap edit button for issue
  Future<void> _findAndTapEditButton(String issueTitle) async {
    await _scrollToFindIssue(issueTitle);
    
    final editButton = editIssueButton(issueTitle);
    expect(editButton, findsOneWidget, reason: 'Edit button should be present for issue $issueTitle');
    
    await tester.tap(editButton);
    await tester.pumpAndSettle();
  }

  /// Submit edit form
  Future<void> _submitEditForm() async {
    final updateButton = find.text('Update Issue');
    expect(updateButton, findsOneWidget, reason: 'Update issue button should be present');
    
    await tester.tap(updateButton);
    await tester.pumpAndSettle();
  }

  /// Verify issue was updated
  Future<void> _verifyIssueUpdated(String issueTitle) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after update');
    await verifyIssueExists(issueTitle);
  }

  /// Delete issue
  Future<void> deleteIssue(String issueTitle) async {
    _logger.i('Deleting issue: $issueTitle');
    
    await _findAndTapDeleteButton(issueTitle);
    await _confirmDeletion();
    await _verifyIssueDeleted(issueTitle);
  }

  /// Find and tap delete button for issue
  Future<void> _findAndTapDeleteButton(String issueTitle) async {
    await _scrollToFindIssue(issueTitle);
    
    final deleteButton = deleteIssueButton(issueTitle);
    expect(deleteButton, findsOneWidget, reason: 'Delete button should be present for issue $issueTitle');
    
    await tester.tap(deleteButton);
    await tester.pumpAndSettle();
  }

  /// Confirm deletion in dialog
  Future<void> _confirmDeletion() async {
    final confirmButton = find.text('Delete');
    expect(confirmButton, findsOneWidget, reason: 'Confirm delete button should be present');
    
    await tester.tap(confirmButton);
    await tester.pumpAndSettle();
  }

  /// Verify issue was deleted
  Future<void> _verifyIssueDeleted(String issueTitle) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after deletion');
    expect(find.text(issueTitle), findsNothing, reason: 'Issue $issueTitle should no longer be visible');
  }

  /// Refresh issues list
  Future<void> refreshIssues() async {
    _logger.d('Refreshing issues list');
    
    if (refreshButton.evaluate().isNotEmpty) {
      await tester.tap(refreshButton);
      await tester.pumpAndSettle();
    }
  }

  /// Verify screen is loaded
  Future<void> _verifyScreenLoaded() async {
    expect(screen, findsOneWidget, reason: 'Maintenance screen should be loaded');
  }

  /// Verify form validation errors
  Future<void> verifyValidationError(String expectedError) async {
    expect(find.text(expectedError), findsOneWidget, 
           reason: 'Validation error should be displayed: $expectedError');
  }

  /// Cancel issue creation
  Future<void> cancelIssueCreation() async {
    if (cancelButton.evaluate().isNotEmpty) {
      await tester.tap(cancelButton);
      await tester.pumpAndSettle();
    }
  }

  /// Verify no issues message
  Future<void> verifyNoIssuesMessage() async {
    expect(noIssuesMessage, findsOneWidget, reason: 'No issues message should be displayed');
  }
}
