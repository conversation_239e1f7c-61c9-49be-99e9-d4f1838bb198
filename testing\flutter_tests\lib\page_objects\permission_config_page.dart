import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../models/user_data.dart';

class PermissionConfigPage {
  final WidgetTester tester;
  static final _logger = Logger();

  PermissionConfigPage(this.tester);

  // Main screen locators
  Finder get screen => find.byKey(const Key('permission_config_screen'));
  Finder get screenPermissionsTab => find.text('Screen Permissions');
  Finder get widgetPermissionsTab => find.text('Widget Permissions');
  Finder get rolePermissionsTab => find.text('Role Permissions');
  
  // Screen permissions locators
  Finder get screensList => find.byKey(const Key('screens_list'));
  Finder get addScreenButton => find.byKey(const Key('add_screen_button'));
  Finder screenCard(String screenName) => find.byKey(Key('screen_card_$screenName'));
  Finder editScreenButton(String screenName) => find.byKey(Key('edit_screen_$screenName'));
  Finder deleteScreenButton(String screenName) => find.byKey(Key('delete_screen_$screenName'));
  
  // Widget permissions locators
  Finder get widgetsList => find.byKey(const Key('widgets_list'));
  Finder get addWidgetButton => find.byKey(const Key('add_widget_button'));
  Finder widgetCard(String widgetName) => find.byKey(Key('widget_card_$widgetName'));
  Finder editWidgetButton(String widgetName) => find.byKey(Key('edit_widget_$widgetName'));
  Finder deleteWidgetButton(String widgetName) => find.byKey(Key('delete_widget_$widgetName'));
  
  // Screen configuration dialog locators
  Finder get screenConfigDialog => find.byKey(const Key('screen_config_dialog'));
  Finder get screenNameField => find.byKey(const Key('screen_name_field'));
  Finder get screenTitleField => find.byKey(const Key('screen_title_field'));
  Finder get screenRouteField => find.byKey(const Key('screen_route_field'));
  Finder get screenDescriptionField => find.byKey(const Key('screen_description_field'));
  Finder get screenIconField => find.byKey(const Key('screen_icon_field'));
  Finder get screenEnabledCheckbox => find.byKey(const Key('screen_enabled_checkbox'));
  Finder get showInNavigationCheckbox => find.byKey(const Key('show_in_navigation_checkbox'));
  Finder get screenPriorityField => find.byKey(const Key('screen_priority_field'));
  
  // Widget configuration dialog locators
  Finder get widgetConfigDialog => find.byKey(const Key('widget_config_dialog'));
  Finder get widgetNameField => find.byKey(const Key('widget_name_field'));
  Finder get widgetTitleField => find.byKey(const Key('widget_title_field'));
  Finder get widgetTypeDropdown => find.byKey(const Key('widget_type_dropdown'));
  Finder get widgetScreenDropdown => find.byKey(const Key('widget_screen_dropdown'));
  Finder get widgetDescriptionField => find.byKey(const Key('widget_description_field'));
  Finder get widgetVisibleCheckbox => find.byKey(const Key('widget_visible_checkbox'));
  Finder get widgetEnabledCheckbox => find.byKey(const Key('widget_enabled_checkbox'));
  
  // Permission assignment locators
  Finder get permissionAssignmentSection => find.byKey(const Key('permission_assignment_section'));
  Finder get roleAssignmentSection => find.byKey(const Key('role_assignment_section'));
  Finder get requireAllPermissionsCheckbox => find.byKey(const Key('require_all_permissions_checkbox'));
  Finder permissionCheckbox(String permission) => find.byKey(Key('permission_checkbox_$permission'));
  Finder roleCheckbox(String role) => find.byKey(Key('role_checkbox_$role'));
  
  // Action buttons
  Finder get saveConfigButton => find.text('Save Configuration');
  Finder get updateConfigButton => find.text('Update Configuration');
  Finder get cancelButton => find.text('Cancel');
  Finder get deleteButton => find.text('Delete');
  
  // Verification locators
  Finder get successMessage => find.byKey(const Key('success_message'));
  Finder get errorMessage => find.byKey(const Key('error_message'));

  /// Navigate to permission configuration screen
  Future<void> navigateToPermissionConfig() async {
    _logger.i('Navigating to permission configuration screen');
    
    final adminMenu = find.byKey(const Key('admin_menu'));
    if (adminMenu.evaluate().isNotEmpty) {
      await tester.tap(adminMenu);
      await tester.pumpAndSettle();
    }
    
    final permissionConfigItem = find.text('Permission Config');
    if (permissionConfigItem.evaluate().isNotEmpty) {
      await tester.tap(permissionConfigItem);
      await tester.pumpAndSettle();
    }
    
    await _verifyScreenLoaded();
  }

  /// Switch to screen permissions tab
  Future<void> switchToScreenPermissionsTab() async {
    _logger.d('Switching to screen permissions tab');
    
    if (screenPermissionsTab.evaluate().isNotEmpty) {
      await tester.tap(screenPermissionsTab);
      await tester.pumpAndSettle();
    }
  }

  /// Switch to widget permissions tab
  Future<void> switchToWidgetPermissionsTab() async {
    _logger.d('Switching to widget permissions tab');
    
    if (widgetPermissionsTab.evaluate().isNotEmpty) {
      await tester.tap(widgetPermissionsTab);
      await tester.pumpAndSettle();
    }
  }

  /// Switch to role permissions tab
  Future<void> switchToRolePermissionsTab() async {
    _logger.d('Switching to role permissions tab');
    
    if (rolePermissionsTab.evaluate().isNotEmpty) {
      await tester.tap(rolePermissionsTab);
      await tester.pumpAndSettle();
    }
  }

  /// Configure screen permissions
  Future<void> configureScreenPermissions(ScreenData screenData) async {
    _logger.i('Configuring screen permissions: ${screenData.name}');
    
    await switchToScreenPermissionsTab();
    await _openAddScreenDialog();
    await _fillScreenConfigForm(screenData);
    await _assignScreenPermissions(screenData.requiredPermissions);
    await _assignScreenRoles(screenData.allowedRoles);
    await _submitScreenConfig();
    await _verifyScreenConfigured(screenData.name);
  }

  /// Open add screen dialog
  Future<void> _openAddScreenDialog() async {
    _logger.d('Opening add screen dialog');
    
    expect(addScreenButton, findsOneWidget, reason: 'Add screen button should be present');
    await tester.tap(addScreenButton);
    await tester.pumpAndSettle();
    
    expect(screenConfigDialog, findsOneWidget, reason: 'Screen config dialog should open');
  }

  /// Fill screen configuration form
  Future<void> _fillScreenConfigForm(ScreenData screenData) async {
    _logger.d('Filling screen config form for: ${screenData.name}');
    
    await tester.enterText(screenNameField, screenData.name);
    await tester.enterText(screenTitleField, screenData.title);
    await tester.enterText(screenRouteField, screenData.route);
    await tester.enterText(screenDescriptionField, screenData.description);
    await tester.enterText(screenIconField, screenData.icon);
    await tester.enterText(screenPriorityField, screenData.priority.toString());
    
    // Set checkboxes
    if (screenData.isEnabled) {
      final enabledCheckbox = screenEnabledCheckbox;
      if (enabledCheckbox.evaluate().isNotEmpty) {
        await tester.tap(enabledCheckbox);
      }
    }
    
    if (screenData.showInNavigation) {
      final navCheckbox = showInNavigationCheckbox;
      if (navCheckbox.evaluate().isNotEmpty) {
        await tester.tap(navCheckbox);
      }
    }
    
    await tester.pumpAndSettle();
  }

  /// Assign permissions to screen
  Future<void> _assignScreenPermissions(List<String> permissions) async {
    _logger.d('Assigning screen permissions: $permissions');
    
    for (final permission in permissions) {
      final checkbox = permissionCheckbox(permission);
      if (checkbox.evaluate().isNotEmpty) {
        await tester.tap(checkbox);
        await tester.pumpAndSettle();
      } else {
        _logger.w('Permission checkbox not found: $permission');
      }
    }
  }

  /// Assign roles to screen
  Future<void> _assignScreenRoles(List<String> roles) async {
    _logger.d('Assigning screen roles: $roles');
    
    for (final role in roles) {
      final checkbox = roleCheckbox(role);
      if (checkbox.evaluate().isNotEmpty) {
        await tester.tap(checkbox);
        await tester.pumpAndSettle();
      } else {
        _logger.w('Role checkbox not found: $role');
      }
    }
  }

  /// Submit screen configuration
  Future<void> _submitScreenConfig() async {
    _logger.d('Submitting screen configuration');
    
    expect(saveConfigButton, findsOneWidget, reason: 'Save config button should be present');
    await tester.tap(saveConfigButton);
    await tester.pumpAndSettle();
    
    // Wait for processing
    await tester.pump(const Duration(seconds: 2));
    await tester.pumpAndSettle();
  }

  /// Verify screen was configured
  Future<void> _verifyScreenConfigured(String screenName) async {
    _logger.d('Verifying screen configured: $screenName');
    
    expect(successMessage, findsOneWidget, reason: 'Success message should appear');
    await verifyScreenExists(screenName);
  }

  /// Configure widget permissions
  Future<void> configureWidgetPermissions(WidgetData widgetData) async {
    _logger.i('Configuring widget permissions: ${widgetData.name}');
    
    await switchToWidgetPermissionsTab();
    await _openAddWidgetDialog();
    await _fillWidgetConfigForm(widgetData);
    await _assignWidgetPermissions(widgetData.requiredPermissions);
    await _assignWidgetRoles(widgetData.allowedRoles);
    await _submitWidgetConfig();
    await _verifyWidgetConfigured(widgetData.name);
  }

  /// Open add widget dialog
  Future<void> _openAddWidgetDialog() async {
    _logger.d('Opening add widget dialog');
    
    expect(addWidgetButton, findsOneWidget, reason: 'Add widget button should be present');
    await tester.tap(addWidgetButton);
    await tester.pumpAndSettle();
    
    expect(widgetConfigDialog, findsOneWidget, reason: 'Widget config dialog should open');
  }

  /// Fill widget configuration form
  Future<void> _fillWidgetConfigForm(WidgetData widgetData) async {
    _logger.d('Filling widget config form for: ${widgetData.name}');
    
    await tester.enterText(widgetNameField, widgetData.name);
    await tester.enterText(widgetTitleField, widgetData.title);
    await tester.enterText(widgetDescriptionField, widgetData.description);
    
    // Select widget type
    await tester.tap(widgetTypeDropdown);
    await tester.pumpAndSettle();
    final typeOption = find.text(widgetData.type);
    if (typeOption.evaluate().isNotEmpty) {
      await tester.tap(typeOption);
      await tester.pumpAndSettle();
    }
    
    // Select screen
    await tester.tap(widgetScreenDropdown);
    await tester.pumpAndSettle();
    final screenOption = find.text(widgetData.screen);
    if (screenOption.evaluate().isNotEmpty) {
      await tester.tap(screenOption);
      await tester.pumpAndSettle();
    }
    
    // Set checkboxes
    if (widgetData.isVisible) {
      final visibleCheckbox = widgetVisibleCheckbox;
      if (visibleCheckbox.evaluate().isNotEmpty) {
        await tester.tap(visibleCheckbox);
      }
    }
    
    if (widgetData.isEnabled) {
      final enabledCheckbox = widgetEnabledCheckbox;
      if (enabledCheckbox.evaluate().isNotEmpty) {
        await tester.tap(enabledCheckbox);
      }
    }
    
    await tester.pumpAndSettle();
  }

  /// Assign permissions to widget
  Future<void> _assignWidgetPermissions(List<String> permissions) async {
    _logger.d('Assigning widget permissions: $permissions');
    
    for (final permission in permissions) {
      final checkbox = permissionCheckbox(permission);
      if (checkbox.evaluate().isNotEmpty) {
        await tester.tap(checkbox);
        await tester.pumpAndSettle();
      } else {
        _logger.w('Permission checkbox not found: $permission');
      }
    }
  }

  /// Assign roles to widget
  Future<void> _assignWidgetRoles(List<String> roles) async {
    _logger.d('Assigning widget roles: $roles');
    
    for (final role in roles) {
      final checkbox = roleCheckbox(role);
      if (checkbox.evaluate().isNotEmpty) {
        await tester.tap(checkbox);
        await tester.pumpAndSettle();
      } else {
        _logger.w('Role checkbox not found: $role');
      }
    }
  }

  /// Submit widget configuration
  Future<void> _submitWidgetConfig() async {
    _logger.d('Submitting widget configuration');
    
    expect(saveConfigButton, findsOneWidget, reason: 'Save config button should be present');
    await tester.tap(saveConfigButton);
    await tester.pumpAndSettle();
    
    // Wait for processing
    await tester.pump(const Duration(seconds: 2));
    await tester.pumpAndSettle();
  }

  /// Verify widget was configured
  Future<void> _verifyWidgetConfigured(String widgetName) async {
    _logger.d('Verifying widget configured: $widgetName');
    
    expect(successMessage, findsOneWidget, reason: 'Success message should appear');
    await verifyWidgetExists(widgetName);
  }

  /// Edit screen permissions
  Future<void> editScreenPermissions(String screenName, ScreenData newData) async {
    _logger.i('Editing screen permissions: $screenName');
    
    await switchToScreenPermissionsTab();
    await _findAndTapEditScreenButton(screenName);
    await _fillScreenConfigForm(newData);
    await _assignScreenPermissions(newData.requiredPermissions);
    await _assignScreenRoles(newData.allowedRoles);
    await _submitScreenUpdate();
    await _verifyScreenUpdated(newData.name);
  }

  /// Find and tap edit screen button
  Future<void> _findAndTapEditScreenButton(String screenName) async {
    await _scrollToFindScreen(screenName);
    
    final editButton = editScreenButton(screenName);
    expect(editButton, findsOneWidget, reason: 'Edit button should be present for screen $screenName');
    
    await tester.tap(editButton);
    await tester.pumpAndSettle();
  }

  /// Submit screen update
  Future<void> _submitScreenUpdate() async {
    expect(updateConfigButton, findsOneWidget, reason: 'Update config button should be present');
    
    await tester.tap(updateConfigButton);
    await tester.pumpAndSettle();
  }

  /// Verify screen was updated
  Future<void> _verifyScreenUpdated(String screenName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after update');
    await verifyScreenExists(screenName);
  }

  /// Delete screen configuration
  Future<void> deleteScreenConfig(String screenName) async {
    _logger.i('Deleting screen configuration: $screenName');
    
    await switchToScreenPermissionsTab();
    await _findAndTapDeleteScreenButton(screenName);
    await _confirmDeletion();
    await _verifyScreenDeleted(screenName);
  }

  /// Find and tap delete screen button
  Future<void> _findAndTapDeleteScreenButton(String screenName) async {
    await _scrollToFindScreen(screenName);
    
    final deleteButton = deleteScreenButton(screenName);
    expect(deleteButton, findsOneWidget, reason: 'Delete button should be present for screen $screenName');
    
    await tester.tap(deleteButton);
    await tester.pumpAndSettle();
  }

  /// Confirm deletion
  Future<void> _confirmDeletion() async {
    expect(deleteButton, findsOneWidget, reason: 'Confirm delete button should be present');
    
    await tester.tap(deleteButton);
    await tester.pumpAndSettle();
  }

  /// Verify screen was deleted
  Future<void> _verifyScreenDeleted(String screenName) async {
    expect(successMessage, findsOneWidget, reason: 'Success message should appear after deletion');
    expect(find.text(screenName), findsNothing, reason: 'Screen $screenName should no longer be visible');
  }

  /// Verify screen exists
  Future<void> verifyScreenExists(String screenName) async {
    _logger.d('Verifying screen exists: $screenName');
    
    await switchToScreenPermissionsTab();
    await _scrollToFindScreen(screenName);
    
    expect(find.text(screenName), findsOneWidget, reason: 'Screen $screenName should be visible');
  }

  /// Verify widget exists
  Future<void> verifyWidgetExists(String widgetName) async {
    _logger.d('Verifying widget exists: $widgetName');
    
    await switchToWidgetPermissionsTab();
    await _scrollToFindWidget(widgetName);
    
    expect(find.text(widgetName), findsOneWidget, reason: 'Widget $widgetName should be visible');
  }

  /// Scroll to find screen
  Future<void> _scrollToFindScreen(String screenName) async {
    final screenText = find.text(screenName);
    
    if (screenText.evaluate().isEmpty && screensList.evaluate().isNotEmpty) {
      await tester.dragUntilVisible(
        screenText,
        screensList,
        const Offset(0, -300),
        maxIteration: 10,
      );
    }
  }

  /// Scroll to find widget
  Future<void> _scrollToFindWidget(String widgetName) async {
    final widgetText = find.text(widgetName);
    
    if (widgetText.evaluate().isEmpty && widgetsList.evaluate().isNotEmpty) {
      await tester.dragUntilVisible(
        widgetText,
        widgetsList,
        const Offset(0, -300),
        maxIteration: 10,
      );
    }
  }

  /// Verify screen is loaded
  Future<void> _verifyScreenLoaded() async {
    expect(screen, findsOneWidget, reason: 'Permission config screen should be loaded');
  }

  /// Verify form validation errors
  Future<void> verifyValidationError(String expectedError) async {
    expect(find.text(expectedError), findsOneWidget, 
           reason: 'Validation error should be displayed: $expectedError');
  }

  /// Cancel configuration
  Future<void> cancelConfiguration() async {
    if (cancelButton.evaluate().isNotEmpty) {
      await tester.tap(cancelButton);
      await tester.pumpAndSettle();
    }
  }

  /// Set require all permissions option
  Future<void> setRequireAllPermissions(bool require) async {
    final checkbox = requireAllPermissionsCheckbox;
    if (checkbox.evaluate().isNotEmpty) {
      final checkboxWidget = tester.widget<Checkbox>(checkbox);
      if (checkboxWidget.value != require) {
        await tester.tap(checkbox);
        await tester.pumpAndSettle();
      }
    }
  }
}
